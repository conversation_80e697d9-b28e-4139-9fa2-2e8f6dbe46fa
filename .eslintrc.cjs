module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  settings: {
    'import/resolver': {
      typescript: {
        directory: './tsconfig.json',
      },
    },
  },
  overrides: [],
  parser: 'vue-eslint-parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    parser: '@typescript-eslint/parser',
  },
  extends: [
    'eslint:recommended',
    'plugin:vue/vue3-essential',
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
    '@unocss',
  ],
  plugins: [
    'vue',
    '@typescript-eslint',
    'unused-imports',
    'simple-import-sort',
    'prettier',
  ],
  rules: {
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'vue/multi-word-component-names': 'off',
    'vue/no-textarea-mustache': 'off',
    'prettier/prettier': [
      'error',
      {
        semi: false,
        singleQuote: true,
        endOfLine: 'auto',
      },
    ],
    '@typescript-eslint/no-explicit-any': 'off',
    'react/no-unescaped-entities': 0,
    'no-unused-vars': 'off', // or "@typescript-eslint/no-unused-vars": "off",
    'unused-imports/no-unused-imports': 'error',
    'unused-imports/no-unused-vars': [
      'warn',
      {
        vars: 'all',
        varsIgnorePattern: '^_',
        args: 'after-used',
        argsIgnorePattern: '^_',
      },
    ],
    eqeqeq: 2,
    'simple-import-sort/imports': [
      'error',
      {
        groups: [
          [
            `^vue$`,
            `^vue-router$`,
            `^dayjs$`,
            `^moment$`,
            `^lodash$`,
            `^element-plus$`,
          ],
          [`.*\\.vue$`], // .vue 文件
          [`^@/api/*`],
          [`^@/common/*`, `.*/config/.*`],
          [`^../*`],
          [`.*/assets/.*`, `^@/assets$`],
          [`.*/hooks/.*`, `^@/hooks$`],
          [`.*/router/.*`, `^@/router$`],
          [`.*/store/.*`, `^@/store$`],
          [`.*/utils/.*`, `^@/utils$`],
          [`^`],
          [`^type `],
        ],
      },
    ],
    'simple-import-sort/exports': 'error',
  },
}
