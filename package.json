{"name": "yc-annotation-fe", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host", "serve": "vite preview", "build-dev": "vite build --mode development", "build": "vite build", "build:prod": "vite build", "git": "tive git -c tive.git.config.cjs", "lint": "eslint --ext .jsx,.ts,.tsx,.vue,.cjs --quiet ./", "lint:stylelint": "stylelint --cache --fix \"src/**/*.{less,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "prepare": "husky install"}, "dependencies": {"@crazydos/vue-markdown": "^1.1.4", "@element-plus/icons-vue": "^2.0.9", "@micro-zoe/micro-app": "1.0.0-rc.3", "aplayer": "^1.10.1", "axios": "^1.6.8", "clipboard": "^2.0.11", "core-js": "^3.25.2", "dayjs": "^1.11.5", "echarts": "^5.5.1", "element-plus": "^2.3.5", "hls.js": "^1.5.7", "js-beautify": "^1.14.7", "lodash-es": "^4.17.21", "pinia": "^2.0.35", "vue": "^3.2.47", "vue-router": "^4.2.1", "web-highlighter": "^0.7.4"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "@iconify-json/ep": "^1.1.10", "@moefe/vue-aplayer": "^2.0.0-beta.5", "@rushstack/eslint-patch": "^1.2.0", "@types/babel__core": "^7.20.1", "@types/core-js": "^2.5.5", "@types/eslint": "^8.40.0", "@types/eslint-config-prettier": "^6.11.0", "@types/eslint-plugin-prettier": "^3.1.0", "@types/fabric": "^5.3.2", "@types/js-beautify": "^1.13.3", "@types/less": "^3.0.3", "@types/lodash": "^4.14.195", "@types/lodash-es": "^4.17.12", "@types/node": "^20.1.4", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "@unocss/eslint-config": "^0.51.13", "@unocss/eslint-plugin": "^0.51.13", "@unocss/postcss": "^0.58.0", "@vitejs/plugin-vue": "^4.1.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.1", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-unused-imports": "latest", "eslint-plugin-vue": "^9.11.1", "husky": "^8.0.3", "less": "^4.1.1", "less-loader": "^6.0.0", "lint-staged": "^13.1.2", "lodash": "^4.17.21", "moment": "^2.29.4", "picocolors": "^1.0.0", "postcss": "^8.4.23", "postcss-html": "^1.5.0", "prettier": "^2.8.8", "sass": "^1.62.1", "stylelint": "^15.2.0", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended-scss": "^11.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^24.0.0", "stylelint-config-standard-scss": "^3.0.0", "stylelint-order": "^5.0.0", "stylelint-prettier": "^3.0.0", "stylelint-scss": "^4.1.0", "terser": "^5.17.5", "typescript": "^4.8.3", "typesync": "^0.11.0", "unocss": "^0.51.13", "unplugin-auto-import": "^0.11.2", "unplugin-icons": "^0.16.1", "unplugin-vue-components": "^0.22.4", "vite": "^4.1.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-insert-html": "^1.0.2", "vite-plugin-inspect": "^0.7.26", "vue-tsc": "^1.0.24"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "__npminstall_done": false}