/*! web-highlighter v0.7.5 https://github.com/alienzhou/web-highlighter */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.Highlighter=t():e.<PERSON>lighter=t()}(this,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=7)}([function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.eventEmitter=t.INTERNAL_ERROR_EVENT=t.UNKNOWN_IDX=t.ROOT_IDX=t.getStylesheet=t.getDefaultOptions=t.CAMEL_DATASET_SPLIT_TYPE=t.CAMEL_DATASET_IDENTIFIER_EXTRA=t.CAMEL_DATASET_IDENTIFIER=t.DATASET_SPLIT_TYPE=t.DATASET_IDENTIFIER_EXTRA=t.DATASET_IDENTIFIER=t.STYLESHEET_ID=t.LOCAL_STORE_KEY=t.ID_DIVISION=void 0;var a=i(n(10)),l=i(n(2));t.ID_DIVISION=";",t.LOCAL_STORE_KEY="highlight-mengshou",t.STYLESHEET_ID="highlight-mengshou-style",t.DATASET_IDENTIFIER="highlight-id",t.DATASET_IDENTIFIER_EXTRA="highlight-id-extra",t.DATASET_SPLIT_TYPE="highlight-split-type",t.CAMEL_DATASET_IDENTIFIER=(0,a.default)(t.DATASET_IDENTIFIER),t.CAMEL_DATASET_IDENTIFIER_EXTRA=(0,a.default)(t.DATASET_IDENTIFIER_EXTRA),t.CAMEL_DATASET_SPLIT_TYPE=(0,a.default)(t.DATASET_SPLIT_TYPE);t.getDefaultOptions=function(){return{$root:document||document.documentElement,rootDocument:document,exceptSelectors:null,wrapTag:"span",verbose:!1,style:{className:"highlight-wrap"},window:window}};t.getStylesheet=function(){return"\n    .".concat((0,t.getDefaultOptions)().style.className," {\n    }\n    .").concat((0,t.getDefaultOptions)().style.className,".active {\n        background: #ffb;\n    }\n")},t.ROOT_IDX=-2,t.UNKNOWN_IDX=-1,t.INTERNAL_ERROR_EVENT="error";var s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t}(l.default);t.eventEmitter=new s},function(e,t,n){"use strict";var r,o,i,a,l,s;Object.defineProperty(t,"__esModule",{value:!0}),t.UserInputEvent=t.SelectedNodeType=t.CreateFrom=t.EventType=t.ERROR=t.SplitType=void 0,function(e){e.none="none",e.head="head",e.tail="tail",e.both="both"}(r||(t.SplitType=r={})),function(e){e.DOM_TYPE_ERROR="[DOM] Receive wrong node type.",e.DOM_SELECTION_EMPTY="[DOM] The selection contains no dom node, may be you except them.",e.RANGE_INVALID="[RANGE] Got invalid dom range, can't convert to a valid highlight range.",e.RANGE_NODE_INVALID="[RANGE] Start or end node isn't a text node, it may occur an error.",e.DB_ID_DUPLICATE_ERROR="[STORE] Unique id conflict.",e.CACHE_SET_ERROR="[CACHE] Cache.data can't be set manually, please use .save().",e.SOURCE_TYPE_ERROR="[SOURCE] Object isn't a highlight source instance.",e.HIGHLIGHT_RANGE_FROZEN="[HIGHLIGHT_RANGE] A highlight range must be frozen before render.",e.HIGHLIGHT_SOURCE_RECREATE="[HIGHLIGHT_SOURCE] Recreate highlights from sources error.",e.HIGHLIGHT_SOURCE_NONE_RENDER="[HIGHLIGHT_SOURCE] This highlight source isn't rendered. May be the exception skips it or the dom structure has changed."}(o||(t.ERROR=o={})),function(e){e.CREATE="selection:create",e.REMOVE="selection:remove",e.MODIFY="selection:modify",e.HOVER="selection:hover",e.HOVER_OUT="selection:hover-out",e.CLICK="selection:click"}(i||(t.EventType=i={})),function(e){e.STORE="from-store",e.INPUT="from-input"}(a||(t.CreateFrom=a={})),function(e){e.text="text",e.span="span"}(l||(t.SelectedNodeType=l={})),function(e){e.touchend="touchend",e.mouseup="mouseup",e.touchstart="touchstart",e.click="click",e.mouseover="mouseover"}(s||(t.UserInputEvent=s={}))},function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},o=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(){this.handlersMap=Object.create(null)}return e.prototype.on=function(e,t){return this.handlersMap[e]||(this.handlersMap[e]=[]),this.handlersMap[e].push(t),this},e.prototype.off=function(e,t){return this.handlersMap[e]&&this.handlersMap[e].splice(this.handlersMap[e].indexOf(t)>>>0,1),this},e.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return this.handlersMap[e]&&this.handlersMap[e].slice().forEach((function(e){e.apply(void 0,o([],r(t),!1))})),this},e}();t.default=i},function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=r(n(5)),i=n(9),a=function(){function e(e,t,n,r,o){this.startMeta=e,this.endMeta=t,this.text=n,this.id=r,this.__isHighlightSource={},o&&(this.extra=o)}return e.prototype.deSerialize=function(e,t){var n=(0,i.queryElementNode)(this,e),r=n.start,a=n.end,l=(0,i.getTextChildByOffset)(r,this.startMeta.textOffset),s=(0,i.getTextChildByOffset)(a,this.endMeta.textOffset);if(!t.Serialize.Restore.isEmpty()){var u=t.Serialize.Restore.call(this,l,s)||[];l=u[0]||l,s=u[1]||s}return new o.default(l,s,this.text,this.id,!0)},e}();t.default=a},function(e,t,n){"use strict";var r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},o=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},i=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.hasClass=t.removeAllClass=t.removeClass=t.addClass=t.addEventListener=t.removeEventListener=t.forEach=t.getHighlightById=t.getHighlightsByRoot=t.getExtraHighlightId=t.getHighlightId=t.isHighlightWrapNode=void 0;var a=n(0);t.isHighlightWrapNode=function(e){return!!e.dataset&&!!e.dataset[a.CAMEL_DATASET_IDENTIFIER]};var l=function(e,n){for(var r=!1,o=null;e;){if((0,t.isHighlightWrapNode)(e)&&(o=e),e===n){r=!0;break}e=e.parentNode}return r?o:null};t.getHighlightId=function(e,t){return(e=l(e,t))?e.dataset[a.CAMEL_DATASET_IDENTIFIER]:""};t.getExtraHighlightId=function(e,t){return(e=l(e,t))?e.dataset[a.CAMEL_DATASET_IDENTIFIER_EXTRA].split(a.ID_DIVISION).filter((function(e){return e})):[]};t.getHighlightsByRoot=function(e,t){var n,o;Array.isArray(e)||(e=[e]);var i=[];try{for(var l=r(e),s=l.next();!s.done;s=l.next()){var u=s.value.querySelectorAll("".concat(t,"[data-").concat(a.DATASET_IDENTIFIER,"]"));i.push.apply(i,u)}}catch(e){n={error:e}}finally{try{s&&!s.done&&(o=l.return)&&o.call(l)}finally{if(n)throw n.error}}return i};t.getHighlightById=function(e,t,n){var o,i,l=[],s=new RegExp("(".concat(t,"\\").concat(a.ID_DIVISION,"|\\").concat(a.ID_DIVISION,"?").concat(t,"$)")),u=e.querySelectorAll("".concat(n,"[data-").concat(a.DATASET_IDENTIFIER,"]"));try{for(var c=r(u),d=c.next();!d.done;d=c.next()){var f=d.value;if(f.dataset[a.CAMEL_DATASET_IDENTIFIER]!==t){var h=f.dataset[a.CAMEL_DATASET_IDENTIFIER_EXTRA];s.test(h)&&l.push(f)}else l.push(f)}}catch(e){o={error:e}}finally{try{d&&!d.done&&(i=c.return)&&i.call(c)}finally{if(o)throw o.error}}return l};t.forEach=function(e,t){for(var n=0;n<e.length;n++)t(e[n],n,e)};t.removeEventListener=function(e,t,n){e.removeEventListener(t,n)};t.addEventListener=function(e,n,r){return e.addEventListener(n,r),function(){(0,t.removeEventListener)(e,n,r)}};t.addClass=function(e,t){var n;Array.isArray(t)||(t=[t]),(n=e.classList).add.apply(n,i([],o(t),!1))};t.removeClass=function(e,t){e.classList.remove(t)};t.removeAllClass=function(e){e.className=""};t.hasClass=function(e,t){return e.classList.contains(t)}},function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=r(n(3)),i=n(11),a=r(n(6)),l=n(12),s=function(){function e(e,t,n,r,o){void 0===o&&(o=!1),this.start=(0,l.formatDomNode)(e),this.end=(0,l.formatDomNode)(t),this.text=n,this.frozen=o,this.id=r}return e.fromSelection=function(t,n){var r=(0,i.getDomRange)(n);if(!r)return null;var o={$node:r.startContainer,offset:r.startOffset},l={$node:r.endContainer,offset:r.endOffset},s=r.toString(),u=t.call(o,l,s);return new e(o,l,s,u=null!=u?u:(0,a.default)())},e.prototype.serialize=function(e,t){var n,r=(0,l.getDomMeta)(this.start.$node,this.start.offset,e),i=(0,l.getDomMeta)(this.end.$node,this.end.offset,e);return t.Serialize.RecordInfo.isEmpty()||(n=t.Serialize.RecordInfo.call(this.start,this.end,e)),this.frozen=!0,new o.default(r,i,this.text,this.id,n)},e.removeDomRange=i.removeSelection,e}();t.default=s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function e(t){return t?(t^16*Math.random()>>t/4).toString(16):([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,e)}},function(e,t,n){e.exports=n(8)},function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var l=a(n(2)),s=a(n(5)),u=a(n(3)),c=a(n(6)),d=a(n(13)),f=a(n(14)),h=a(n(16)),p=a(n(17)),E=n(0),v=n(1),_=n(4),y=function(e){function t(t){var n=e.call(this)||this;n.run=function(){return(0,_.addEventListener)(n.options.$root,n.event.PointerEnd,n._handleSelection)},n.stop=function(){(0,_.removeEventListener)(n.options.$root,n.event.PointerEnd,n._handleSelection)},n.addClass=function(e,t){n.getDoms(t).forEach((function(t){(0,_.addClass)(t,e)}))},n.removeClass=function(e,t){n.getDoms(t).forEach((function(t){(0,_.removeClass)(t,e)}))},n.getIdByDom=function(e){return(0,_.getHighlightId)(e,n.options.$root)},n.getExtraIdByDom=function(e){return(0,_.getExtraHighlightId)(e,n.options.$root)},n.getDoms=function(e){return e?(0,_.getHighlightById)(n.options.$root,e,n.options.wrapTag):(0,_.getHighlightsByRoot)(n.options.$root,n.options.wrapTag)},n.dispose=function(){var e=n.options.$root;(0,_.removeEventListener)(e,n.event.PointerOver,n._handleHighlightHover),(0,_.removeEventListener)(e,n.event.PointerEnd,n._handleSelection),(0,_.removeEventListener)(e,n.event.PointerTap,n._handleHighlightClick),n.removeAll()},n.setOption=function(e){n.options=i(i({},n.options),e),n.painter=new p.default({$root:n.options.$root,rootDocument:n.options.rootDocument,wrapTag:n.options.wrapTag,className:n.options.style.className,exceptSelectors:n.options.exceptSelectors,window:n.options.window},n.hooks)},n.fromRange=function(e){var t={$node:e.startContainer,offset:e.startOffset},r={$node:e.endContainer,offset:e.endOffset},o=e.toString(),i=n.hooks.Render.UUID.call(t,r,o);i=null!=i?i:(0,c.default)();var a=new s.default(t,r,o,i);return a?n._highlightFromHRange(a):(E.eventEmitter.emit(E.INTERNAL_ERROR_EVENT,{type:v.ERROR.RANGE_INVALID}),null)},n.fromStore=function(e,t,r,o,i){var a=new u.default(e,t,r,o,i);try{return n._highlightFromHSource(a),a}catch(e){return E.eventEmitter.emit(E.INTERNAL_ERROR_EVENT,{type:v.ERROR.HIGHLIGHT_SOURCE_RECREATE,error:e,detail:a}),null}},n._getHooks=function(){return{Render:{UUID:new d.default("Render.UUID"),SelectedNodes:new d.default("Render.SelectedNodes"),WrapNode:new d.default("Render.WrapNode")},Serialize:{Restore:new d.default("Serialize.Restore"),RecordInfo:new d.default("Serialize.RecordInfo")},Remove:{UpdateNodes:new d.default("Remove.UpdateNodes")}}},n._highlightFromHRange=function(e){var t=e.serialize(n.options.$root,n.hooks);return 0===n.painter.highlightRange(e).length?(E.eventEmitter.emit(E.INTERNAL_ERROR_EVENT,{type:v.ERROR.DOM_SELECTION_EMPTY}),null):(n.cache.save(t),n.emit(v.EventType.CREATE,{sources:[t],type:v.CreateFrom.INPUT},n),t)},n._handleSelection=function(){var e=s.default.fromSelection(n.hooks.Render.UUID,n.options.rootDocument);e&&(n._highlightFromHRange(e),s.default.removeDomRange(n.options.rootDocument))},n._handleHighlightHover=function(e){var t=e.target;if(!(0,_.isHighlightWrapNode)(t))return n._hoverId&&n.emit(v.EventType.HOVER_OUT,{id:n._hoverId},n,e),void(n._hoverId=null);var r=(0,_.getHighlightId)(t,n.options.$root);n._hoverId!==r&&(n._hoverId&&n.emit(v.EventType.HOVER_OUT,{id:n._hoverId},n,e),n._hoverId=r,n.emit(v.EventType.HOVER,{id:n._hoverId},n,e))},n._handleError=function(e){n.options.verbose&&console.warn(e)},n._handleHighlightClick=function(e){var t=e.target;if((0,_.isHighlightWrapNode)(t)){var r=(0,_.getHighlightId)(t,n.options.$root);n.emit(v.EventType.CLICK,{id:r},n,e)}},n.options=(0,E.getDefaultOptions)(),n.hooks=n._getHooks(),n.setOption(t),n.event=(0,f.default)(n.options.window),n.cache=new h.default;var r=n.options.$root;return(0,_.addEventListener)(r,n.event.PointerOver,n._handleHighlightHover),(0,_.addEventListener)(r,n.event.PointerTap,n._handleHighlightClick),E.eventEmitter.on(E.INTERNAL_ERROR_EVENT,n._handleError),n}return o(t,e),t.prototype.remove=function(e){if(e){var t=this.painter.removeHighlight(e);this.cache.remove(e),t&&this.emit(v.EventType.REMOVE,{ids:[e]},this)}},t.prototype.removeAll=function(){this.painter.removeAllHighlight();var e=this.cache.removeAll();this.emit(v.EventType.REMOVE,{ids:e},this)},t.prototype._highlightFromHSource=function(e){void 0===e&&(e=[]);var t=this.painter.highlightSource(e);this.emit(v.EventType.CREATE,{sources:t,type:v.CreateFrom.STORE},this),this.cache.save(e)},t.event=v.EventType,t.isHighlightWrapNode=_.isHighlightWrapNode,t.isHighlightSource=function(e){return!!e.__isHighlightSource},t}(l.default);t.default=y},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.queryElementNode=t.getTextChildByOffset=void 0;var r=n(0);t.getTextChildByOffset=function(e,t){for(var n=[e],r=null,o=0,i=0;r=n.pop();){for(var a=r.childNodes,l=a.length-1;l>=0;l--)n.push(a[l]);if(3===r.nodeType&&(i=t-o,(o+=r.textContent.length)>=t))break}return r||(r=e),{$node:r,offset:i}};t.queryElementNode=function(e,t){return{start:e.startMeta.parentIndex===r.ROOT_IDX?t:t.getElementsByTagName(e.startMeta.parentTagName)[e.startMeta.parentIndex],end:e.endMeta.parentIndex===r.ROOT_IDX?t:t.getElementsByTagName(e.endMeta.parentTagName)[e.endMeta.parentIndex]}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return e.split("-").reduce((function(e,t,n){return e+(0===n?t:t[0].toUpperCase()+t.slice(1))}),"")}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removeSelection=t.getDomRange=void 0;t.getDomRange=function(e){var t=e.getSelection();return t.isCollapsed?(console.debug("no text selected"),null):t.getRangeAt(0)};t.removeSelection=function(e){e.getSelection().removeAllRanges()}},function(e,t,n){"use strict";var r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.formatDomNode=t.getDomMeta=void 0,t.getClosestTextNode=i;var o=n(0);t.getDomMeta=function(e,t,n){var r=function(e){if(e instanceof HTMLElement&&(!e.dataset||!e.dataset[o.CAMEL_DATASET_IDENTIFIER]))return e;for(var t=e.parentNode;null==t?void 0:t.dataset[o.CAMEL_DATASET_IDENTIFIER];)t=t.parentNode;return t}(e),i=r===n?o.ROOT_IDX:function(e,t){for(var n=e.tagName,r=t.getElementsByTagName(n),i=0;i<r.length;i++)if(e===r[i])return i;return o.UNKNOWN_IDX}(r,n),a=function(e,t){for(var n=[e],r=null,o=0;r=n.pop();){for(var i=r.childNodes,a=i.length-1;a>=0;a--)n.push(i[a]);if(3===r.nodeType&&r!==t)o+=r.textContent.length;else if(3===r.nodeType)break}return o}(r,e);return{parentTagName:r.tagName,parentIndex:i,textOffset:a+t}};function i(e){var t,n;if(e.nodeType==e.TEXT_NODE)return e;try{for(var o=r(e.childNodes),a=o.next();!a.done;a=o.next()){var l=i(a.value);if(null!==l&&l.nodeType==e.TEXT_NODE)return l}}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return null}t.formatDomNode=function(e){if(3===e.$node.nodeType||4===e.$node.nodeType||8===e.$node.nodeType)return e;e.$node.childNodes.length>e.offset?e.$node.childNodes[e.offset]:e.$node;return{$node:i(e.$node.childNodes[e.offset]),offset:0}}},function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},o=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e){this.name="",this.ops=[],this.name=e}return e.prototype.tap=function(e){var t=this;return-1===this.ops.indexOf(e)&&this.ops.push(e),function(){t.remove(e)}},e.prototype.remove=function(e){var t=this.ops.indexOf(e);t<0||this.ops.splice(t,1)},e.prototype.isEmpty=function(){return 0===this.ops.length},e.prototype.call=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return this.ops.forEach((function(n){e=n.apply(void 0,o([],r(t),!1))})),e},e}();t.default=i},function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=n(1),i=r(n(15));t.default=function(e){var t=(0,i.default)(e.navigator.userAgent);return{PointerEnd:t?o.UserInputEvent.touchend:o.UserInputEvent.mouseup,PointerTap:t?o.UserInputEvent.touchstart:o.UserInputEvent.click,PointerOver:t?o.UserInputEvent.touchstart:o.UserInputEvent.mouseover}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=/Android|iPhone|BlackBerry|BB10|Opera Mini|Phone|Mobile|Silk|Windows Phone|Mobile(?:.+)Firefox\b/i;t.default=function(e){return r.test(e)}},function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},a=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},l=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var u=s(n(2)),c=n(1),d=function(e){function t(){var t=e.apply(this,a([],i(arguments),!1))||this;return t._data=new Map,t}return o(t,e),Object.defineProperty(t.prototype,"data",{get:function(){return this.getAll()},set:function(e){throw c.ERROR.CACHE_SET_ERROR},enumerable:!1,configurable:!0}),t.prototype.save=function(e){var t=this;Array.isArray(e)?e.forEach((function(e){return t._data.set(e.id,e)})):this._data.set(e.id,e)},t.prototype.get=function(e){return this._data.get(e)},t.prototype.remove=function(e){this._data.delete(e)},t.prototype.getAll=function(){var e,t,n=[];try{for(var r=l(this._data),o=r.next();!o.done;o=r.next()){var i=o.value;n.push(i[1])}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}return n},t.prototype.removeAll=function(){var e,t,n=[];try{for(var r=l(this._data),o=r.next();!o.done;o=r.next()){var i=o.value;n.push(i[0])}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}return this._data=new Map,n},t}(u.default);t.default=d},function(e,t,n){"use strict";var r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},o=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},i=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var l=a(n(3)),s=n(18),u=n(4),c=n(1),d=n(20),f=n(0),h=function(){function e(e,t){this.options={$root:e.$root,rootDocument:e.rootDocument,wrapTag:e.wrapTag,exceptSelectors:e.exceptSelectors,className:e.className,window:e.window},this.hooks=t,(0,d.initDefaultStylesheet)(this.options.rootDocument)}return e.prototype.highlightRange=function(e){var t=this;if(!e.frozen)throw c.ERROR.HIGHLIGHT_RANGE_FROZEN;var n=this.options,r=n.$root,o=n.rootDocument,i=n.className,a=n.exceptSelectors,l=(n.window,this.hooks),u=(0,s.getSelectedNodes)(r,o,e.start,e.end,a);return l.Render.SelectedNodes.isEmpty()||(u=l.Render.SelectedNodes.call(e.id,u)||[]),u.map((function(n){var r=(0,s.wrapHighlight)(n,e,i,t.options.wrapTag,o);return l.Render.WrapNode.isEmpty()||(r=l.Render.WrapNode.call(e.id,r)),r}))},e.prototype.highlightSource=function(e){var t=this,n=Array.isArray(e)?e:[e],r=[];return n.forEach((function(e){if(e instanceof l.default){var n=e.deSerialize(t.options.$root,t.hooks);t.highlightRange(n).length>0?r.push(e):f.eventEmitter.emit(f.INTERNAL_ERROR_EVENT,{type:c.ERROR.HIGHLIGHT_SOURCE_NONE_RENDER,detail:e})}else f.eventEmitter.emit(f.INTERNAL_ERROR_EVENT,{type:c.ERROR.SOURCE_TYPE_ERROR})})),r},e.prototype.removeHighlight=function(e){var t,n,a=this,l=new RegExp("(".concat(e,"\\").concat(f.ID_DIVISION,"|\\").concat(f.ID_DIVISION,"?").concat(e,"$)")),c=this.hooks,d=this.options.wrapTag,h=this.options.rootDocument.querySelectorAll("".concat(d,"[data-").concat(f.DATASET_IDENTIFIER,"]")),p=[],E=[],v=[];try{for(var _=r(h),y=_.next();!y.done;y=_.next()){var T=y.value,g=T.dataset[f.CAMEL_DATASET_IDENTIFIER],I=T.dataset[f.CAMEL_DATASET_IDENTIFIER_EXTRA];g!==e||I?g===e?E.push(T):g!==e&&l.test(I)&&v.push(T):p.push(T)}}catch(e){t={error:e}}finally{try{y&&!y.done&&(n=_.return)&&n.call(_)}finally{if(t)throw t.error}}return p.forEach((function(t){var n=t.parentNode,r=a.options.rootDocument.createDocumentFragment();(0,u.forEach)(t.childNodes,(function(e){return r.appendChild(e.cloneNode(!1))}));var o=t.previousSibling,i=t.nextSibling;n.replaceChild(r,t),(0,s.normalizeSiblingText)(o,!0),(0,s.normalizeSiblingText)(i,!1),c.Remove.UpdateNodes.call(e,t,"remove")})),E.forEach((function(t){var n=t.dataset,r=n[f.CAMEL_DATASET_IDENTIFIER_EXTRA].split(f.ID_DIVISION),l=r.shift(),s=a.options.rootDocument.querySelector("".concat(d,"[data-").concat(f.DATASET_IDENTIFIER,'="').concat(l,'"]'));s&&((0,u.removeAllClass)(t),(0,u.addClass)(t,i([],o(s.classList),!1))),n[f.CAMEL_DATASET_IDENTIFIER]=l,n[f.CAMEL_DATASET_IDENTIFIER_EXTRA]=r.join(f.ID_DIVISION),c.Remove.UpdateNodes.call(e,t,"id-update")})),v.forEach((function(t){var n=t.dataset[f.CAMEL_DATASET_IDENTIFIER_EXTRA];t.dataset[f.CAMEL_DATASET_IDENTIFIER_EXTRA]=n.replace(l,""),c.Remove.UpdateNodes.call(e,t,"extra-update")})),p.length+E.length+v.length!==0},e.prototype.removeAllHighlight=function(){var e=this.options,t=e.wrapTag,n=e.$root,r=e.rootDocument;(0,u.getHighlightsByRoot)(n,t).forEach((function(e){var t=e.parentNode,n=r.createDocumentFragment();(0,u.forEach)(e.childNodes,(function(e){return n.appendChild(e.cloneNode(!1))})),t.replaceChild(n,e),t.normalize()}))},e}();t.default=h},function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a},o=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.normalizeSiblingText=t.wrapHighlight=t.getSelectedNodes=void 0;var i=n(1),a=n(4),l=n(0),s=n(19),u=function(e,t){if(!e)return!1;if(/^\./.test(t)){var n=t.replace(/^\./,"");return e&&(0,a.hasClass)(e,n)}if(/^#/.test(t)){var r=t.replace(/^#/,"");return e&&e.id===r}var o=t.toUpperCase();return e&&e.tagName===o};t.getSelectedNodes=function(e,t,n,r,o){var a=n.$node,l=r.$node,s=n.offset,c=r.offset;if(a===l&&(a instanceof Text||a instanceof t.defaultView.Text))return function(e,t,n,r){for(var o=e,a=function(e){return null==r?void 0:r.some((function(t){return u(e,t)}))};o;){if(1===o.nodeType&&a(o))return[];o=o.parentNode}e.splitText(t);var l=e.nextSibling;return l.splitText(n-t),[{$node:l,type:i.SelectedNodeType.text,splitType:i.SplitType.both}]}(a,s,c,o);for(var d=[e],f=[],h=function(e){return null==o?void 0:o.some((function(t){return u(e,t)}))},p=!1,E=null;E=d.pop();)if(1!==E.nodeType||!h(E)){for(var v=E.childNodes,_=v.length-1;_>=0;_--)d.push(v[_]);if(E===a){if(3===E.nodeType){E.splitText(s);var y=E.nextSibling;f.push({$node:y,type:i.SelectedNodeType.text,splitType:i.SplitType.head})}p=!0}else{if(E===l){if(3===E.nodeType)(y=E).splitText(c),f.push({$node:y,type:i.SelectedNodeType.text,splitType:i.SplitType.tail});break}if(p&&3===E.nodeType){if(""===E.textContent.trim())continue;f.push({$node:E,type:i.SelectedNodeType.text,splitType:i.SplitType.none})}}}return f};var c=function(e,t){var n=Array.isArray(t)?t:[t];return(n=0===n.length?[(0,l.getDefaultOptions)().style.className]:n).forEach((function(t){(0,a.addClass)(e,t)})),e},d=function(e){return!e||!e.textContent};t.wrapHighlight=function(e,t,n,u,f){var h=e.$node.parentNode,p=e.$node.previousSibling,E=e.$node.nextSibling;return(0,a.isHighlightWrapNode)(h)?!(0,a.isHighlightWrapNode)(h)||d(p)&&d(E)?function(e,t,n){var r=e.$node.parentNode,o=r;(0,a.removeAllClass)(o),c(o,n);var i=r.dataset,s=i[l.CAMEL_DATASET_IDENTIFIER];return i[l.CAMEL_DATASET_IDENTIFIER]=t.id,i[l.CAMEL_DATASET_IDENTIFIER_EXTRA]=i[l.CAMEL_DATASET_IDENTIFIER_EXTRA]?s+l.ID_DIVISION+i[l.CAMEL_DATASET_IDENTIFIER_EXTRA]:s,o}(e,t,n):function(e,t,n,a,u){var d=u.createElement(a),f=e.$node.parentNode,h=e.$node.previousSibling,p=e.$node.nextSibling,E=u.createDocumentFragment(),v=f.dataset[l.CAMEL_DATASET_IDENTIFIER],_=f.dataset[l.CAMEL_DATASET_IDENTIFIER_EXTRA],y=_?v+l.ID_DIVISION+_:v;d.setAttribute("data-".concat(l.DATASET_IDENTIFIER),t.id),d.setAttribute("data-".concat(l.DATASET_IDENTIFIER_EXTRA),y),d.appendChild(e.$node.cloneNode(!1));var T,g=!1,I=!1;h&&((m=f.cloneNode(!1)).textContent=h.textContent,E.appendChild(m),g=!0);var m,R=[];(Array.isArray(n)?R.push.apply(R,o([],r(n),!1)):R.push(n),c(d,(0,s.unique)(R)),E.appendChild(d),p)&&((m=f.cloneNode(!1)).textContent=p.textContent,E.appendChild(m),I=!0);return T=g&&I?i.SplitType.both:g?i.SplitType.head:I?i.SplitType.tail:i.SplitType.none,d.setAttribute("data-".concat(l.DATASET_SPLIT_TYPE),T),f.parentNode.replaceChild(E,f),d}(e,t,n,u,f):function(e,t,n,r,o){var i=o.createElement(r);return c(i,n),i.appendChild(e.$node.cloneNode(!1)),e.$node.parentNode.replaceChild(i,e.$node),i.setAttribute("data-".concat(l.DATASET_IDENTIFIER),t.id),i.setAttribute("data-".concat(l.DATASET_SPLIT_TYPE),e.splitType),i.setAttribute("data-".concat(l.DATASET_IDENTIFIER_EXTRA),""),i}(e,t,n,u,f)};t.normalizeSiblingText=function(e,t){if(void 0===t&&(t=!0),e&&3===e.nodeType){var n=t?e.nextSibling:e.previousSibling;if(3===n.nodeType){var r=n.nodeValue;e.nodeValue=t?e.nodeValue+r:r+e.nodeValue,n.parentNode.removeChild(n)}}}},function(e,t,n){"use strict";var r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.unique=void 0;t.unique=function(e){var t,n,o=[];try{for(var i=r(e),a=i.next();!a.done;a=i.next()){var l=a.value;-1===o.indexOf(l)&&o.push(l)}}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}return o}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.initDefaultStylesheet=void 0;var r=n(0);t.initDefaultStylesheet=function(e){var t=r.STYLESHEET_ID,n=e.getElementById(t);if(!n){var o=e.createTextNode((0,r.getStylesheet)());(n=e.createElement("style")).id=t,n.appendChild(o),e.head.appendChild(n)}return n}}]).default}));
//# sourceMappingURL=web-highlighter.min.js.map