<template>
  <el-config-provider :locale="zhCn">
    <div class="common-layout overflow-hidden h-100vh">
      <div class="header-nav flex flex-space-between items-center pl-30px pr-30px">
        <div class="flex items-center color-#fff text-20px">
          <div class="mr-10px bg-#fff w-30px">
            <img @click="Router.push('/sample/importManage')"
                 class="main-logo"
                 src="@/assets/img/logo.png"
                 alt="logo" />
          </div>
          标注平台
        </div>
        <div class="flex items-center cursor-pointer">
          <el-avatar :size="30"
                     :src="store.picSrc" />
          <el-dropdown>
            <div class="color-#fff text-14px ml-10px">
              {{ store.userInfo.realName }}
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="exit"> 退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="h-100vh flex w-100% app-main pt-60px overflow-y-auto">
        <div class="zy-el-aside"
             v-if="!store.isMicroApp">
          <Menu />
        </div>
        <div class="main-content">
          <div class="crumbs-body">
            <div class="page-title">{{ topTitle }}</div>
            <div class="page-describer">{{ info }}</div>
          </div>
          <router-view />
          <!--          <template v-if="data.isAuth || 1">-->
          <!--            <router-view />-->
          <!--          </template>-->
          <!--          <div v-else>401</div>-->
        </div>
      </div>
    </div>
  </el-config-provider>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

import Menu from "@/components/Menu.vue";

import utils from "@/common/utils";

import zhCn from "element-plus/dist/locale/zh-cn.mjs";

import { RoutePathCodeEnum } from "@/router/routes";

import { useCommonStore } from "@/store/useCommonStore";
import { useUserStore } from "@/store/useUserStore";
const Router = useRouter();
const route: any = useRoute();
const store = useUserStore();
const CommonStore = useCommonStore();

const topTitle = ref("");
const info = ref<string | null>(null);
const titleMap = {
  ImportManage: "样本管理",
  TaskManage: "标注任务",
  "sample-approval-list": "样本审批",
  "quality-list": "标注项",
  "enum-list": "枚举值",
  "evaluation-list": "评测管理",
  "session-list": "模型评测",
  EvaluationMessageList: "信息提取类评测",
  EvaluationErrorList: "纠错准度评测",
  EvaluationTextList: "文本生成评测",
  "prompt-evaluation": "流式评测",
  "session-evaluation": "评测任务",
};

const data = reactive({
  circleUrl:
    "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
  squareUrl:
    "https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png",
  sizeList: ["small", "", "large"] as const,
  isAuth: true,
  loaded: false,
});

async function exit() {
  await utils.removeCookie("passport-token");
  await store.getSsoUrl();
}

onMounted(async () => {
  // const loading = ElLoading.service({
  //   lock: true,
  //   text: '加载中...',
  //   background: 'rgba(0, 0, 0, 0.7)',
  // })
  await store.getUserToken();
  await store.getUserInfo();
  await store.getUserPermissionCode();
  await store.getCurUserRoles();
  await CommonStore.getItemTypeMap();
  // console.log(store.getPermissionCode)
  // console.log(store.getUserRoleList)
  // put("管理人员",384);
  // put("审批人员",386);
  // put("标注人员",385);

  // authHandle()
  // loading.close()
  data.loaded = true;
  const messageInfo = sessionStorage.getItem("info");
  info.value = messageInfo;
});

function authHandle() {
  const code = RoutePathCodeEnum[route.path];
  console.log({ code });
  data.isAuth = store.permissionCode.includes(code);
  if (code && !data.isAuth) {
    Router.replace("/401");
  }
}

watch([(): string => route.name, (): string => route.path], ([name, path]) => {
  console.log("watch: ", name);
  topTitle.value = titleMap[name] || "";
  if (path !== "/" && data.loaded) {
    // authHandle()
  }
});
</script>

<style lang="less" scoped>
.common-layout {
  // overflow: hidden;
  // height: 100vw;

  .header-nav {
    background: #4455ff;
    // background: blue;
    height: 60px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 888;
  }

  .main-content {
    box-sizing: border-box;
    flex: 1;
    background-color: #f6f6f6;
    padding: 15px 16px;
    overflow-y: auto;

    .crumbs-body {
      margin-top: -16px;
      margin-left: -16px;
      margin-right: -16px;
      margin-bottom: 16px;
      background: #fff;
      padding: 0 16px;

      .page-title {
        font-size: 16px;
        font-weight: 500;
        line-height: 48px;
        font-family: PingFangSC-Medium;
      }
      .page-describer {
        font-size: 12px;
        color: #666;
        line-height: 17px;
      }
    }
  }
}

.app-main {
  height: 100vh;
}
</style>
