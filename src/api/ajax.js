import { ElMessage } from 'element-plus';

import axios from 'axios';

import { useUserStore } from '@/store/useUserStore';

const instance = axios.create({
  timeout: 100000,
});

const Whitelist = ['/crmAuditConf/authorize/login'];
let store = null;

const getUserStore = () => {
  if (!store) {
    store = useUserStore();
  }
};

// const isProd = import.meta.env.PROD

// request拦截器
instance.interceptors.request.use(async (config) => {
  getUserStore();
  // let TOKEN = window.location.host.includes('bdp.yiche.com')
  //   ? Utils.getCookie('passport-token')
  //   : testToken
  config.headers['access-token'] = store.getToken;
  return config;
});
// 异步处理response请求
async function responseData(response) {
  const res = response.data;
  let code = res.Status !== undefined ? res.Status : res.code;
  if (res === 'SUCCESS') {
    code = 0;
  }

  if (+code === 40101 || +code === 401) {
    console.log(code, response);
    // 40101: token过期
    store.clearUserData();
    return Promise.reject();
  }
  return { ...response, code };
}
instance.interceptors.response.use(responseData, (error) => {
  if (error.response.status === 500) {
    //console.error('接口请求失败',error.response.config.url);
    ElMessage.error(`请求失败 接口名称${error.response.config.url}`);
  }
  return '';
});

export const ajax = (opts = {}) =>
  new Promise((resolve, reject) => {
    const {
      url,
      method,
      headers,
      data,
      params,
      callback,
      msg,
      config,
      responseType,
    } = opts;
    instance({
      url,
      method: method || 'GET',
      headers: {
        'content-type': 'application/json',
        'Cache-Control': 'no-transform',
        ...headers,
      },
      data: data || {},
      params: params,
      responseType,
      ...config,
    })
      .then((res) => {
        // 异常状态处理
        if (!res) {
          //message.error('网络异常');
          return;
        }
        callback && callback(res);
        let data = null;
        if (res.status !== 200) {
          resolve(res);
          return;
        }

        data = res.data.records || res.data;
        if (res.data.code === 0) {
          if (data === 'OK' && msg !== 'no') {
            ElMessage.success(msg || '操作成功！');
          }
          resolve(data);
        } else {
          if (res.data.code === 500) {
            ElMessage.error('服务器内部错误');
            reject({ ...data, msg: res.data.msg, code: res.data.code });
          } else if (res.config.responseType === 'json') {
            ElMessage.error(res.data.msg);
          }
          resolve({ ...data, msg: res.data.msg, code: res.data.code, data });
        }
      })
      .catch((e) => {
        // console.log(e)
        ElMessage.error('网络错误，请重试');
        reject(e);
      });
  });
