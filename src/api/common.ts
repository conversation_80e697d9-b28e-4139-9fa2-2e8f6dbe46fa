import { ajax } from '@/api/ajax'

import { CommonDropDownType, SelectListItemType } from '@/common/interface'

// 根据ID获取一个用户标注详情
export const getPermissionCode = (resourceId = '23'): Promise<string[]> => {
  return ajax({
    url: '/nbi/uc/uResource/permission',
    params: {
      resourceId,
    },
  })
}

// 获取公共下拉列表 1 所属应用 2 执行状态 4 对应服务 5 任务进度  6 标注状态 7 导入批次 8 标签配置类型
export const getDropDownList = (
  dropDownType: CommonDropDownType
): Promise<SelectListItemType[]> => {
  return ajax({
    url: '/nbi/anno/common/getDropDownList',
    params: {
      dropDownType,
    },
  })
}

// 获取所有参与标注的人员信息
export const getAllAnnoUsers = (): Promise<SelectListItemType[]> => {
  return ajax({
    url: '/nbi/anno/sampleAudit/getAllAnnoUsers',
  })
}

export const getAnnoTaskList = (
  appId: string | null = null,
  serviceId: string | null = null,
  taskStatus: string | null = null
): Promise<SelectListItemType[]> => {
  return ajax({
    url: '/nbi/anno/api/annoTask/getAnnoTaskList',
    params: {
      appId,
      serviceId,
      taskStatus,
    },
  })
}

// 获取当前登录用户的角色列表
export const getCurUserRoles = (): Promise<SelectListItemType[]> => {
  return ajax({
    url: '/nbi/anno/common/getCurUserRoles',
  })
}

// 获取根类型Map
export const getItemTypeMapApi = (): Promise<Record<string, number[]>> => {
  return ajax({
    url: '/nbi/anno/tags/getItemTypeMap',
  })
}
