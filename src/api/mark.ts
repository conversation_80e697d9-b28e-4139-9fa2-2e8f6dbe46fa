import { ajax } from '@/api/ajax'

import type { MarkDetailType } from '@/common/interface'
import { markReqItemType, PromiseStrMsgType } from '@/common/interface'

// 根据ID获取一个用户标注详情
/*
* userAnnoId: number | string,
  idOffset = 0, // 0 默认 1 下一条
  viewMode = 'view'
  * 过滤条件
* */
export const getOneAnnoById = (data: any): Promise<MarkDetailType> => {
  return ajax({
    url: '/nbi/anno/sampleAnno/getOneAnnoById',
    params: data,
  })
}

// 废弃一个标注样本的标注
export const abandonAnAnno = (id: number | string): PromiseStrMsgType => {
  return ajax({
    url: '/nbi/anno/sampleAnno/abandonAnAnno',
    method: 'POST',
    data: { id },
  })
}

// 提交一个标注样本的标注进行审核
export const submitAudit = (id: number | string): Promise<string> => {
  return ajax({
    url: '/nbi/anno/sampleAnno/submitAudit',
    method: 'POST',
    data: { id },
  })
}

// 保存一个标注样本任务
export const saveAnAnno = (data: {
  userAnnoId: string
  userAnnoDetails: markReqItemType[]
  msg?: string
}): PromiseStrMsgType => {
  console.log(data.msg)
  return ajax({
    url: '/nbi/anno/sampleAnno/saveAnAnno',
    method: 'POST',
    data,
    msg: data.msg || '保存成功！',
  })
}

// 继续标注 修改状态 待审批 -> 标注中
export const continueAnno = (id: string | number): Promise<string> => {
  return ajax({
    url: '/nbi/anno/sampleAnno/continueAnno',
    method: 'POST',
    data: {
      id,
    },
  })
}
