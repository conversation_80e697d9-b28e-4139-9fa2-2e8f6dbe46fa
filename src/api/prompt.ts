import { ajax } from '@/api/ajax'

import {
  PromiseResType,
  PromptListItemType,
  PromptListResponseType,
  PromptListResType,
  SelectListItemType,
} from '@/common/interface'

// 获取流分页列表
export const getTagsPage = (
  params: PromptListItemType
): PromptListResponseType<PromptListResType> => {
  const notNullParams = removeEmptyProperties(params)
  return ajax({
    url: '/nbi/evaluate/flowEval/flow/getFlowPage',
    params: notNullParams,
  })
}

function removeEmptyProperties(obj) {
  const newObj = {}
  for (const key in obj) {
    if (obj[key]) {
      newObj[key] = obj[key]
    }
  }
  return newObj
}

// 模糊获取所有下拉列表 1 获取任务列表 2 获取状态列表 3 获取创建人列表 4 获取所有我的Prompt 5 根据InstructID获取Prompt版本信息 6 获取计算类型列表
export const getCommonDropDownList = (
  type: number,
  param1?: string
): Promise<SelectListItemType[]> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/common/getCommonDropDownList',
    method: 'GET',
    params: {
      type,
      param1,
    },
  })
}
// 获取问题集列表
export const getQuestionSetList = (): Promise<SelectListItemType[]> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/common/getQuestionSetList',
    method: 'GET',
  })
}

// 根据flowId删除一个任务
export const delTask = (flowId: any): Promise<PromiseResType> => {
  return ajax({
    url: `/nbi/evaluate/flowEval/flow/deleteFlow`,
    method: 'POST',
    data: {
      flowId,
    },
  })
}
// 开始一个流式评测任务
export const startTask = (flowId: any): Promise<PromiseResType> => {
  return ajax({
    url: `/nbi/evaluate/flowEval/flow/startFlowAction`,
    method: 'POST',
    data: {
      flowId,
    },
  })
}
// 删除流式评测任务节点
export const deleteAction = (data): Promise<PromiseResType> => {
  return ajax({
    url: `/nbi/evaluate/flowEval/flow/deleteAction`,
    method: 'POST',
    data,
  })
}
// 获取标注项下拉列表
export const getPromptPlaceHolderList = (
  instructId?: string,
  promptId?: string
): Promise<SelectListItemType[]> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/common/getPromptPlaceHolderList',
    method: 'GET',
    params: {
      instructId,
      promptId,
    },
  })
}
// 根据FlowId,step获取ACTION详情
export const getFlowActionDetailByFlowId = (
  flowId: any,
  stepOrder: number
): Promise<boolean> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/flow/getFlowActionDetailByFlowId',
    method: 'GET',
    params: {
      flowId,
      stepOrder,
    },
  })
}

export const createFlow = (
  flowName: string,
  id?: string
): Promise<SelectListItemType[]> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/flow/createOrUpdateFlow',
    method: 'POST',
    data: {
      flowName,
      id,
    },
  })
}

// 创建流式评测任务节点
export const createAction = (data): Promise<boolean> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/flow/createAction',
    method: 'POST',
    data: data,
  })
}

// 根据ID获取任务详情
export const getFlowDetailList = (data): Promise<boolean> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/flow/getFlowDetailWithDataPageByFlowId',
    method: 'GET',
    params: data,
  })
}

// 根据ID获取之前的列名
export const getColumnGroupMap = (data): Promise<any> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/common/getColumnGroupMapByFlowId',
    method: 'GET',
    params: data,
  })
}

// 导出列表内容
export const downLoadDetailsByFlowId = (flowId): Promise<boolean> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/flow/downLoadDetailsByFlowId',
    method: 'GET',
    responseType: 'blob',
    params: {
      flowId,
    },
  })
}

// 导出列表内容
export const getJsonPlaceHolderList = (data): Promise<boolean> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/common/getJsonPlaceHolderList',
    method: 'POST',
    data,
  })
}

// 停止评测
export const stopFlowAction = (data): Promise<boolean> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/flow/stopFlowAction',
    method: 'POST',
    data,
  })
}
// 启动任务
export const startFlowAction = (data): Promise<boolean> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/flow/startFlowAction',
    method: 'POST',
    data,
  })
}

// 获取任务每一列状态
export const getActionListByFlowId = (data): Promise<boolean> => {
  return ajax({
    url: '/nbi/evaluate/flowEval/common/getActionListByFlowId',
    method: 'GET',
    params: data,
  })
}
