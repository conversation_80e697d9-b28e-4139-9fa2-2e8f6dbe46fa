import { ajax } from '@/api/ajax'

import type {
  CorrectionReqType,
  NumberOrString,
  PagListResType,
  PromiseResType,
  PushQuestionReqType,
  SampleListResType,
  TaskCreateReqType,
  TaskDetailListReqType,
  TaskDetailPagListResType,
  TaskListReqType,
  TaskListResType,
  TaskPagListResType,
  TaskResListType,
} from '@/common/interface'

import { useUserStore } from '@/store/useUserStore'

const apiPrefix = '/nbi/queshub'

// 评测样本映射列表
export const getEvaluationSampleList = (params: {
  sampleSource: NumberOrString
}): Promise<SampleListResType[]> => {
  return ajax({
    url: `${apiPrefix}/evaluation/task/evaluationSampleList`,
    params,
  })
}

// 样本数据映射列表
export const getSampleDataList = (params: {
  sampleSource: NumberOrString
  id: NumberOrString
}): Promise<SampleListResType[]> => {
  return ajax({
    url: `${apiPrefix}/evaluation/task/sampleDataList`,
    params,
  })
}

// 更新任务名称
export const updateTaskName = (params: {
  taskName: string
  id: NumberOrString
}): Promise<any[]> => {
  return ajax({
    url: `${apiPrefix}/evaluation/task/update`,
    params,
  })
}

// 创建任务
export const createTask = (data: TaskCreateReqType): Promise<any> => {
  const store = useUserStore()

  return ajax({
    url: `${apiPrefix}/evaluation/task/create`,
    method: 'POST',
    data: {
      ...data,
      username: store.userInfo.realName,
    },
  })
}

// 任务列表分页
export const getTaskList = (
  params: TaskListReqType
): Promise<PagListResType<TaskPagListResType>> => {
  return ajax({
    url: `${apiPrefix}/evaluation/task/page`,
    params,
  })
}

// 删除任务
export const delTask = (params: {
  id: NumberOrString
}): Promise<PromiseResType> => {
  return ajax({
    url: `${apiPrefix}/evaluation/task/del`,
    params,
  })
}

// 任务详情
export const getTaskDetail = (params: {
  id: NumberOrString
}): Promise<TaskCreateReqType> => {
  return ajax({
    url: `${apiPrefix}/evaluation/task/detail`,
    params,
  })
}

// 明细分页
export const getTaskDetailList = (
  params: TaskDetailListReqType
): Promise<PagListResType<TaskDetailPagListResType>> => {
  return ajax({
    url: `${apiPrefix}/evaluation/task/detail/itemPage`,
    params,
  })
}

// 明细分页 多模型
export const getModelDetailList = (
  params: TaskDetailListReqType
): Promise<PagListResType<TaskDetailPagListResType>> => {
  return ajax({
    url: `${apiPrefix}/evaluation/task/answer/detail`,
    params,
  })
}

// 结果修正
export const setCorrection = (data: CorrectionReqType): Promise<any> => {
  return ajax({
    url: `${apiPrefix}/evaluation/task/detail/correction`,
    method: 'POST',
    data,
  })
}

// 推送问题库
export const pushQuestionManage = (data: PushQuestionReqType): Promise<any> => {
  return ajax({
    url: `${apiPrefix}/evaluation/task/detail/pushQuestionManage`,
    method: 'POST',
    data,
  })
}

// 评测任务统计结果列表
export const getTaskResList = (
  params: TaskResListType
): Promise<PagListResType<TaskListResType>> => {
  return ajax({
    url: `${apiPrefix}/statistics/page`,
    params,
  })
}

// 评测任务统计结果导出下载
export const downloadData = (params: {
  taskId: NumberOrString
}): Promise<{
  data: Blob
}> => {
  return ajax({
    url: `${apiPrefix}/statistics/downloadData`,
    params,
    responseType: 'blob',
  })
}

// 获取问题集列表
export const getQueList = () => {
  return ajax({
    url: `${apiPrefix}/quesBank/coll/list`,
    params: {
      collName: '',
    },
  })
}

// 意图ID列表
export const getIntentionIdList = async (): Promise<any[]> => {
  return ajax({
    url: `${apiPrefix}/common/intentionIdList`,
  })
}

// 是否管理员账号 1:管理员 2:普通用户
export const getUserRole = () => {
  return ajax({
    url: `${apiPrefix}/common/userStatus`,
  })
}
