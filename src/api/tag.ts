import { ajax } from '@/api/ajax'

import {
  AuddtingSearchType,
  AuditingResponseType,
  ListResponseType,
  newItemReqType,
  newTagReqType,
  PromiseStrMsgType,
  SelectListItemType,
  TagsListResType,
  TagsPageListQueryType,
} from '@/common/interface'

// 获取标签信息分页
export const getTagsPage = (
  params: TagsPageListQueryType
): ListResponseType<TagsListResType> => {
  const notNullParams = removeEmptyProperties(params)
  return ajax({
    url: '/nbi/anno/tags/getTagsPage',
    params: notNullParams,
  })
}

function removeEmptyProperties(obj) {
  const newObj = {}
  for (const key in obj) {
    if (obj[key]) {
      newObj[key] = obj[key]
    }
  }
  return newObj
}

// 获取关联选项列表
export const getItemsDropDownForCreate = (): Promise<SelectListItemType[]> => {
  return ajax({
    url: '/nbi/anno/tags/getItemsDropDownForCreate',
  })
}

// 获取标注项下拉列表
export const getTagDropDownForQuery = (): Promise<SelectListItemType[]> => {
  return ajax({
    url: '/nbi/anno/tags/getTagDropDownForQuery',
  })
}

// 创建一个新的质检项
export const newTag = (data: newTagReqType): PromiseStrMsgType => {
  return ajax({
    url: '/nbi/anno/tags/newTag',
    method: 'POST',
    data,
  })
}

// 创建一个新的选择项
export const newItem = (data: newItemReqType): PromiseStrMsgType => {
  return ajax({
    url: '/nbi/anno/tags/newItem',
    method: 'POST',
    data,
  })
}

// 根据应用ID获取所有的标注项
export const getTagListForAnno = (
  annoTaskId: string
): Promise<TagsListResType[]> => {
  return ajax({
    url: '/nbi/anno/tags/getTagListForAnno',
    params: {
      annoTaskId,
    },
  })
}

// 获取标注项子下拉列表
export const getItemDropDownForAnno = (
  itemId: number | string | undefined,
  userInput?: string
): Promise<SelectListItemType[]> => {
  return ajax({
    url: '/nbi/anno/tags/getItemDropDownForAnno',
    params: {
      itemId,
      userInput,
    },
  })
}

// 获取待审批的用户标注详情分页
export const getAuditingPage = (
  params: AuddtingSearchType
): Promise<ListResponseType<AuditingResponseType>> => {
  return ajax({
    url: '/nbi/anno/sampleAudit/getAuditingPage',
    params,
  })
}

// 通过标注样本的标注进行审核批量
export const passAnAnno = (data: number | string[]): PromiseStrMsgType => {
  return ajax({
    url: '/nbi/anno/sampleAudit/passAnAnno',
    method: 'POST',
    data,
  })
}

// 驳回标注样本的标注进行审核批量
export const rejectAnAnno = (data: number | string[]): PromiseStrMsgType => {
  return ajax({
    url: '/nbi/anno/sampleAudit/rejectAnAnno',
    method: 'POST',
    data,
  })
}

// 对应值获取列表
export const getItemsPage = (data: {
  page: number
  size: number
  keyword: string
}): ListResponseType<TagsListResType> => {
  return ajax({
    url: '/nbi/anno/tags/getItemsPage',
    method: 'POST',
    data,
  })
}

// 删除对应值
export const deleteItem = (data: { itemId: number }): Promise<boolean> => {
  return ajax({
    url: '/nbi/anno/tags/deleteItem',
    method: 'POST',
    params: data,
  })
}
