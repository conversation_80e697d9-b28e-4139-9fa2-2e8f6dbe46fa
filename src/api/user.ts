import { ajax } from '@/api/ajax'

import { getSsoCheckUrl } from '@/common/helper'

export const getUserToken = (prams) => {
  return ajax({
    method: 'POST',
    url: '/nbi/sso/yc/sso_login',
    data: prams,
  })
}

export const loginCheck = () => {
  return ajax({
    url: '/nbi/sso/yc/login_check',
    method: 'POST',
    data: {
      platform: getSsoCheckUrl(),
    },
  })
}

export const getRefreshToken = (prams) => {
  return ajax({
    method: 'POST',
    url: '/nbi/auth/refreshToken',
    data: prams,
  })
}
export const getCurrentEmployee = (prams = {}) => {
  return ajax({
    method: 'get',
    url: '/nbi/auth/getCurrentEmployee',
    data: prams,
  })
}
export const getAllemployeeapi = (params) => {
  return ajax({
    url: '/allemployeeapi.ashx',
    params,
  })
}
export const listResourceByCurrentUser = (params) => {
  return ajax({
    url: '/nbi/uc/nbiUBusiness/listResourceByCurrentUser',
    params,
  })
}
// 获取可以埋点的产品
export const getByCode = (params) => {
  return ajax({
    url: '/nbi/utrack/board/general/dict/getByCode',
    params,
  })
}
