@unocss all;

.main-logo {
  display: block;
  width: 100%;
  cursor: pointer;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-h {
  min-height: calc(100vh - 90px);
}

.session-page,
.task-content,
.mark-task-page,
.label-content
{

  .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #f0f0f0;
  }

  .el-tabs__item {
    color: rgba(0,0, 0, 0.88);
    font-weight: normal;

    &:hover {
      color: #4096ff;
    }

    &.is-active {
      color: #1677ff;
    }

    &.is-disabled {
      color: rgba(0,0, 0, 0.25);
    }
  }

  .el-tabs__active-bar {
    background-color: #1677ff;
  }
}

.pd-20 {
  padding: 20px;
}