@use "sass:list";
@use "sass:math";
@use "sass:color";

$role-colors: #dc0100, #008001, #0000ff, #01abb3, #810ca8;
$colors: #afdafe, #66b1ff, #ebb563, #b799ff, #f99b7d, #efedd4;

@for $i from 1 through length($colors) {
  .annotator-hl-#{$i} {
    background: list.nth($colors, $i);

    //opacity: (math.random());
  }
}

.mark-selected {
  cursor: pointer;
}

.mark-active {
  position: relative;
  font-size: 24px;
  text-decoration: underline;
  color: red;

  //background-color: #b799ff;

  /* &::before {
    position: absolute;
    top: -10px;

    //display: none;
    display: inline-block;
    border: 8px solid transparent;
    border-top: 8px solid red;

    //content: attr(data-selector);
    content: "";
  } */
}

.mark-page {
  //overflow-y: auto;
  position: relative;
  overflow: hidden;

  //padding: 20px;
  height: calc(100vh - 168px);
  background-color: #ffffff;

  &.audio {
    height: calc(100vh - 222px) !important;
    z-index: 2;
  }
}

.box-disabled {
  user-select: none;
  pointer-events: none;
}

.mark-container {
  display: flex;
  justify-content: space-between;
  height: 100%;
}

.mark-player {
  position: absolute;
  left: 5px;
  top: 0;
  margin-top: 21px;
  z-index: 99;
}

.mark-page-position {
  position: relative;
}

.player-list {
  width: 50px;
  margin-bottom: 20px;
  padding-top: 4px;
  padding-left: 10px;
  // background: red;
}

#player {
  margin-bottom: 10px;

  // width: 100%;
  background-color: white;
  border-radius: 2px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

  .aplayer-pause {
    right: 6px;
    bottom: 6px;
  }

  .aplayer-volume-wrap {
    display: none;
  }


  .aplayer-icon-loop {
    display: none;
  }

  .aplayer-music {
    display: none;
  }

  .aplayer-pic {
    margin-top: 5px;
    width: 33px;
    height: 33px !important;
  }

  .aplayer-info {
    height: 44px;
    margin-left: 10px;
  }
}

.audio-player {
  width: 100%;
  height: 28px;
}

.mark-content {
  //margin: 50px 20%;
  position: relative;
  overflow-y: auto;

  //border-top: 1px solid #999999;
  padding: 5px 20px 10px 45px;
  height: 100%;
  box-shadow: 2px 0 12px rgba(0, 0, 0, 12%);
  line-height: 2;
  flex: 1;

  &::-webkit-scrollbar {
    display: none;
    width: 10px;
    height: 10px;
  }
}

.mark-tools {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0;

  //margin-top: 20px;
}

.mark-config-wrapper {
  overflow-y: auto;
  padding: 10px 15px 100px;
  height: 100%;
  box-sizing: border-box;
  flex: 0 0 309px;
  // z-index: 1;

  &::-webkit-scrollbar {
    display: none;
    width: 10px;
    height: 10px;
  }
}

.mark-config-title {
  margin-bottom: 10px;
}

.mark-config-card {
  margin-bottom: 10px;

  .el-card__body {
    padding: 10px;
  }

  &.active {
    border-color: #409eff;
  }

  &.invalid {
    box-shadow: 0 0 3px 1px inset #ff0000;
  }
}

.mark-config-head,
.mark-config-head-middle {
  display: flex;
  align-items: center;
}

.mark-config-head-index {
  margin-right: 5px;
  border: 1px solid #333333;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  text-align: center;
  color: #ff0000;
  flex: 0 0 20px;
  line-height: 20px;
}

.mark-config-head-middle {
  flex: 1;
}

.mark-config-head-label {
  flex: 0 0 42px;
  font-size: 14px;
}

.mark-config-head-text {
  overflow: hidden;
  padding-right: 10px;
  width: 180px;
  font-size: 14px;
  font-weight: bold;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mark-config-props {
  display: flex;
  align-items: center;
  margin-top: 15px;

  .el-input-number .el-input__inner {
    text-align: left;
  }
}

.mark-config-props-label {
  flex: 0 0 60px;
  font-size: 14px;
}

.mark-config-props-select {
  flex: 1;
}

.ignore,
.disabled {
  user-select: none;
  background: #dddddd;
}

.block {
  display: inline-block;
}

.textDesc {
  border: 0;
  border-radius: 5px;
  padding: 5px;
  width: 100%;
  height: 51px;
  resize: none;
  box-sizing: border-box;
  line-height: 1;
}

.mark-edit {
  background: #ffffff;
}

.dialogue-wrapper {
  border: 1px solid pink;
  padding: 10px;
  line-height: 30px;
}

.dialogue-list {
  display: flex;
  margin-bottom: 20px;
}

.dialogue-role,
.dialogue-time {
  height: 30px;
  line-height: 30px;
}

.dialogue-role {
  padding-left: 5px;
  font-weight: bold;
  font-size: 15px;
  flex: 0 0 110px;
  //letter-spacing: 10px;

  @for $i from 1 through length($role-colors) {
    &.role-class-#{$i} {
      color: list.nth($role-colors, $i);

      //opacity: (math.random());
    }
  }
}

.dialogue-msg {
  border-bottom: 1px solid #eeeeee;
  padding: 0 10px;
  flex: 1;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 16px;
}

.dialogue-time {
  padding: 0 10px;
  font-size: 14px;
  text-align: right;

  //flex: 0 0 90px;
  color: #666666;
}

.context-menu-wrapper {
  position: absolute;
  z-index: 99;
  display: none;
  padding: 10px;
  background-color: #ffffff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 50%);
  transition: all 1s;

  &.show {
    display: block;
  }

  .mark-config-head-text {
    flex: 1;
    padding-right: 0;
  }

  .el-cascader-node {
    padding: 10px;
  }

  .el-cascader-menu {
    border: none;
    min-width: 130px;
    height: 100%;

    &:last-child {
      //min-width: auto;
      box-shadow: 0 0 5px rgba(0, 0, 0, 50%);
    }
  }

  .el-scrollbar__wrap {
    height: auto;
  }

  .el-cascader-node.is-active {
    font-weight: initial;
    color: #606266;

    .el-cascader-node__prefix {
      display: none;
    }
  }
}

.page-header {

  .el-breadcrumb__inner a,
  .el-breadcrumb__inner.is-link {
    color: #409eff;
  }
}

/*
样本类型 问答类
*/

.mark-page-qa {

  .dialogue-content {
    flex: 1;
  }

  .dialogue-time {
    display: inline-flex;
  }

  .dialogue-ps {
    flex: 0 0 300px;
    padding-left: 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .dialogue-ps-wrap {
    flex: 1;
    border: 1px solid #eee;
    min-height: 100px;
    max-height: 500px;
    overflow-y: auto;
  }

  .dialogue-ps-content {
    resize: none;
    font-size: 12px;
    line-height: 1.6;

    &.disabled {
      background-color: #F6F6F6;
    }
  }
}