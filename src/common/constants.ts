export const hlColors = [
  '#AFDAFE',
  '#66B1FF',
  '#EBB563',
  '#B799FF',
  '#F99B7D',
  '#EFEDD4',
]
export const ColorList = hlColors.map((color, i) => ({
  value: `annotator-hl-${i + 1}`,
  label: color,
}))

export const ConfigType = [
  {
    value: '01',
    label: '自定义',
  },
  {
    value: '02',
    label: '维表关联',
  },
]

export const ConfigValueType = [
  {
    value: '01',
    label: '输入',
  },
  {
    value: '02',
    label: '选择',
  },
]

export const TagStatus = [
  {
    key: 0,
    value: '有效',
  },
  {
    key: 1,
    value: '无效',
  },
]

export enum TagStatusEnum {
  '有效' = 0,
  '无效' = 1,
}

// 样本类型
export const SampleTypeOptions = [
  {
    value: '0',
    label: '对话类',
    desc: '*NLP质检类项目',
  },
  // {
  //   value: '1',
  //   label: '图像',
  //   desc: '*图像质检类项目',
  // },
  // {
  //   value: '2',
  //   label: '音频',
  //   desc: '*音频质检类项目',
  // },
  // {
  //   value: '3',
  //   label: '视频',
  //   desc: '*视频质检类项目',
  // },
  // {
  //   value: '4',
  //   label: '网页',
  //   desc: '*网页质检类项目',
  // },
  {
    value: '1001',
    label: '问答类',
    desc: '*智能对话类项目',
  },
  {
    value: '1002',
    label: '对话类+录音',
    desc: '*文本纠错类项目',
  },
  {
    value: '1003',
    label: '问答&文章类',
    desc: '*智能对话、智能概要类项目',
  },
]

// 标注方式
export const MarkTypes = [
  {
    value: 0,
    label: '划词标注',
    desc: '可自由选字、词、句进行标注',
  },
  {
    value: 1,
    label: '固定标注',
    desc: '无需划选，固定内容下进行标注，如一问一答或整个样本下',
  },
]

export enum SampleTypeEnum {
  '对话类' = 0,
  '图像',
  '音频',
  '视频',
  '网页',
  '问答类' = 1001,
  '问答&文章类' = 1003,
  '对话类+录音' = 1002,
}

export const SampleTypeToPath = {
  0: '/mark',
  1001: '/mark-ask',
  1002: '/mark-audio',
  1003: '/mark-ask',
}

export const MarkTaskTabs = [
  {
    name: '21',
    label: '标注任务',
    path: '/task/taskManage',
  },
  {
    name: '22',
    label: '样本审批',
    path: '/sample-approval-list',
  },
  {
    name: '23',
    label: '预标注',
    disabled: true,
  },
]

export const MarkTaskPoint = {
  '21': 'task-approval',
  '22': 'sample-approval',
  '23': 'sample-approval',
}

export const LabelTabs = [
  {
    name: '31',
    label: '标注项',
    path: '/quality-list',
    disabled: false,
  },
  {
    name: '32',
    label: '枚举值',
    path: '/enum-list',
    disabled: false,
  },
]

// 枚举值类型 （自定义下拉列表）
export const EnumTypeList = [0, 9]

// 维表类型
export const TableTypeEnumList = [6, 7, 8, 10, 11, 12]

export enum TagTypeEnum {
  'LIMITED_DROPDOWN' = 0,
  'USER_INPUT_STRING',
  'USER_INPUT_NUMBER',
  'INTERFACE',
}
