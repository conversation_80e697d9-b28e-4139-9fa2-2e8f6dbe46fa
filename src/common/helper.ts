import {
  markConfigItemBackendType,
  markConfigItemType,
} from '@/hooks/JsMark/common/interface'

export const isTrue = (value) => {
  if ([null, undefined].includes(value)) return false
  if (typeof value === 'number') return true
  return typeof value === 'string' && value.trim()
}

export let getSsoCheckUrl = () => {
  const platform = window.location.host.includes('bdp.yiche.com')
    ? 'anno-platform'
    : 'anno-platform-dev'
  getSsoCheckUrl = function () {
    return platform
  }
  return platform
}

export function getLastData(
  data: markConfigItemBackendType[]
): markConfigItemType[] {
  return data.map(
    (
      {
        position,
        content,
        tagId,
        tagCode,
        tagName,
        uid,
        className,
        auditCommentInfo,
        id,
      },
      index
    ) => {
      return {
        id,
        auditCommentInfo: JSON.parse(auditCommentInfo),
        uid,
        index: index + 1,
        className,
        offset: Number(position),
        text: content,
        desc: {
          configKey: `${tagId}`,
          configValue: `${tagCode}`,
          tagName: tagName || '',
        },
        tagName: tagName || '',
      } as markConfigItemType
    }
  )
}

export function getMenuPosition(
  outerEle: HTMLElement,
  innerEle: HTMLElement
): string {
  const outerBCR = outerEle.getBoundingClientRect()
  const innerBCR = innerEle.getBoundingClientRect()
  // if (270 < innerBCR.width) {
  //   return 'left'
  // }
  if (270 >= outerBCR.left + outerBCR.width - innerBCR.left - innerBCR.width) {
    return 'right'
  }
  return 'left'
}

export async function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => {
    const timer = setTimeout(() => {
      resolve()
      clearTimeout(timer)
    }, ms)
  })
}

export const downloadFile = (res, filename) => {
  // const { headers, data } = res
  // const disposition = headers['content-disposition']
  // console.log(disposition)
  // const filename = disposition.split('=')[1].replace(/\\\\"/g, '')
  // let blob = new Blob([res.data], {
  //   type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
  // })
  const url = window.URL.createObjectURL(res)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  link.remove()
  window.URL.revokeObjectURL(url)
}

export function getEnumStr({ enumValues }) {
  const s = '枚举值<br>'
  if (enumValues?.length === 0) return s
  const str =
    enumValues?.reduce((prev, { key, value }) => {
      prev += `${value}: ${key} ； `
      return prev
    }, s) || s
  return `<div style="max-width: 580px">${str}</div>`
}
