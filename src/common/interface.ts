import type { markConfigItemBackendType } from '@/hooks/JsMark/common/interface'
export type NumberOrString = number | string

export type IOptionsType = {
  label: string
  value: NumberOrString
}

export interface MarkDetailType {
  userAnnoId: string
  applicationId: string | number
  applicationName: string
  annoTaskName: string
  sampleBusiId: string
  annoStatusName: string
  sampleContent: string
  annotatorName: string
  annoStatus: number
  code?: number
  msg?: string
  userAnnoDetails: markConfigItemBackendType[]
  serviceTypeId?: number
  sampleType: number
  tagShowMethod: number
  appId: string
  serviceId: string
}

export interface DialogueType {
  id: string
  r: string
  s: string
  t: string
  r_t: string | number
  addi?: string
  ls: {
    r: string
    s: string
    t: string
    r_t: string | number
  }[]
}

export interface markReqItemType {
  position: number
  content: string
  tagId: string
  tagCode: string | number
  tagName?: string
  id?: number
  uid: string
  className: string
  auditCommentInfo?: string
  frontProperties?: string
}

export interface ListBaseQueryType {
  page: number
  size: number
}

export interface TagsPageListQueryType extends ListBaseQueryType {
  applicationId?: number | ''
  tageId?: number | ''
  itemId: number | ''
  keyword: string
  serviceId?: string
  appId?: string
}

export type ListResponseType<T> = Promise<{
  currentPage: number
  pageSize: number
  total: number
  elements: T[]
}>
export type PromptListResponseType<T> = Promise<{
  currentPage: number
  pageSize: number
  total: number
  list: T[]
}>

export interface PromptListResType {
  id: number // 主键
  flowName: string // 流名称
  flowChain: string // 流链子
  flowStatus: string // 流的状态
  createName: string // 创建人姓名
  updateName: string // 更新人姓名
  updateTime: string // 更新时间
  createTime: string // 修改时间
}
export interface TagsListResType {
  id: number // 主键
  applicationId: number // 所属应用
  updateTime: string // 更新时间
  tagName: string // 项名称
  tagDesc: string // 项描述
  typicalSample: string // 典型样例
  itemId: number | string // 对应值的关联id
  itemType: number // 对应值的关联类型
  applicationName: string // 所属应用名字
  creatorName: string // 创建人姓名
  itemNameStr: string // 对应值字符串
  status: number // 项状态 0 有效
}

export type SelectListItemType = {
  key: number | string
  value: string
}

export interface PromptListItemType extends ListBaseQueryType {
  // applicationId?: number | ''
  // tageId?: number | ''
  // itemId: number | ''
  // keyword: string
  // serviceId?: string
  // appId?: string
  flowTaskStatuses: string
}
export interface newTagReqType {
  id?: NumberOrString // 主键
  applicationId: NumberOrString // 所属应用
  status: NumberOrString // 状态
  tagName: string // 项名称
  tagDesc: string // 项描述
  typicalSample: string // 典型样例
  itemId: NumberOrString // 关联的选项系统ID
  tagBusiId: NumberOrString // 关联的业务系统ID
  tagNameEn: NumberOrString // 质检项英文名称
}

/*
 * 枚举值类型
 * LIMITED_DROPDOWN(0, "下拉列表"),
 * USER_INPUT_STRING(1, "用户输入字符串"),
 * USER_INPUT_NUMBER(2, "用户输入数字"),
 * INTERFACE_BRAND(6, "品牌接口"),
 * INTERFACE_SERIES(7, "车系接口"),
 * INTERFACE_AREA(8, "地域接口"),
 * TAG(9, "标签"),
 * INTERFACE_MOTO_SERIES(10, "摩托车车系"),
 * INTERFACE_MOTO_BRAND(11, "摩托车品牌"),
 * INTERFACE_BAIKE(12, "百科"),
 * */

// 枚举值类型
export type EnumItemType = 0 | 1 | 2 | 6 | 7 | 8 | 9 | 10 | 11 | 12

export interface newItemReqType {
  itemType: EnumItemType | '' // 选项类型
  itemName: string // 选项名称
  itemContent: string // 内容，如果是下拉项 则是 序列化的 List<DropDown>
  itemContentAbbr?: string // 描述
}

export type CommonDropDownType = 1 | 2 | 4 | 5 | 6 | 7 | 8 // 1 所属应用 2 执行状态 4 对应服务 5 任务进度  6 标注状态 7 导入批次 8 标签配置类型

export interface AuddtingSearchType extends ListBaseQueryType {
  sampleBusiId?: string // 样本业务ID
  annoStatus?: string | number // 标注状态
  applicationId?: string // 所属应用ID
  taskIdNameInVague?: string // 所属任务
  annoUserDomain?: string // 标注人员域账户
  submitTimeBeginEnd?: string // 提交审批开始结束时间
  purposeId: number // 样本用途      UNKNOWN(-1, "空"),//就是空 JIU_CUO(1, "训练集"),ZHI_JIAN(2, "评测集"),
}

export interface AuditingResponseType {
  id: number // 主键
  annoMethod: number // 标注方式
  annoTaskId: number // 标注任务ID
  userDomain: string // 标注人域账户
  userName: string // 标注人姓名
  sampleId: number // 样本ID
  sampleType: number // 样本类型
  annoStatus: number // 标注状态
  finishTime: string // 完成时间
  annoTaskName: string // 任务名称
  sampleBusiId: string // 样本业务ID
  applicationId: number // 所属应用名称
  applicationName: string // 所属应用名称
  serviceType: number // 对应服务ID
  serviceTypeName: string // 对应服务名称
  annoStatusName: string // 标注状态名称
}

export interface AxiosErrorResType {
  msg: string
  code: number
}

export type PromiseResType = {
  records?: any
} & AxiosErrorResType

export type PromiseStrMsgType = Promise<string | AxiosErrorResType>

export type PagReqType = {
  current: number
  size: number
}

export type TaskCreateReqType = {
  id?: NumberOrString // 任务ID
  taskName: string // 任务名称
  sampleSource: NumberOrString // 样本来源 1:问题库 2:标注
  evaluationSample: NumberOrString // 评测样本
  evaluationSampleName?: string // 评测样本名称
  sampleData: string // 样本数据
  sampleDataName?: string // 样本数据名称
  application?: string // 1:问题库、2:AIC、3:智能助手、4:IM
  appId: NumberOrString // 1:问题库、2:AIC、3:智能助手、4:IM
  evaluationTypeId: NumberOrString // 1:AIC意图评测,2:实体评测,3:模型答案评测,4:IM意图评测
  comparisonType: number // 对比类型 1:模型对比样本
  taskDescription: string // 任务描述
  taskStatus?: number // 任务状态 1:待执行 2:执行中 3:已完成 4:已取消
  username: string // 操作人
}

export type TaskListReqType = PagReqType & {
  appId?: NumberOrString // 1:问题库、2:AIC、3:智能助手、4:IM
  evaluationType?: string // 意图、实体
  taskName?: string // 任务名称
  username?: string // 创建人
  startDate?: string // 创建时间
  endDate?: string // 创建时间
  createAts?: string // 创建时间
}

export type TaskDetailListReqType = PagReqType & {
  comparisonType?: number // 对比类型 1:模型对比样本
  consistencyList?: string // 对比结果 0:不一致 1:一致 2:包含
  evaluationTaskId?: NumberOrString // 评测任务ID
}

export type TaskResListType = PagReqType & {
  taskId: NumberOrString // 任务ID
}

export type CorrectionReqType = {
  detailId: number //明细ID
  evaluationTypeId: string //意图、实体
  correction: string //修正内容
}

export type SampleListResType = {
  id: number // 主键
  name: string // 名称
  evaluationType?: string // 意图、实体
}

export type PagListResType<T> = {
  total: number // 总数
  records: T[] // 数据
}

export type TaskPagListResType = {
  createTime?: string // 创建时间
  id?: number // 评测任务ID
  taskStatus?: number
  taskName?: string // 任务名称
  application?: string // 所属应用
  appId?: string // 所属应用ID
  evaluationType?: string // 意图评测
  comparisonType?: number
  taskDescription?: string // 任务描述
  username?: string // 创建用户
}

export type ModelAnswerListType = {
  modelName: string
  modelId: number
  modelAnswer: string
}

export type DetailColumnsType = {
  value: string
  label: string
  selected: boolean
  width?: string
  type: '1' | '2' | '3'
}

export type TaskDetailPagListResType = {
  id: number // 主键
  evaluationTaskId: number // 评测任务ID
  modelTaskId: number // 模型任务ID
  contentId: number // 问题ID
  content: string // 问题内容
  comparisonType: number // 对比类型 1：模型对比样本 2:模型对比标注
  standardBatchId: number // 标准答案批次版本ID
  standardIntentionId: string // 意图ID
  standardIntentionName: string // 意图名称
  standardEntityName: string // 实体名称
  compareBatchId: number // 对比批次版本ID
  compareIntentionId: string // 意图ID
  compareIntentionName: string // 模型意图名称
  compareEntityName: string // 模型实体名称
  intentConsistency: number // 意图一致性 0:不一致 1:一致 2:包含
  entityConsistency: number // 实体一致性 0:不一致 1:一致 2:包含
  intentCorrection: string // 意图修正结果
  entityCorrection: string // 实体修正结果
  // 多模型对比
  questionContentId: number // 问题ID
  questionContent: string // 问题内容
  referenceAnswer: string // 参考答案
  bestModelName: number // 最优解批次
  reason: string // 原因
  modelAnswerList: ModelAnswerListType[]
}

export type TaskListResType = {
  id: number // 主键
  taskId: number // 评测任务ID
  evaluationName: string // 评测名称
  evaluationType: string // 评测类型
  standardAnswersNum: number // 标准答案数量
  compareAnswersNum: number // 对比答案数量
  consistentNum: number // 一致数量
  containNum: number // 包含数量
  inconsistentNum: number // 不一致数量
  accuracyRate: number // 正确率
  errorRate: number // 错误率
  precisionRate: number // 精准率
  recallRate: number // 召回率
  f1: number // f1
}

export type PushQuestionReqType = {
  evaluationTaskId: NumberOrString // 推送任务ID
  pushRange: NumberOrString // 1:全量 2:不一致
  pushType: NumberOrString // 1:更新原问题集 2:其他问题集
  newCollId?: NumberOrString // pushType为2时传值
}
