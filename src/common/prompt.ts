import { filter } from 'lodash-es';
export const appTypeList = (step) => {

  const list = [
    {
      key: '1',
      value: 'EXCEL_IMPORT',
      label: 'Excel文件',
    },
    {
      key: '2',
      value: 'QUESTION_HUB',
      label: '问题库',
    },
    {
      key: '3',
      value: 'INTERFACE',
      label: '自定义接口',
    },
    {
      key: '4',
      value: 'PROMPT',
      label: 'Prompt',
    },

    {
      key: '6',
      value: 'SIMPLE_CALC',
      label: '简单计算',
    },

  ];

  if (step === 1) {
    return list.filter((item) => ['1', '2', '3'].includes(item.key));
  }
  return list.filter((item) => ['4', '6', '3'].includes(item.key));
}

export const checkOptions = [
  {
    label: '问题ID',
    value: '1',
  },
  {
    label: '问题内容',
    value: '2',
  },
  {
    label: '问题标签',
    value: '3',
  },
  {
    label: '问题意图ID',
    value: '4',
  },
  {
    label: '问题意图名称',
    value: '5',
  },
  {
    label: '参考答案',
    value: '6',
  },
  {
    label: '问题实体名称',
    value: '7',
  },
]