<template>
  <el-menu router :default-active="defaultActive" class="el-menu-vertical-demo">
    <template v-for="item in MenuList">
      <el-sub-menu
        :key="item.name"
        v-if="item.children && item.children.length"
        :index="item.name"
      >
        <template #title>
          <el-icon>
            <component :is="item.icon"></component>
          </el-icon>
          <span>{{ item.meta?.title }}</span>
        </template>

        <template :key="childItem.name" v-for="childItem in item.children">
          <el-menu-item :route="childItem" :index="childItem.name">
            <span>{{ childItem.meta?.title }}</span>
          </el-menu-item>
        </template>
      </el-sub-menu>

      <el-menu-item :key="item!.name" v-else :index="item.name">
        <el-icon>
          <component :is="item.icon"></component>
        </el-icon>
        <span>{{ item.meta?.title }}</span>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { MenuList } from '@/router/routes'

const route: any = useRoute()
const router = useRouter()
const defaultActive = ref<any>()
watch(route, (n) => {
  defaultActive.value = n.name
})

onMounted(async () => {
  if (route.path !== '/') {
    defaultActive.value = route.name
  }
})
</script>
