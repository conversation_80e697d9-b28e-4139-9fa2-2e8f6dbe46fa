<template>
  <el-tabs
    v-model="that.activeName"
    @tab-click="tabsClick"
    :before-leave="beforeLeave"
  >
    <template v-for="item in props.data" :key="item.name">
      <el-tab-pane
        :label="item.label"
        :name="item.name"
        :disabled="!!item.disabled"
      ></el-tab-pane>
    </template>
  </el-tabs>
</template>
<script setup lang="ts">
import { reactive } from 'vue'
import { useRouter } from 'vue-router'

import { MarkTaskPoint, MarkTaskTabs } from '@/common/constants'

import { useUserStore } from '@/store/useUserStore'

const store = useUserStore()
const Router = useRouter()

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  value: {
    type: String,
    default: '',
  },
})

const that = reactive({
  activeName: props.value,
})

// function haveAuth({ path }) {
//   let code = RoutePathCodeEnum[path]
//   return store.permissionCode.includes(code)
// }

function beforeLeave(paneName) {
  if (paneName === '21' && props.value === '22') return false
  if (paneName === '22' && props.value === '21') return false
  if (paneName === '23') return false
}

function tabsClick({ paneName }) {
  if (['21', '22'].includes(paneName) && MarkTaskPoint[paneName]) {
    window.$SDK_ALL.sendPageEvent({
      event_id: 'lantu_click', // 业务字段x
      data: {
        button: 'taskManage', // 业务字段
        card: MarkTaskPoint[paneName],
      },
    })
  }

  if (paneName === '23') return
  let { path } = MarkTaskTabs.find((item) => item.name === paneName) || {}
  if (!path) return
  Router.replace({
    path,
  })
}
</script>
