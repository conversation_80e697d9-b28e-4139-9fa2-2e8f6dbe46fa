{"version": 3, "sources": ["webpack://Highlighter/webpack/universalModuleDefinition", "webpack://Highlighter/webpack/bootstrap", "webpack://Highlighter/./src/util/const.ts", "webpack://Highlighter/./src/types/index.ts", "webpack://Highlighter/./src/util/event.emitter.ts", "webpack://Highlighter/./src/model/source/index.ts", "webpack://Highlighter/./src/util/dom.ts", "webpack://Highlighter/./src/model/range/index.ts", "webpack://Highlighter/./src/util/uuid.ts", "webpack://Highlighter/./src/index.ts", "webpack://Highlighter/./src/model/source/dom.ts", "webpack://Highlighter/./src/util/camel.ts", "webpack://Highlighter/./src/model/range/selection.ts", "webpack://Highlighter/./src/model/range/dom.ts", "webpack://Highlighter/./src/util/hook.ts", "webpack://Highlighter/./src/util/interaction.ts", "webpack://Highlighter/./src/util/is.mobile.ts", "webpack://Highlighter/./src/data/cache.ts", "webpack://Highlighter/./src/painter/index.ts", "webpack://Highlighter/./src/painter/dom.ts", "webpack://Highlighter/./src/util/tool.ts", "webpack://Highlighter/./src/painter/style.ts"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "ID_DIVISION", "LOCAL_STORE_KEY", "STYLESHEET_ID", "DATASET_IDENTIFIER", "DATASET_IDENTIFIER_EXTRA", "DATASET_SPLIT_TYPE", "CAMEL_DATASET_IDENTIFIER", "CAMEL_DATASET_IDENTIFIER_EXTRA", "CAMEL_DATASET_SPLIT_TYPE", "getDefaultOptions", "$root", "document", "documentElement", "rootDocument", "exceptSelectors", "wrapTag", "verbose", "style", "className", "window", "getStylesh<PERSON>t", "ROOT_IDX", "UNKNOWN_IDX", "INTERNAL_ERROR_EVENT", "eventEmitter", "ErrorEventEmitter", "SplitType", "ERROR", "EventType", "CreateFrom", "SelectedNodeType", "UserInputEvent", "handlersMap", "on", "type", "handler", "push", "off", "splice", "indexOf", "emit", "slice", "for<PERSON>ach", "data", "EventEmitter", "startMeta", "endMeta", "text", "id", "extra", "__isHighlightSource", "deSerialize", "hooks", "queryElementNode", "start", "end", "startInfo", "getTextChildByOffset", "textOffset", "endInfo", "Serialize", "Rest<PERSON>", "isEmpty", "res", "HighlightSource", "isHighlightWrapNode", "$node", "dataset", "findAncestorWrapperInRoot", "isInsideRoot", "$wrapper", "parentNode", "getHighlightId", "getExtraHighlightId", "split", "filter", "getHighlightsByRoot", "$roots", "Array", "isArray", "$wraps", "$list", "querySelectorAll", "apply", "getHighlightById", "$highlights", "reg", "RegExp", "$n", "extraId", "test", "$nodes", "cb", "length", "removeEventListener", "$el", "evt", "fn", "addEventListener", "addClass", "classList", "add", "removeClass", "remove", "removeAllClass", "hasClass", "contains", "frozen", "formatDomNode", "fromSelection", "idHook", "range", "getDomRange", "startContainer", "offset", "startOffset", "endContainer", "endOffset", "toString", "HighlightRange", "serialize", "getDomMeta", "RecordInfo", "removeDomRange", "removeSelection", "createUUID", "a", "Math", "random", "replace", "options", "run", "event", "PointerEnd", "_handleSelection", "stop", "getDoms", "getIdByDom", "getExtraIdByDom", "dispose", "PointerOver", "_handleHighlightHover", "PointerTap", "_handleHighlightClick", "removeAll", "setOption", "painter", "fromRange", "Render", "UUID", "h<PERSON><PERSON><PERSON>", "_highlightFromHRange", "RANGE_INVALID", "fromStore", "hs", "_highlightFromHSource", "err", "HIGHLIGHT_SOURCE_RECREATE", "error", "detail", "_getHooks", "SelectedNodes", "WrapNode", "Remove", "UpdateNodes", "source", "highlightRange", "DOM_SELECTION_EMPTY", "cache", "save", "CREATE", "sources", "INPUT", "e", "$target", "target", "_hoverId", "HOVER_OUT", "HOVER", "_handleError", "console", "warn", "CLICK", "doseExist", "removeHighlight", "REMOVE", "ids", "remove<PERSON>ll<PERSON>ighlight", "renderedSources", "highlightSource", "STORE", "isHighlightSource", "Highlighter", "$parent", "nodeStack", "$curNode", "curOffset", "pop", "children", "childNodes", "nodeType", "textContent", "parentIndex", "getElementsByTagName", "parentTagName", "reduce", "str", "idx", "toUpperCase", "selection", "getSelection", "isCollapsed", "debug", "getRangeAt", "removeAllRanges", "$originParent", "HTMLElement", "get<PERSON><PERSON>in<PERSON><PERSON>nt", "index", "tagName", "countGlobalNodeIndex", "preNodeOffset", "$text", "getTextPreOffset", "getClosestTextNode", "node", "TEXT_NODE", "closestTextNode", "ops", "tap", "ret", "op", "args", "Hook", "isMobile", "navigator", "userAgent", "touchend", "mouseup", "touchstart", "click", "mouseover", "regMobile", "_data", "Map", "getAll", "map", "CACHE_SET_ERROR", "set", "delete", "list", "pair", "<PERSON><PERSON>", "initDefaultStylesheet", "HIGHLIGHT_RANGE_FROZEN", "$selectedNodes", "getSelectedNodes", "wrapHighlight", "HIGHLIGHT_SOURCE_NONE_RENDER", "SOURCE_TYPE_ERROR", "$spans", "$toRemove", "$idToUpdate", "$extraToUpdate", "$s", "spanId", "spanExtraIds", "$fr", "createDocumentFragment", "$c", "append<PERSON><PERSON><PERSON>", "cloneNode", "$prev", "previousSibling", "$next", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON>", "normalizeSiblingText", "newId", "shift", "$overlapSpan", "querySelector", "join", "extraIds", "normalize", "isMatchSelector", "selector", "$startNode", "$endNode", "defaultView", "Text", "$element", "isExcepted", "$e", "some", "splitText", "passedNode", "splitType", "both", "getNodesIfSameStartEnd", "selectedNodes", "withinSelectedRange", "curNode", "head", "tail", "trim", "none", "classNames", "isNodeEmpty", "selected", "$wrap", "formerId", "wrapOverlapNode", "createElement", "parentId", "parentExtraId", "extraInfo", "setAttribute", "headSplit", "tailSplit", "$span", "classNameList", "unique", "wrapPartialNode", "wrapNewNode", "isNext", "$sibling", "nodeValue", "<PERSON><PERSON><PERSON><PERSON>", "arr", "el", "styleId", "$style", "getElementById", "$cssNode", "createTextNode"], "mappings": ";CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAqB,YAAID,IAEzBD,EAAkB,YAAIC,IARxB,CASGK,MAAM,WACT,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,G,o7BC3ErD,eACA,UAEa,EAAAC,YAAc,IACd,EAAAC,gBAAkB,qBAClB,EAAAC,cAAgB,2BAEhB,EAAAC,mBAAqB,eACrB,EAAAC,yBAA2B,qBAC3B,EAAAC,mBAAqB,uBACrB,EAAAC,0BAA2B,aAAM,EAAAH,oBACjC,EAAAI,gCAAiC,aAAM,EAAAH,0BACvC,EAAAI,0BAA2B,aAAM,EAAAH,oBAIjC,EAAAI,kBAAoB,WAAM,OACnCC,MAAOC,UAAYA,SAASC,gBAC5BC,aAAcF,SACdG,gBAAiB,KACjBC,QANqB,OAOrBC,SAAS,EACTC,MAAO,CACHC,UAAW,kBAEfC,OAAM,SAGG,EAAAC,cAAgB,WAAM,wBAC5B,IAAAX,qBAAoBQ,MAAMC,UAAS,4BAEnC,IAAAT,qBAAoBQ,MAAMC,UAAS,kDAK7B,EAAAG,UAAY,EACZ,EAAAC,aAAe,EACf,EAAAC,qBAAuB,QAMpC,+B,+CAA+D,OAA/B,OAA+B,EAA/D,CAAgC,WAEnB,EAAAC,aAAe,IAAIC,G,6BC7BhC,IAAYC,EAOAC,EAcAC,EASAC,EAKAC,EAiDAC,E,yIApFZ,SAAYL,GACR,cACA,cACA,cACA,cAJJ,CAAYA,IAAS,YAATA,EAAS,KAOrB,SAAYC,GACR,kDACA,0FACA,2FACA,2FACA,sDACA,kFACA,yEACA,6FACA,yFAEA,0JAXJ,CAAYA,IAAK,QAALA,EAAK,KAcjB,SAAYC,GACR,4BACA,4BACA,4BACA,0BACA,kCACA,0BANJ,CAAYA,IAAS,YAATA,EAAS,KASrB,SAAYC,GACR,qBACA,qBAFJ,CAAYA,IAAU,aAAVA,EAAU,KAKtB,SAAYC,GACR,cACA,cAFJ,CAAYA,IAAgB,mBAAhBA,EAAgB,KAiD5B,SAAYC,GACR,sBACA,oBACA,0BACA,gBACA,wBALJ,CAAYA,IAAc,iBAAdA,EAAc,M,2lBChG1B,8BACY,KAAAC,YAA8BtD,OAAOY,OAAO,MA6BxD,OA3BI,YAAA2C,GAAA,SAAsBC,EAASC,GAO3B,OANKvE,KAAKoE,YAAYE,KAClBtE,KAAKoE,YAAYE,GAAQ,IAG7BtE,KAAKoE,YAAYE,GAAME,KAAKD,GAErBvE,MAGX,YAAAyE,IAAA,SAAuBH,EAASC,GAK5B,OAJIvE,KAAKoE,YAAYE,IACjBtE,KAAKoE,YAAYE,GAAMI,OAAO1E,KAAKoE,YAAYE,GAAMK,QAAQJ,KAAa,EAAG,GAG1EvE,MAGX,YAAA4E,KAAA,SAAwBN,G,IAAS,wDAO7B,OANItE,KAAKoE,YAAYE,IACjBtE,KAAKoE,YAAYE,GAAMO,QAAQC,SAAQ,SAAAP,GACnCA,EAAO,oBAAIQ,IAAI,OAIhB/E,MAEf,EA9BA,GAgCA,UAAegF,G,kKCrCf,cACA,OAEA,aAaI,WAAYC,EAAoBC,EAAkBC,EAAcC,EAAYC,GACxErF,KAAKiF,UAAYA,EACjBjF,KAAKkF,QAAUA,EACflF,KAAKmF,KAAOA,EACZnF,KAAKoF,GAAKA,EACVpF,KAAKsF,oBAAsB,GAEvBD,IACArF,KAAKqF,MAAQA,GAoBzB,OAhBI,YAAAE,YAAA,SAAYzC,EAA+B0C,GACjC,OAAiB,IAAAC,kBAAiBzF,KAAM8C,GAAtC4C,EAAK,QAAEC,EAAG,MACdC,GAAY,IAAAC,sBAAqBH,EAAO1F,KAAKiF,UAAUa,YACvDC,GAAU,IAAAF,sBAAqBF,EAAK3F,KAAKkF,QAAQY,YAErD,IAAKN,EAAMQ,UAAUC,QAAQC,UAAW,CACpC,IAAMC,EAAiBX,EAAMQ,UAAUC,QAAQ1F,KAAKP,KAAM4F,EAAWG,IAAY,GAEjFH,EAAYO,EAAI,IAAMP,EACtBG,EAAUI,EAAI,IAAMJ,EAKxB,OAFc,IAAI,UAAeH,EAAWG,EAAS/F,KAAKmF,KAAMnF,KAAKoF,IAAI,IAIjF,EAzCA,GA2CA,UAAegB,G,inCCpDf,WAUa,EAAAC,oBAAsB,SAACC,GAChC,QAAEA,EAAMC,WAAaD,EAAMC,QAAQ,EAAA7D,2BAUvC,IAAM8D,EAA4B,SAACF,EAAoBxD,GAInD,IAHA,IAAI2D,GAAe,EACfC,EAAwB,KAErBJ,GAAO,CAKV,IAJI,IAAAD,qBAAoBC,KACpBI,EAAWJ,GAGXA,IAAUxD,EAAO,CACjB2D,GAAe,EACf,MAGJH,EAAQA,EAAMK,WAGlB,OAAOF,EAAeC,EAAW,MAMxB,EAAAE,eAAiB,SAACN,EAAoBxD,GAG/C,OAFAwD,EAAQE,EAA0BF,EAAOxD,IAMlCwD,EAAMC,QAAQ,EAAA7D,0BAHV,IASF,EAAAmE,oBAAsB,SAACP,EAAoBxD,GAGpD,OAFAwD,EAAQE,EAA0BF,EAAOxD,IAMlCwD,EAAMC,QAAQ,EAAA5D,gCAAgCmE,MAAM,EAAA1E,aAAa2E,QAAO,SAAA3G,GAAK,OAAAA,KAHzE,IASF,EAAA4G,oBAAsB,SAACC,EAAqC9D,G,QAChE+D,MAAMC,QAAQF,KACfA,EAAS,CAACA,IAGd,IAAMG,EAAwB,G,IAE9B,IAAiB,QAAAH,GAAM,8BAAE,CAApB,IACKI,EADG,QACQC,iBAA8B,UAAGnE,EAAO,iBAAS,EAAAZ,mBAAkB,MAGpF6E,EAAO5C,KAAK+C,MAAMH,EAAQC,I,iGAG9B,OAAOD,GAME,EAAAI,iBAAmB,SAAC1E,EAAoBsC,EAAYjC,G,QACvDsE,EAA6B,GAC7BC,EAAM,IAAIC,OAAO,WAAIvC,EAAE,aAAK,EAAAhD,YAAW,cAAM,EAAAA,YAAW,YAAIgD,EAAE,OAC9DiC,EAAQvE,EAAMwE,iBAA8B,UAAGnE,EAAO,iBAAS,EAAAZ,mBAAkB,M,IAEvF,IAAiB,QAAA8E,GAAK,8BAAE,CAAnB,IACKO,EADG,QAIT,GAFYA,EAAGrB,QAAQ,EAAA7D,4BAEX0C,EAAZ,CAKA,IAAMyC,EAAUD,EAAGrB,QAAQ,EAAA5D,gCAEvB+E,EAAII,KAAKD,IACTJ,EAAYjD,KAAKoD,QAPjBH,EAAYjD,KAAKoD,I,iGAYzB,OAAOH,GAGE,EAAA3C,QAAU,SAACiD,EAAkBC,GACtC,IAAK,IAAI5H,EAAI,EAAGA,EAAI2H,EAAOE,OAAQ7H,IAC/B4H,EAAGD,EAAO3H,GAAIA,EAAG2H,IAIZ,EAAAG,oBAAsB,SAACC,EAAkBC,EAAaC,GAC/DF,EAAID,oBAAoBE,EAAKC,IAOpB,EAAAC,iBAAmB,SAACH,EAAkBC,EAAaC,GAG5D,OAFAF,EAAIG,iBAAiBF,EAAKC,GAEnB,YACH,IAAAH,qBAAoBC,EAAKC,EAAKC,KAIzB,EAAAE,SAAW,SAACJ,EAAkB7E,G,MAClC4D,MAAMC,QAAQ7D,KACfA,EAAY,CAACA,KAGjB,EAAA6E,EAAIK,WAAUC,IAAG,eAAInF,IAAS,KAGrB,EAAAoF,YAAc,SAACP,EAAkB7E,GAC1C6E,EAAIK,UAAUG,OAAOrF,IAGZ,EAAAsF,eAAiB,SAACT,GAC3BA,EAAI7E,UAAY,IAGP,EAAAuF,SAAW,SAACV,EAAkB7E,GAA+B,OAAA6E,EAAIK,UAAUM,SAASxF,K,kKClJjG,cAEA,QACA,UACA,QAGA,aAaI,WAAYoC,EAAgBC,EAAcR,EAAcC,EAAY2D,QAAA,IAAAA,OAAA,GAChE/I,KAAK0F,OAAQ,IAAAsD,eAActD,GAC3B1F,KAAK2F,KAAM,IAAAqD,eAAcrD,GACzB3F,KAAKmF,KAAOA,EACZnF,KAAK+I,OAASA,EACd/I,KAAKoF,GAAKA,EA2ClB,OAxCW,EAAA6D,cAAP,SAAqBC,EAAsBjG,GACvC,IAAMkG,GAAQ,IAAAC,aAAYnG,GAE1B,IAAKkG,EACD,OAAO,KAGX,IAAMzD,EAAiB,CACnBY,MAAO6C,EAAME,eACbC,OAAQH,EAAMI,aAEZ5D,EAAe,CACjBW,MAAO6C,EAAMK,aACbF,OAAQH,EAAMM,WAGZtE,EAAOgE,EAAMO,WACftE,EAAK8D,EAAO3I,KAAKmF,EAAOC,EAAKR,GAIjC,OAAO,IAAIwE,EAAejE,EAAOC,EAAKR,EAFtCC,EAAK,MAAOA,EAAoCA,GAAK,iBAOzD,YAAAwE,UAAA,SAAU9G,EAA+B0C,GACrC,IAGIH,EAHEJ,GAAY,IAAA4E,YAAW7J,KAAK0F,MAAMY,MAAetG,KAAK0F,MAAM4D,OAAQxG,GACpEoC,GAAU,IAAA2E,YAAW7J,KAAK2F,IAAIW,MAAetG,KAAK2F,IAAI2D,OAAQxG,GAUpE,OANK0C,EAAMQ,UAAU8D,WAAW5D,YAC5Bb,EAAQG,EAAMQ,UAAU8D,WAAWvJ,KAAKP,KAAK0F,MAAO1F,KAAK2F,IAAK7C,IAGlE9C,KAAK+I,QAAS,EAEP,IAAI,UAAgB9D,EAAWC,EAASlF,KAAKmF,KAAMnF,KAAKoF,GAAIC,IA1DhE,EAAA0E,eAAiB,EAAAC,gBA4D5B,EA7DA,GA+DA,UAAeL,G,8ECzEf,mBAAwBM,EAAWC,GAC/B,OAAOA,GACAA,EAAsB,GAAhBC,KAAKC,UAAmBF,EAAI,GAAKR,SAAS,KAC9C,CAAC,MAA+B,KAAO,KAAO,KAAO,MAAMW,QAAQ,SAAUJ,K,g4BCP1F,cACA,UACA,UACA,UACA,WACA,WACA,WACA,WACA,OACA,OACA,OAqBA,cAiBI,WAAYK,GACR,QAAK,YAAE,KAuBX,EAAAC,IAAM,WAAM,WAAAjC,kBAAiB,EAAKgC,QAAQxH,MAAO,EAAK0H,MAAMC,WAAY,EAAKC,mBAE7E,EAAAC,KAAO,YACH,IAAAzC,qBAAoB,EAAKoC,QAAQxH,MAAO,EAAK0H,MAAMC,WAAY,EAAKC,mBAGxE,EAAAnC,SAAW,SAACjF,EAAmB8B,GAC3B,EAAKwF,QAAQxF,GAAIN,SAAQ,SAAA8C,IACrB,IAAAW,UAASX,EAAItE,OAIrB,EAAAoF,YAAc,SAACpF,EAAmB8B,GAC9B,EAAKwF,QAAQxF,GAAIN,SAAQ,SAAA8C,IACrB,IAAAc,aAAYd,EAAItE,OAIxB,EAAAuH,WAAa,SAACvE,GAA+B,WAAAM,gBAAeN,EAAO,EAAKgE,QAAQxH,QAEhF,EAAAgI,gBAAkB,SAACxE,GAAiC,WAAAO,qBAAoBP,EAAO,EAAKgE,QAAQxH,QAE5F,EAAA8H,QAAU,SAACxF,GACP,OAAAA,GACM,IAAAoC,kBAAiB,EAAK8C,QAAQxH,MAAOsC,EAAI,EAAKkF,QAAQnH,UACtD,IAAA6D,qBAAoB,EAAKsD,QAAQxH,MAAO,EAAKwH,QAAQnH,UAE/D,EAAA4H,QAAU,WACN,IAAMjI,EAAQ,EAAKwH,QAAQxH,OAE3B,IAAAoF,qBAAoBpF,EAAO,EAAK0H,MAAMQ,YAAa,EAAKC,wBACxD,IAAA/C,qBAAoBpF,EAAO,EAAK0H,MAAMC,WAAY,EAAKC,mBACvD,IAAAxC,qBAAoBpF,EAAO,EAAK0H,MAAMU,WAAY,EAAKC,uBACvD,EAAKC,aAGT,EAAAC,UAAY,SAACf,GACT,EAAKA,QAAU,EAAH,KACL,EAAKA,SACLA,GAEP,EAAKgB,QAAU,IAAI,UACf,CACIxI,MAAO,EAAKwH,QAAQxH,MACpBG,aAAc,EAAKqH,QAAQrH,aAC3BE,QAAS,EAAKmH,QAAQnH,QACtBG,UAAW,EAAKgH,QAAQjH,MAAMC,UAC9BJ,gBAAiB,EAAKoH,QAAQpH,iBAElC,EAAKsC,QAIb,EAAA+F,UAAY,SAACpC,GACT,IAAMzD,EAAiB,CACnBY,MAAO6C,EAAME,eACbC,OAAQH,EAAMI,aAEZ5D,EAAe,CACjBW,MAAO6C,EAAMK,aACbF,OAAQH,EAAMM,WAGZtE,EAAOgE,EAAMO,WACftE,EAAK,EAAKI,MAAMgG,OAAOC,KAAKlL,KAAKmF,EAAOC,EAAKR,GAEjDC,EAAK,MAAOA,EAAoCA,GAAK,eAErD,IAAMsG,EAAS,IAAI,UAAehG,EAAOC,EAAKR,EAAMC,GAEpD,OAAKsG,EAQE,EAAKC,qBAAqBD,IAP7B,EAAA9H,aAAagB,KAAK,EAAAjB,qBAAsB,CACpCW,KAAM,EAAAP,MAAM6H,gBAGT,OAMf,EAAAC,UAAY,SAACnG,EAAgBC,EAAcR,EAAcC,EAAYC,GACjE,IAAMyG,EAAK,IAAI,UAAgBpG,EAAOC,EAAKR,EAAMC,EAAIC,GAErD,IAGI,OAFA,EAAK0G,sBAAsBD,GAEpBA,EACT,MAAOE,GAOL,OANA,EAAApI,aAAagB,KAAK,EAAAjB,qBAAsB,CACpCW,KAAM,EAAAP,MAAMkI,0BACZC,MAAOF,EACPG,OAAQL,IAGL,OA2BE,EAAAM,UAAY,WAAe,OACxCZ,OAAQ,CACJC,KAAM,IAAI,UAAK,eACfY,cAAe,IAAI,UAAK,wBACxBC,SAAU,IAAI,UAAK,oBAEvBtG,UAAW,CACPC,QAAS,IAAI,UAAK,qBAClB6D,WAAY,IAAI,UAAK,yBAEzByC,OAAQ,CACJC,YAAa,IAAI,UAAK,yBAIb,EAAAb,qBAAuB,SAACxC,GACrC,IAAMsD,EAA0BtD,EAAMS,UAAU,EAAKU,QAAQxH,MAAO,EAAK0C,OAGzE,OAAsB,IAFP,EAAK8F,QAAQoB,eAAevD,GAEhClB,QACP,EAAArE,aAAagB,KAAK,EAAAjB,qBAAsB,CACpCW,KAAM,EAAAP,MAAM4I,sBAGT,OAGX,EAAKC,MAAMC,KAAKJ,GAChB,EAAK7H,KAAK,EAAAZ,UAAU8I,OAAQ,CAAEC,QAAS,CAACN,GAASnI,KAAM,EAAAL,WAAW+I,OAAS,GAEpEP,IAUM,EAAA/B,iBAAmB,WAIhC,IAAMvB,EAAQ,UAAeF,cAAc,EAAKzD,MAAMgG,OAAOC,KAAM,EAAKnB,QAAQrH,cAE5EkG,IACA,EAAKwC,qBAAqBxC,GAC1B,UAAeY,eAAe,EAAKO,QAAQrH,gBAIlC,EAAAgI,sBAAwB,SAACgC,GAAiC,IAAMC,EAAUD,EAAEE,OAEzF,KAAK,IAAA9G,qBAAoB6G,GAIrB,OAHA,EAAKE,UAAY,EAAKxI,KAAK,EAAAZ,UAAUqJ,UAAW,CAAEjI,GAAI,EAAKgI,UAAY,EAAMH,QAC7E,EAAKG,SAAW,MAKpB,IAAMhI,GAAK,IAAAwB,gBAAesG,EAAS,EAAK5C,QAAQxH,OAG5C,EAAKsK,WAAahI,IAKlB,EAAKgI,UACL,EAAKxI,KAAK,EAAAZ,UAAUqJ,UAAW,CAAEjI,GAAI,EAAKgI,UAAY,EAAMH,GAGhE,EAAKG,SAAWhI,EAChB,EAAKR,KAAK,EAAAZ,UAAUsJ,MAAO,CAAElI,GAAI,EAAKgI,UAAY,EAAMH,KAG3C,EAAAM,aAAe,SAACjJ,GACzB,EAAKgG,QAAQlH,SAEboK,QAAQC,KAAKnJ,IAIJ,EAAA6G,sBAAwB,SAAC8B,GACtC,IAAMC,EAAUD,EAAEE,OAElB,IAAI,IAAA9G,qBAAoB6G,GAAU,CAC9B,IAAM9H,GAAK,IAAAwB,gBAAesG,EAAS,EAAK5C,QAAQxH,OAEhD,EAAK8B,KAAK,EAAAZ,UAAU0J,MAAO,CAAEtI,GAAE,GAAI,EAAM6H,KA1O7C,EAAK3C,SAAU,IAAAzH,qBAEf,EAAK2C,MAAQ,EAAK4G,YAClB,EAAKf,UAAUf,GAEf,EAAKE,OAAQ,aAAe,EAAKF,QAAQ/G,QAGzC,EAAKqJ,MAAQ,IAAI,UAEjB,IAAM9J,EAAQ,EAAKwH,QAAQxH,M,OAG3B,IAAAwF,kBAAiBxF,EAAO,EAAK0H,MAAMQ,YAAa,EAAKC,wBAErD,IAAA3C,kBAAiBxF,EAAO,EAAK0H,MAAMU,WAAY,EAAKC,uBACpD,EAAAvH,aAAaS,GAAG,EAAAV,qBAAsB,EAAK4J,c,EA6NnD,OAhQyC,OA4IrC,YAAA5E,OAAA,SAAOvD,GACH,GAAKA,EAAL,CAIA,IAAMuI,EAAY3N,KAAKsL,QAAQsC,gBAAgBxI,GAE/CpF,KAAK4M,MAAMjE,OAAOvD,GAGduI,GACA3N,KAAK4E,KAAK,EAAAZ,UAAU6J,OAAQ,CAAEC,IAAK,CAAC1I,IAAOpF,QAInD,YAAAoL,UAAA,WACIpL,KAAKsL,QAAQyC,qBAEb,IAAMD,EAAM9N,KAAK4M,MAAMxB,YAEvBpL,KAAK4E,KAAK,EAAAZ,UAAU6J,OAAQ,CAAEC,IAAG,GAAI9N,OAoCjC,YAAA+L,sBAAR,SAA8BgB,QAAA,IAAAA,MAAA,IAC1B,IAAMiB,EAAqChO,KAAKsL,QAAQ2C,gBAAgBlB,GAExE/M,KAAK4E,KAAK,EAAAZ,UAAU8I,OAAQ,CAAEC,QAASiB,EAAiB1J,KAAM,EAAAL,WAAWiK,OAASlO,MAClFA,KAAK4M,MAAMC,KAAKE,IAvMb,EAAAvC,MAAQ,EAAAxG,UAER,EAAAqC,oBAAsB,EAAAA,oBAoCtB,EAAA8H,kBAAoB,SAACzN,GAAW,QAAEA,EAAE4E,qBAyN/C,EAhQA,CAAyC,W,UAApB8I,G,+HC9BrB,WAWa,EAAAvI,qBAAuB,SAACwI,EAAe/E,GAOhD,IANA,IAAMgF,EAAoB,CAACD,GAEvBE,EAAiB,KACjBC,EAAY,EACZjF,EAAc,EAEVgF,EAAWD,EAAUG,OAAQ,CAGjC,IAFA,IAAMC,EAAWH,EAASI,WAEjBvO,EAAIsO,EAASzG,OAAS,EAAG7H,GAAK,EAAGA,IACtCkO,EAAU9J,KAAKkK,EAAStO,IAG5B,GAA0B,IAAtBmO,EAASK,WACTrF,EAAcD,EAASkF,GACvBA,GAAaD,EAASM,YAAY5G,SAEjBqB,GACb,MASZ,OAJKiF,IACDA,EAAWF,GAGR,CACH/H,MAAOiI,EACPjF,OAAQC,IAWH,EAAA9D,iBAAmB,SAACqG,EAAqBhJ,GAUlD,MAAO,CAAE4C,MARLoG,EAAG7G,UAAU6J,cAAgB,EAAArL,SACvBX,EACAA,EAAMiM,qBAAqBjD,EAAG7G,UAAU+J,eAAelD,EAAG7G,UAAU6J,aAM9DnJ,IAJZmG,EAAG5G,QAAQ4J,cAAgB,EAAArL,SACrBX,EACAA,EAAMiM,qBAAqBjD,EAAG5G,QAAQ8J,eAAelD,EAAG5G,QAAQ4J,gB,8EC1D9E,mBAAgB5E,GACZ,OAAAA,EAAEpD,MAAM,KAAKmI,QAAO,SAACC,EAAK/M,EAAGgN,GAAQ,OAAAD,GAAe,IAARC,EAAYhN,EAAIA,EAAE,GAAGiN,cAAgBjN,EAAE0C,MAAM,MAAK,M,qHCCrF,EAAAuE,YAAc,SAACnG,GACxB,IAAMoM,EAAYpM,EAAaqM,eAE/B,OAAID,EAAUE,aAEV/B,QAAQgC,MAAM,oBAEP,MAGJH,EAAUI,WAAW,IAGnB,EAAAzF,gBAAkB,SAAC/G,GAC5BA,EAAaqM,eAAeI,oB,ubCgFhC,uBA/FA,WA2Da,EAAA7F,WAAa,SAACvD,EAA2BgD,EAAgBxG,GAClE,IAAM6M,EAfc,SAACrJ,GACrB,GAAIA,aAAiBsJ,eAAiBtJ,EAAMC,UAAYD,EAAMC,QAAQ,EAAA7D,2BAClE,OAAO4D,EAKX,IAFA,IAAIqJ,EAAgBrJ,EAAMK,WAEnBgJ,aAAa,EAAbA,EAAepJ,QAAQ,EAAA7D,2BAC1BiN,EAAgBA,EAAchJ,WAGlC,OAAOgJ,EAIeE,CAAgBvJ,GAChCwJ,EAAQH,IAAkB7M,EAAQ,EAAAW,SA3Df,SAAC6C,EAAaxD,GAIvC,IAHA,IAAMiN,EAAWzJ,EAAsByJ,QACjC1I,EAAQvE,EAAMiM,qBAAqBgB,GAEhC3P,EAAI,EAAGA,EAAIiH,EAAMY,OAAQ7H,IAC9B,GAAIkG,IAAUe,EAAMjH,GAChB,OAAOA,EAIf,OAAO,EAAAsD,YAiD4CsM,CAAqBL,EAAe7M,GACjFmN,EA3Ce,SAACnN,EAAaoN,GAMnC,IALA,IAAM5B,EAAoB,CAACxL,GAEvByL,EAAiB,KACjBjF,EAAS,EAELiF,EAAWD,EAAUG,OAAQ,CAGjC,IAFA,IAAMC,EAAWH,EAASI,WAEjBvO,EAAIsO,EAASzG,OAAS,EAAG7H,GAAK,EAAGA,IACtCkO,EAAU9J,KAAKkK,EAAStO,IAG5B,GAA0B,IAAtBmO,EAASK,UAAkBL,IAAa2B,EACxC5G,GAAUiF,EAASM,YAAY5G,YAC5B,GAA0B,IAAtBsG,EAASK,SAChB,MAIR,OAAOtF,EAuBe6G,CAAiBR,EAAerJ,GAGtD,MAAO,CACH0I,cAHYW,EAAcI,QAI1BjB,YAAagB,EACbhK,WAAYmK,EAAgB3G,IA2BpC,SAAgB8G,EAAmBC,G,QAC/B,GAAIA,EAAKzB,UAAYyB,EAAKC,UACtB,OAAOD,E,IAGX,IAAsB,QAAAA,EAAK1B,YAAU,8BAAE,CAAlC,IACK4B,EAAkBH,EADV,SAGd,GAAwB,OAApBG,GAA4BA,EAAgB3B,UAAYyB,EAAKC,UAC7D,OAAOC,G,iGAIf,OAAO,KApCE,EAAAvH,cAAgB,SAACnH,GAC1B,GAEyB,IAArBA,EAAEyE,MAAMsI,UAEa,IAArB/M,EAAEyE,MAAMsI,UAEa,IAArB/M,EAAEyE,MAAMsI,SAER,OAAO/M,EAIQA,EAAEyE,MAAMqI,WAAW1G,OAASpG,EAAEyH,OAASzH,EAAEyE,MAAMqI,WAAW9M,EAAEyH,QAAUzH,EAAEyE,MAI3F,MAAO,CACHA,MAHoB8J,EAAmBvO,EAAEyE,MAAMqI,WAAW9M,EAAEyH,SAI5DA,OAAQ,K,2lBCzFhB,iBAKI,WAAY3I,GAJZ,KAAAA,KAAO,GAEU,KAAA6P,IAAyB,GAGtCxQ,KAAKW,KAAOA,EAoCpB,OAjCI,YAAA8P,IAAA,SAAIzI,GAAJ,WAKI,OAJ8B,IAA1BhI,KAAKwQ,IAAI7L,QAAQqD,IACjBhI,KAAKwQ,IAAIhM,KAAKwD,GAGX,WACH,EAAKW,OAAOX,KAIpB,YAAAW,OAAA,SAAOX,GACH,IAAMmH,EAAMnP,KAAKwQ,IAAI7L,QAAQqD,GAEzBmH,EAAM,GAIVnP,KAAKwQ,IAAI9L,OAAOyK,EAAK,IAGzB,YAAAjJ,QAAA,WACI,OAA2B,IAApBlG,KAAKwQ,IAAIvI,QAGpB,YAAA1H,KAAA,W,IAAK,IACGmQ,EADH,kDAOD,OAJA1Q,KAAKwQ,IAAI1L,SAAQ,SAAA6L,GACbD,EAAMC,EAAE,oBAAIC,IAAI,OAGbF,GAEf,EA1CA,GA4CA,UAAeG,G,kKC9Cf,WACA,WAEA,mBAAgBtN,GACZ,IAAMuN,GAAW,aAAavN,EAAOwN,UAAUC,WAS/C,MAPkC,CAC9BvG,WAAYqG,EAAW,EAAA3M,eAAe8M,SAAW,EAAA9M,eAAe+M,QAChEhG,WAAY4F,EAAW,EAAA3M,eAAegN,WAAa,EAAAhN,eAAeiN,MAElEpG,YAAa8F,EAAW,EAAA3M,eAAegN,WAAa,EAAAhN,eAAekN,a,8ECX3E,IAAMC,EAAY,mGAElB,mBAAgBN,GAAsB,OAAAM,EAAUxJ,KAAKkJ,K,s8CCNrD,cAEA,OAEA,2B,uDACY,EAAAO,MAAsC,IAAIC,I,EAiDtD,OAlDoB,OAGhB,sBAAI,mBAAI,C,IAAR,WACI,OAAOxR,KAAKyR,U,IAGhB,SAASC,GACL,MAAM,EAAA3N,MAAM4N,iB,gCAGhB,YAAA9E,KAAA,SAAKJ,GAAL,WACSvF,MAAMC,QAAQsF,GAMnBA,EAAO3H,SAAQ,SAAA3C,GAAK,SAAKoP,MAAMK,IAAIzP,EAAEiD,GAAIjD,MALrCnC,KAAKuR,MAAMK,IAAInF,EAAOrH,GAAIqH,IAQlC,YAAAxL,IAAA,SAAImE,GACA,OAAOpF,KAAKuR,MAAMtQ,IAAImE,IAG1B,YAAAuD,OAAA,SAAOvD,GACHpF,KAAKuR,MAAMM,OAAOzM,IAGtB,YAAAqM,OAAA,W,QACUK,EAA0B,G,IAEhC,IAAmB,QAAA9R,KAAKuR,OAAK,8BAAE,CAA1B,IAAMQ,EAAI,QACXD,EAAKtN,KAAKuN,EAAK,K,iGAGnB,OAAOD,GAGX,YAAA1G,UAAA,W,QACU0C,EAAgB,G,IAEtB,IAAmB,QAAA9N,KAAKuR,OAAK,8BAAE,CAA1B,IAAMQ,EAAI,QACXjE,EAAItJ,KAAKuN,EAAK,K,iGAKlB,OAFA/R,KAAKuR,MAAQ,IAAIC,IAEV1D,GAEf,EAlDA,CAAoB,WAoDpB,UAAekE,G,4+BChDf,cACA,QACA,OACA,OACA,QACA,OASA,aASI,WAAY1H,EAAyB9E,GACjCxF,KAAKsK,QAAU,CACXxH,MAAOwH,EAAQxH,MACfG,aAAcqH,EAAQrH,aACtBE,QAASmH,EAAQnH,QACjBD,gBAAiBoH,EAAQpH,gBACzBI,UAAWgH,EAAQhH,WAEvBtD,KAAKwF,MAAQA,GAEb,IAAAyM,uBAAsBjS,KAAKsK,QAAQrH,cAoK3C,OAhKI,YAAAyJ,eAAA,SAAevD,GAAf,WACI,IAAKA,EAAMJ,OACP,MAAM,EAAAhF,MAAMmO,uBAGV,MAAsDlS,KAAKsK,QAAzDxH,EAAK,QAAEG,EAAY,eAAEK,EAAS,YAAEJ,EAAe,kBACjDsC,EAAQxF,KAAKwF,MAEf2M,GAAiB,IAAAC,kBAAiBtP,EAAOG,EAAckG,EAAMzD,MAAOyD,EAAMxD,IAAKzC,GAMnF,OAJKsC,EAAMgG,OAAOa,cAAcnG,YAC5BiM,EAAiB3M,EAAMgG,OAAOa,cAAc9L,KAAK4I,EAAM/D,GAAI+M,IAAmB,IAG3EA,EAAeT,KAAI,SAAA7P,GACtB,IAAIyE,GAAQ,IAAA+L,eAAcxQ,EAAGsH,EAAO7F,EAAW,EAAKgH,QAAQnH,QAASF,GAMrE,OAJKuC,EAAMgG,OAAOc,SAASpG,YACvBI,EAAQd,EAAMgG,OAAOc,SAAS/L,KAAK4I,EAAM/D,GAAIkB,IAG1CA,MAIf,YAAA2H,gBAAA,SAAgBlB,GAAhB,WACU+E,EAAO5K,MAAMC,QAAQ4F,GAAWA,EAAU,CAACA,GAE3CiB,EAAqC,GAwB3C,OAtBA8D,EAAKhN,SAAQ,SAAA3C,GACT,GAAMA,aAAa,UAAnB,CAQA,IAAMgH,EAAQhH,EAAEoD,YAAY,EAAK+E,QAAQxH,MAAO,EAAK0C,OACtC,EAAKkH,eAAevD,GAExBlB,OAAS,EAChB+F,EAAgBxJ,KAAKrC,GAErB,EAAAyB,aAAagB,KAAK,EAAAjB,qBAAsB,CACpCW,KAAM,EAAAP,MAAMuO,6BACZnG,OAAQhK,SAfZ,EAAAyB,aAAagB,KAAK,EAAAjB,qBAAsB,CACpCW,KAAM,EAAAP,MAAMwO,uBAmBjBvE,GAOX,YAAAJ,gBAAA,SAAgBxI,G,QAAhB,OAEUsC,EAAM,IAAIC,OAAO,WAAIvC,EAAE,aAAK,EAAAhD,YAAW,cAAM,EAAAA,YAAW,YAAIgD,EAAE,OAE9DI,EAAQxF,KAAKwF,MACbrC,EAAUnD,KAAKsK,QAAQnH,QAEvBqP,EAASxS,KAAKsK,QAAQrH,aAAaqE,iBACrC,UAAGnE,EAAO,iBAAS,EAAAZ,mBAAkB,MAInCkQ,EAA2B,GAE3BC,EAA6B,GAE7BC,EAAgC,G,IAEtC,IAAiB,QAAAH,GAAM,8BAAE,CAApB,IAAMI,EAAE,QACHC,EAASD,EAAGrM,QAAQ,EAAA7D,0BACpBoQ,EAAeF,EAAGrM,QAAQ,EAAA5D,gCAG5BkQ,IAAWzN,GAAO0N,EAIbD,IAAWzN,EAChBsN,EAAYlO,KAAKoO,GAGZC,IAAWzN,GAAMsC,EAAII,KAAKgL,IAC/BH,EAAenO,KAAKoO,GARpBH,EAAUjO,KAAKoO,I,iGA0DvB,OA9CAH,EAAU3N,SAAQ,SAAA8N,GACd,IAAMvE,EAAUuE,EAAGjM,WACboM,EAAM,EAAKzI,QAAQrH,aAAa+P,0BAEtC,IAAAlO,SAAQ8N,EAAGjE,YAAY,SAACsE,GAAa,OAAAF,EAAIG,YAAYD,EAAGE,WAAU,OAElE,IAAMC,EAAQR,EAAGS,gBACXC,EAAQV,EAAGW,YAEjBlF,EAAQmF,aAAaT,EAAKH,IAE1B,IAAAa,sBAAqBL,GAAO,IAC5B,IAAAK,sBAAqBH,GAAO,GAC5B9N,EAAM+G,OAAOC,YAAYjM,KAAK6E,EAAIwN,EAAI,aAG1CF,EAAY5N,SAAQ,SAAA8N,GAChB,IAAMrM,EAAUqM,EAAGrM,QACbuH,EAAMvH,EAAQ,EAAA5D,gCAAgCmE,MAAM,EAAA1E,aACpDsR,EAAQ5F,EAAI6F,QAGZC,EAAe,EAAKtJ,QAAQrH,aAAa4Q,cAC3C,UAAG1Q,EAAO,iBAAS,EAAAZ,mBAAkB,aAAKmR,EAAK,OAG/CE,KAEA,IAAAhL,gBAAegK,IAEf,IAAArK,UAASqK,EAAI,EAAF,KAAMgB,EAAapL,YAAS,KAG3CjC,EAAQ,EAAA7D,0BAA4BgR,EACpCnN,EAAQ,EAAA5D,gCAAkCmL,EAAIgG,KAAK,EAAA1R,aAEnDoD,EAAM+G,OAAOC,YAAYjM,KAAK6E,EAAIwN,EAAI,gBAG1CD,EAAe7N,SAAQ,SAAA8N,GACnB,IAAMmB,EAAWnB,EAAGrM,QAAQ,EAAA5D,gCAE5BiQ,EAAGrM,QAAQ,EAAA5D,gCAAkCoR,EAAS1J,QAAQ3C,EAAK,IACnElC,EAAM+G,OAAOC,YAAYjM,KAAK6E,EAAIwN,EAAI,mBAGnCH,EAAUxK,OAASyK,EAAYzK,OAAS0K,EAAe1K,SAAW,GAG7E,YAAA8F,mBAAA,WACU,MAAmC/N,KAAKsK,QAAtCnH,EAAO,UAAEL,EAAK,QAAEG,EAAY,gBACrB,IAAA+D,qBAAoBlE,EAAOK,GAEnC2B,SAAQ,SAAA8N,GACX,IAAMvE,EAAUuE,EAAGjM,WACboM,EAAM9P,EAAa+P,0BAEzB,IAAAlO,SAAQ8N,EAAGjE,YAAY,SAACsE,GAAa,OAAAF,EAAIG,YAAYD,EAAGE,WAAU,OAClE9E,EAAQmF,aAAaT,EAAKH,GAG1BvE,EAAQ2F,gBAIpB,EAvLA,G,yqBCpBA,WACA,OACA,OASA,QAQMC,EAAkB,SAAC3N,EAAoB4N,GACzC,IAAK5N,EACD,OAAO,EAGX,GAAI,MAAMwB,KAAKoM,GAAW,CACtB,IAAM5Q,EAAY4Q,EAAS7J,QAAQ,MAAO,IAE1C,OAAO/D,IAAS,IAAAuC,UAASvC,EAAOhD,GAC7B,GAAI,KAAKwE,KAAKoM,GAAW,CAC5B,IAAM9O,EAAK8O,EAAS7J,QAAQ,KAAM,IAElC,OAAO/D,GAASA,EAAMlB,KAAOA,EAGjC,IAAM2K,EAAUmE,EAAS9E,cAEzB,OAAO9I,GAASA,EAAMyJ,UAAYA,GA0CzB,EAAAqC,iBAAmB,SAC5BtP,EACAG,EACAyC,EACAC,EACAzC,GAEA,IAAMiR,EAAazO,EAAMY,MACnB8N,EAAWzO,EAAIW,MACfiD,EAAc7D,EAAM4D,OACpBG,EAAY9D,EAAI2D,OAKtB,GAAI6K,IAAeC,GAAYD,aAAsBlR,EAAaoR,YAAYC,KAC1E,OApDuB,SAC3BH,EACA5K,EACAE,EACAvG,GAMA,IAJA,IAAIqR,EAAWJ,EAETK,EAAa,SAACC,GAAoB,OAAAvR,aAAe,EAAfA,EAAiBwR,MAAK,SAAAvS,GAAK,OAAA8R,EAAgBQ,EAAItS,OAEhFoS,GAAU,CACb,GAA0B,IAAtBA,EAAS3F,UAAkB4F,EAAWD,GACtC,MAAO,GAGXA,EAAWA,EAAS5N,WAGxBwN,EAAWQ,UAAUpL,GAErB,IAAMqL,EAAaT,EAAWZ,YAI9B,OAFAqB,EAAWD,UAAUlL,EAAYF,GAE1B,CACH,CACIjD,MAAOsO,EACPtQ,KAAM,EAAAJ,iBAAiBiB,KACvB0P,UAAW,EAAA/Q,UAAUgR,OAwBlBC,CAAuBZ,EAAY5K,EAAaE,EAAWvG,GAWtE,IARA,IAAMoL,EAA2D,CAACxL,GAC5DkS,EAAgC,GAEhCR,EAAa,SAACC,GAAoB,OAAAvR,aAAe,EAAfA,EAAiBwR,MAAK,SAAAvS,GAAK,OAAA8R,EAAgBQ,EAAItS,OAEnF8S,GAAsB,EACtBC,EAAgB,KAEZA,EAAU5G,EAAUG,OAExB,GAAyB,IAArByG,EAAQtG,WAAkB4F,EAAWU,GAAzC,CAMA,IAFA,IAAMxG,EAAWwG,EAAQvG,WAEhBvO,EAAIsO,EAASzG,OAAS,EAAG7H,GAAK,EAAGA,IACtCkO,EAAU9J,KAAKkK,EAAStO,IAI5B,GAAI8U,IAAYf,EAAY,CACxB,GAAyB,IAArBe,EAAQtG,SAAgB,CACvBsG,EAAiBP,UAAUpL,GAE5B,IAAM8G,EAAO6E,EAAQ3B,YAErByB,EAAcxQ,KAAK,CACf8B,MAAO+J,EACP/L,KAAM,EAAAJ,iBAAiBiB,KACvB0P,UAAW,EAAA/Q,UAAUqR,OAK7BF,GAAsB,MACnB,IAAIC,IAAYd,EAAU,CAC7B,GAAyB,IAArBc,EAAQtG,UACFyB,EAAO6E,GAERP,UAAUlL,GACfuL,EAAcxQ,KAAK,CACf8B,MAAO+J,EACP/L,KAAM,EAAAJ,iBAAiBiB,KACvB0P,UAAW,EAAA/Q,UAAUsR,OAK7B,MAGC,GAAIH,GAA4C,IAArBC,EAAQtG,SAAgB,CAGpD,GAAmC,KAA/BsG,EAAQrG,YAAYwG,OAAe,SAEvCL,EAAcxQ,KAAK,CACf8B,MAAO4O,EACP5Q,KAAM,EAAAJ,iBAAiBiB,KACvB0P,UAAW,EAAA/Q,UAAUwR,SAKjC,OAAON,GAGX,IAAMzM,EAAW,SAACJ,EAAkB7E,GAChC,IAAIiS,EAAarO,MAAMC,QAAQ7D,GAAaA,EAAY,CAACA,GAOzD,OALAiS,EAAmC,IAAtBA,EAAWtN,OAAe,EAAC,IAAApF,qBAAoBQ,MAAMC,WAAaiS,GACpEzQ,SAAQ,SAAArE,IACf,cAAgB0H,EAAK1H,MAGlB0H,GAGLqN,EAAc,SAAC5N,GAAsB,OAACA,IAAOA,EAAGiH,aAgIzC,EAAAwD,cAAgB,SACzBoD,EACAtM,EACA7F,EACAH,EACAF,GAEA,IAAMoL,EAAUoH,EAASnP,MAAMK,WACzByM,EAAQqC,EAASnP,MAAM+M,gBACvBC,EAAQmC,EAASnP,MAAMiN,YAiB7B,OAZK,IAAAlN,qBAAoBgI,KAIhB,IAAAhI,qBAAoBgI,IAAcmH,EAAYpC,IAAWoC,EAAYlC,GA9C1D,SAACmC,EAAwBtM,EAAuB7F,GACpE,IAAM+K,EAAUoH,EAASnP,MAAMK,WACzB+O,EAAqBrH,GAE3B,IAAAzF,gBAAe8M,GACfnN,EAASmN,EAAOpS,GAEhB,IAAMiD,EAAU8H,EAAQ9H,QAClBoP,EAAWpP,EAAQ,EAAA7D,0BAOzB,OALA6D,EAAQ,EAAA7D,0BAA4ByG,EAAM/D,GAC1CmB,EAAQ,EAAA5D,gCAAkC4D,EAAQ,EAAA5D,gCAC5CgT,EAAW,EAAAvT,YAAcmE,EAAQ,EAAA5D,gCACjCgT,EAECD,EAoCKE,CAAgBH,EAAUtM,EAAO7F,GA1HzB,SACpBmS,EACAtM,EACA7F,EACAH,EACAF,GAEA,IAAMyS,EAAqBzS,EAAa4S,cAAc1S,GAEhDkL,EAAUoH,EAASnP,MAAMK,WACzByM,EAAQqC,EAASnP,MAAM+M,gBACvBC,EAAQmC,EAASnP,MAAMiN,YACvBR,EAAM9P,EAAa+P,yBACnB8C,EAAWzH,EAAQ9H,QAAQ,EAAA7D,0BAC3BqT,EAAgB1H,EAAQ9H,QAAQ,EAAA5D,gCAChCqT,EAAYD,EAAgBD,EAAW,EAAA1T,YAAc2T,EAAgBD,EAE3EJ,EAAMO,aAAa,eAAQ,EAAA1T,oBAAsB4G,EAAM/D,IACvDsQ,EAAMO,aAAa,eAAQ,EAAAzT,0BAA4BwT,GACvDN,EAAMxC,YAAYuC,EAASnP,MAAM6M,WAAU,IAE3C,IAEI0B,EAFAqB,GAAY,EACZC,GAAY,EAGZ/C,KACMgD,EAAQ/H,EAAQ8E,WAAU,IAE1BtE,YAAcuE,EAAMvE,YAC1BkE,EAAIG,YAAYkD,GAChBF,GAAY,GAGhB,IAYUE,EAZJC,EAA0B,IAE5BnP,MAAMC,QAAQ7D,GACd+S,EAAc7R,KAAI,MAAlB6R,EAAa,OAAS/S,IAAS,IAE/B+S,EAAc7R,KAAKlB,GAGvBiF,EAASmN,GAAO,IAAAY,QAAOD,IACvBtD,EAAIG,YAAYwC,GAEZpC,MACM8C,EAAQ/H,EAAQ8E,WAAU,IAE1BtE,YAAcyE,EAAMzE,YAC1BkE,EAAIG,YAAYkD,GAChBD,GAAY,GAgBhB,OAZItB,EADAqB,GAAaC,EACD,EAAArS,UAAUgR,KACfoB,EACK,EAAApS,UAAUqR,KACfgB,EACK,EAAArS,UAAUsR,KAEV,EAAAtR,UAAUwR,KAG1BI,EAAMO,aAAa,eAAQ,EAAAxT,oBAAsBoS,GACjDxG,EAAQ1H,WAAW6M,aAAaT,EAAK1E,GAE9BqH,EAqDKa,CAAgBd,EAAUtM,EAAO7F,EAAWH,EAASF,GA9IjD,SAChBwS,EACAtM,EACA7F,EACAH,EACAF,GAEA,IAAMyS,EAAQzS,EAAa4S,cAAc1S,GAWzC,OATAoF,EAASmN,EAAOpS,GAEhBoS,EAAMxC,YAAYuC,EAASnP,MAAM6M,WAAU,IAC3CsC,EAASnP,MAAMK,WAAW6M,aAAakC,EAAOD,EAASnP,OAEvDoP,EAAMO,aAAa,eAAQ,EAAA1T,oBAAsB4G,EAAM/D,IACvDsQ,EAAMO,aAAa,eAAQ,EAAAxT,oBAAsBgT,EAASZ,WAC1Da,EAAMO,aAAa,eAAQ,EAAAzT,0BAA4B,IAEhDkT,EAwHKc,CAAYf,EAAUtM,EAAO7F,EAAWH,EAASF,IAkBpD,EAAAwQ,qBAAuB,SAACb,EAAU6D,GAC3C,QAD2C,IAAAA,OAAA,GACtC7D,GAAsB,IAAhBA,EAAGhE,SAAd,CAIA,IAAM8H,EAAWD,EAAS7D,EAAGW,YAAcX,EAAGS,gBAE9C,GAA0B,IAAtBqD,EAAS9H,SAAb,CAIA,IAAMzJ,EAAOuR,EAASC,UAEtB/D,EAAG+D,UAAYF,EAAS7D,EAAG+D,UAAYxR,EAAOA,EAAOyN,EAAG+D,UACxDD,EAAS/P,WAAWiQ,YAAYF,O,maC9VvB,EAAAJ,OAAS,SAAIO,G,QAChB1Q,EAAW,G,IAEjB,IAAiB,QAAA0Q,GAAG,8BAAE,CAAjB,IAAMC,EAAE,SACgB,IAArB3Q,EAAIxB,QAAQmS,IACZ3Q,EAAI3B,KAAKsS,I,iGAIjB,OAAO3Q,I,6GCTX,WAEa,EAAA8L,sBAAwB,SAAChP,GAClC,IAAM8T,EAAU,EAAAzU,cAEZ0U,EAA2B/T,EAAagU,eAAeF,GAE3D,IAAKC,EAAQ,CACT,IAAME,EAAWjU,EAAakU,gBAAe,IAAA3T,mBAE7CwT,EAAS/T,EAAa4S,cAAc,UAC7BzQ,GAAK2R,EACZC,EAAO9D,YAAYgE,GACnBjU,EAAakS,KAAKjC,YAAY8D,GAGlC,OAAOA,M", "file": "web-highlighter.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Highlighter\"] = factory();\n\telse\n\t\troot[\"Highlighter\"] = factory();\n})(this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 7);\n", "/**\n * all constants\n * cSpell:ignore mengshou\n */\n\nimport type HighlightSource from '@src/model/source';\nimport type { ERROR } from '@src/types';\nimport camel from '@src/util/camel';\nimport EventEmitter from '@src/util/event.emitter';\n\nexport const ID_DIVISION = ';';\nexport const LOCAL_STORE_KEY = 'highlight-mengshou';\nexport const STYLESHEET_ID = 'highlight-mengshou-style';\n\nexport const DATASET_IDENTIFIER = 'highlight-id';\nexport const DATASET_IDENTIFIER_EXTRA = 'highlight-id-extra';\nexport const DATASET_SPLIT_TYPE = 'highlight-split-type';\nexport const CAMEL_DATASET_IDENTIFIER = camel(DATASET_IDENTIFIER);\nexport const CAMEL_DATASET_IDENTIFIER_EXTRA = camel(DATASET_IDENTIFIER_EXTRA);\nexport const CAMEL_DATASET_SPLIT_TYPE = camel(DATASET_SPLIT_TYPE);\n\nconst DEFAULT_WRAP_TAG = 'span';\n\nexport const getDefaultOptions = () => ({\n    $root: document || document.documentElement,\n    rootDocument: document,\n    exceptSelectors: null,\n    wrapTag: DEFAULT_WRAP_TAG,\n    verbose: false,\n    style: {\n        className: 'highlight-wrap',\n    },\n    window,\n});\n\nexport const getStylesheet = () => `\n    .${getDefaultOptions().style.className} {\n    }\n    .${getDefaultOptions().style.className}.active {\n        background: #ffb;\n    }\n`;\n\nexport const ROOT_IDX = -2;\nexport const UNKNOWN_IDX = -1;\nexport const INTERNAL_ERROR_EVENT = 'error';\n\ninterface EventHandlerMap {\n    [key: string]: (...args: any[]) => void;\n    error: (data: { type: ERROR; detail?: HighlightSource; error?: any }) => void;\n}\nclass ErrorEventEmitter extends EventEmitter<EventHandlerMap> {}\n\nexport const eventEmitter = new ErrorEventEmitter();\n", "import type Hook from '@src/util/hook';\n\nexport type RootElement = Document | HTMLElement;\n\nexport interface HighlighterOptions {\n    $root?: RootElement;\n    rootDocument?: Document;\n    exceptSelectors?: string[];\n    wrapTag?: string;\n    verbose?: boolean;\n    style?: {\n        className?: string[] | string;\n    };\n    window: Window;\n}\n\nexport interface PainterOptions {\n    $root: RootElement;\n    rootDocument: Document;\n    wrapTag: string;\n    className: string[] | string;\n    exceptSelectors: string[];\n}\n\nexport enum SplitType {\n    none = 'none',\n    head = 'head',\n    tail = 'tail',\n    both = 'both',\n}\n\nexport enum ERROR {\n    DOM_TYPE_ERROR = '[DOM] Receive wrong node type.',\n    DOM_SELECTION_EMPTY = '[DOM] The selection contains no dom node, may be you except them.',\n    RANGE_INVALID = \"[RANGE] Got invalid dom range, can't convert to a valid highlight range.\",\n    RANGE_NODE_INVALID = \"[RANGE] Start or end node isn't a text node, it may occur an error.\",\n    DB_ID_DUPLICATE_ERROR = '[STORE] Unique id conflict.',\n    CACHE_SET_ERROR = \"[CACHE] Cache.data can't be set manually, please use .save().\",\n    SOURCE_TYPE_ERROR = \"[SOURCE] Object isn't a highlight source instance.\",\n    HIGHLIGHT_RANGE_FROZEN = '[HIGHLIGHT_RANGE] A highlight range must be frozen before render.',\n    HIGHLIGHT_SOURCE_RECREATE = '[HIGHLIGHT_SOURCE] Recreate highlights from sources error.',\n    // eslint-disable-next-line max-len\n    HIGHLIGHT_SOURCE_NONE_RENDER = \"[HIGHLIGHT_SOURCE] This highlight source isn't rendered. May be the exception skips it or the dom structure has changed.\",\n}\n\nexport enum EventType {\n    CREATE = 'selection:create',\n    REMOVE = 'selection:remove',\n    MODIFY = 'selection:modify',\n    HOVER = 'selection:hover',\n    HOVER_OUT = 'selection:hover-out',\n    CLICK = 'selection:click',\n}\n\nexport enum CreateFrom {\n    STORE = 'from-store',\n    INPUT = 'from-input',\n}\n\nexport enum SelectedNodeType {\n    text = 'text',\n    span = 'span',\n}\n\nexport interface SelectedNode {\n    $node: Node | Text;\n    type: SelectedNodeType;\n    splitType: SplitType;\n}\n\nexport interface DomMeta {\n    parentTagName: string;\n    parentIndex: number;\n    textOffset: number;\n    extra?: unknown;\n}\n\nexport interface DomNode {\n    $node: Node;\n    offset: number;\n}\n\nexport interface HighlightPosition {\n    start: {\n        top: number;\n        left: number;\n    };\n    end: {\n        top: number;\n        left: number;\n    };\n}\n\nexport interface HookMap {\n    Render: {\n        UUID: Hook<string>;\n        SelectedNodes: Hook<SelectedNode[]>;\n        WrapNode: Hook<HTMLElement>;\n    };\n    Serialize: {\n        Restore: Hook<DomNode[]>;\n        RecordInfo: Hook<string>;\n    };\n    Remove: {\n        UpdateNodes: Hook;\n    };\n}\n\nexport enum UserInputEvent {\n    touchend = 'touchend',\n    mouseup = 'mouseup',\n    touchstart = 'touchstart',\n    click = 'click',\n    mouseover = 'mouseover',\n}\n\nexport interface IInteraction {\n    PointerEnd: UserInputEvent;\n    PointerTap: UserInputEvent;\n    PointerOver: UserInputEvent;\n}\n", "/**\n * tiny event emitter\n * modify from mitt\n */\n\ntype EventHandler = (...data: unknown[]) => void;\n\ntype EventMap = Record<string, EventHandler>;\ntype HandlersMap<T extends EventMap> = {\n    [K in keyof T]: T[K][];\n};\n\nclass EventEmitter<U extends EventMap = EventMap> {\n    private handlersMap: HandlersMap<U> = Object.create(null);\n\n    on<T extends keyof U>(type: T, handler: U[T]) {\n        if (!this.handlersMap[type]) {\n            this.handlersMap[type] = [];\n        }\n\n        this.handlersMap[type].push(handler);\n\n        return this;\n    }\n\n    off<T extends keyof U>(type: T, handler: U[T]) {\n        if (this.handlersMap[type]) {\n            this.handlersMap[type].splice(this.handlersMap[type].indexOf(handler) >>> 0, 1);\n        }\n\n        return this;\n    }\n\n    emit<T extends keyof U>(type: T, ...data: Parameters<U[T]>) {\n        if (this.handlersMap[type]) {\n            this.handlersMap[type].slice().forEach(handler => {\n                handler(...data);\n            });\n        }\n\n        return this;\n    }\n}\n\nexport default EventEmitter;\n", "/**\n * HighlightSource Class (HSource)\n * This Object can be deSerialized to HRange.\n * Also it has the ability for persistence.\n */\n\nimport type { DomMeta, HookMap, DomNode } from '@src/types';\nimport HighlightRange from '@src/model/range/index';\nimport { queryElementNode, getTextChildByOffset } from '@src/model/source/dom';\n\nclass HighlightSource {\n    startMeta: DomMeta;\n\n    endMeta: DomMeta;\n\n    text: string;\n\n    id: string;\n\n    extra?: unknown;\n\n    __isHighlightSource: unknown;\n\n    constructor(startMeta: DomMeta, endMeta: DomMeta, text: string, id: string, extra?: unknown) {\n        this.startMeta = startMeta;\n        this.endMeta = endMeta;\n        this.text = text;\n        this.id = id;\n        this.__isHighlightSource = {};\n\n        if (extra) {\n            this.extra = extra;\n        }\n    }\n\n    deSerialize($root: Document | HTMLElement, hooks: HookMap): HighlightRange {\n        const { start, end } = queryElementNode(this, $root);\n        let startInfo = getTextChildByOffset(start, this.startMeta.textOffset);\n        let endInfo = getTextChildByOffset(end, this.endMeta.textOffset);\n\n        if (!hooks.Serialize.Restore.isEmpty()) {\n            const res: DomNode[] = hooks.Serialize.Restore.call(this, startInfo, endInfo) || [];\n\n            startInfo = res[0] || startInfo;\n            endInfo = res[1] || endInfo;\n        }\n\n        const range = new HighlightRange(startInfo, endInfo, this.text, this.id, true);\n\n        return range;\n    }\n}\n\nexport default HighlightSource;\n", "import type { RootElement } from '@src/types';\nimport {\n    ID_DIVISION,\n    DATASET_IDENTIFIER,\n    CAMEL_DATASET_IDENTIFIER,\n    CAMEL_DATASET_IDENTIFIER_EXTRA,\n} from '@src/util/const';\n\n/**\n * whether a wrapper node\n */\nexport const isHighlightWrapNode = ($node: HTMLElement): boolean =>\n    !!$node.dataset && !!$node.dataset[CAMEL_DATASET_IDENTIFIER];\n\n/**\n * ===================================================================================\n * below methods (getHighlightId/getExtraHighlightId)\n * will check whether the node is inside a wrapper iteratively util reach the root node\n * if the node is not inside the root, the id must be empty\n * ====================================================================================\n */\n\nconst findAncestorWrapperInRoot = ($node: HTMLElement, $root: RootElement): HTMLElement => {\n    let isInsideRoot = false;\n    let $wrapper: HTMLElement = null;\n\n    while ($node) {\n        if (isHighlightWrapNode($node)) {\n            $wrapper = $node;\n        }\n\n        if ($node === $root) {\n            isInsideRoot = true;\n            break;\n        }\n\n        $node = $node.parentNode as HTMLElement;\n    }\n\n    return isInsideRoot ? $wrapper : null;\n};\n\n/**\n * get highlight id by a node\n */\nexport const getHighlightId = ($node: HTMLElement, $root: RootElement): string => {\n    $node = findAncestorWrapperInRoot($node, $root);\n\n    if (!$node) {\n        return '';\n    }\n\n    return $node.dataset[CAMEL_DATASET_IDENTIFIER];\n};\n\n/**\n * get extra highlight id by a node\n */\nexport const getExtraHighlightId = ($node: HTMLElement, $root: RootElement): string[] => {\n    $node = findAncestorWrapperInRoot($node, $root);\n\n    if (!$node) {\n        return [];\n    }\n\n    return $node.dataset[CAMEL_DATASET_IDENTIFIER_EXTRA].split(ID_DIVISION).filter(i => i);\n};\n\n/**\n * get all highlight wrapping nodes nodes from a root node\n */\nexport const getHighlightsByRoot = ($roots: RootElement | RootElement[], wrapTag: string): HTMLElement[] => {\n    if (!Array.isArray($roots)) {\n        $roots = [$roots];\n    }\n\n    const $wraps: HTMLElement[] = [];\n\n    for (const $r of $roots) {\n        const $list = $r.querySelectorAll<HTMLElement>(`${wrapTag}[data-${DATASET_IDENTIFIER}]`);\n\n        // eslint-disable-next-line prefer-spread\n        $wraps.push.apply($wraps, $list);\n    }\n\n    return $wraps;\n};\n\n/**\n * get all highlight wrapping nodes by highlight id from a root node\n */\nexport const getHighlightById = ($root: RootElement, id: string, wrapTag: string): HTMLElement[] => {\n    const $highlights: HTMLElement[] = [];\n    const reg = new RegExp(`(${id}\\\\${ID_DIVISION}|\\\\${ID_DIVISION}?${id}$)`);\n    const $list = $root.querySelectorAll<HTMLElement>(`${wrapTag}[data-${DATASET_IDENTIFIER}]`);\n\n    for (const $l of $list) {\n        const $n = $l;\n        const nid = $n.dataset[CAMEL_DATASET_IDENTIFIER];\n\n        if (nid === id) {\n            $highlights.push($n);\n            continue;\n        }\n\n        const extraId = $n.dataset[CAMEL_DATASET_IDENTIFIER_EXTRA];\n\n        if (reg.test(extraId)) {\n            $highlights.push($n);\n            continue;\n        }\n    }\n\n    return $highlights;\n};\n\nexport const forEach = ($nodes: NodeList, cb: (n: Node, idx: number, s: NodeList) => void): void => {\n    for (let i = 0; i < $nodes.length; i++) {\n        cb($nodes[i], i, $nodes);\n    }\n};\n\nexport const removeEventListener = ($el: RootElement, evt: string, fn: EventListenerOrEventListenerObject) => {\n    $el.removeEventListener(evt, fn);\n};\n\n/**\n * maybe be need some polyfill methods later\n * provide unified dom methods for compatibility\n */\nexport const addEventListener = ($el: RootElement, evt: string, fn: EventListenerOrEventListenerObject) => {\n    $el.addEventListener(evt, fn);\n\n    return () => {\n        removeEventListener($el, evt, fn);\n    };\n};\n\nexport const addClass = ($el: HTMLElement, className: string[] | string) => {\n    if (!Array.isArray(className)) {\n        className = [className];\n    }\n\n    $el.classList.add(...className);\n};\n\nexport const removeClass = ($el: HTMLElement, className: string): void => {\n    $el.classList.remove(className);\n};\n\nexport const removeAllClass = ($el: HTMLElement): void => {\n    $el.className = '';\n};\n\nexport const hasClass = ($el: HTMLElement, className: string): boolean => $el.classList.contains(className);\n", "/**\n * the HighlightRange Class（HRange）\n * It's a special object called <PERSON><PERSON>ange in Highlighter,\n * represents for a piece of chosen dom\n */\n\nimport type { DomNode, HookMap } from '@src/types';\nimport type Hook from '@src/util/hook';\nimport HighlightSource from '@src/model/source/index';\nimport { ERROR } from '@src/types';\nimport { getDomRange, removeSelection } from '@src/model/range/selection';\nimport uuid from '@src/util/uuid';\nimport { getDomMeta, formatDomNode } from '@src/model/range/dom';\nimport { eventEmitter, INTERNAL_ERROR_EVENT } from '@src/util/const';\n\nclass HighlightRange {\n    static removeDomRange = removeSelection;\n\n    start: DomNode;\n\n    end: DomNode;\n\n    text: string;\n\n    id: string;\n\n    frozen: boolean;\n\n    constructor(start: DomNode, end: DomNode, text: string, id: string, frozen = false) {\n        this.start = formatDomNode(start);\n        this.end = formatDomNode(end);\n        this.text = text;\n        this.frozen = frozen;\n        this.id = id;\n    }\n\n    static fromSelection(idHook: Hook<string>, rootDocument: Document) {\n        const range = getDomRange(rootDocument);\n\n        if (!range) {\n            return null;\n        }\n\n        const start: DomNode = {\n            $node: range.startContainer,\n            offset: range.startOffset,\n        };\n        const end: DomNode = {\n            $node: range.endContainer,\n            offset: range.endOffset,\n        };\n\n        const text = range.toString();\n        let id = idHook.call(start, end, text);\n\n        id = typeof id !== 'undefined' && id !== null ? id : uuid();\n\n        return new HighlightRange(start, end, text, id);\n    }\n\n    // serialize the HRange instance\n    // so that you can save the returned object (e.g. use JSON.stringify on it and send to backend)\n    serialize($root: Document | HTMLElement, hooks: HookMap): HighlightSource {\n        const startMeta = getDomMeta(this.start.$node as Text, this.start.offset, $root);\n        const endMeta = getDomMeta(this.end.$node as Text, this.end.offset, $root);\n\n        let extra;\n\n        if (!hooks.Serialize.RecordInfo.isEmpty()) {\n            extra = hooks.Serialize.RecordInfo.call(this.start, this.end, $root);\n        }\n\n        this.frozen = true;\n\n        return new HighlightSource(startMeta, endMeta, this.text, this.id, extra);\n    }\n}\n\nexport default HighlightRange;\n", "/**\n * generate UUID\n */\n\n/* eslint-disable @typescript-eslint/restrict-plus-operands */\nexport default function createUUID(a?): string {\n    return a\n        ? (a ^ ((Math.random() * 16) >> (a / 4))).toString(16)\n        : ((([1e7] as unknown) as string) + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, createUUID);\n}\n", "import type { <PERSON><PERSON><PERSON>, <PERSON>Meta, <PERSON>Map, HighlighterOptions, IInteraction } from '@src/types';\nimport EventEmitter from '@src/util/event.emitter';\nimport HighlightRange from '@src/model/range';\nimport HighlightSource from '@src/model/source';\nimport uuid from '@src/util/uuid';\nimport Hook from '@src/util/hook';\nimport getInteraction from '@src/util/interaction';\nimport Cache from '@src/data/cache';\nimport Painter from '@src/painter';\nimport { eventEmitter, getDefaultOptions, INTERNAL_ERROR_EVENT } from '@src/util/const';\nimport { ERROR, EventType, CreateFrom } from '@src/types';\nimport {\n    addClass,\n    removeClass,\n    isHighlightWrapNode,\n    getHighlightById,\n    getExtraHighlightId,\n    getHighlightsByRoot,\n    getHighlightId,\n    addEventListener,\n    removeEventListener,\n} from '@src/util/dom';\n\ninterface EventHandlerMap {\n    [key: string]: (...args: any[]) => void;\n    [EventType.CLICK]: (data: { id: string }, h: Highlighter, e: MouseEvent | TouchEvent) => void;\n    [EventType.HOVER]: (data: { id: string }, h: Highlighter, e: MouseEvent | TouchEvent) => void;\n    [EventType.HOVER_OUT]: (data: { id: string }, h: Highlighter, e: MouseEvent | TouchEvent) => void;\n    [EventType.CREATE]: (data: { sources: HighlightSource[]; type: CreateFrom }, h: Highlighter) => void;\n    [EventType.REMOVE]: (data: { ids: string[] }, h: Highlighter) => void;\n}\n\nexport default class Highlighter extends EventEmitter<EventHandlerMap> {\n    static event = EventType;\n\n    static isHighlightWrapNode = isHighlightWrapNode;\n\n    hooks: HookMap;\n\n    painter: Painter;\n\n    cache: Cache;\n\n    private _hoverId: string;\n\n    private options: HighlighterOptions;\n\n    private readonly event: IInteraction;\n\n    constructor(options?: HighlighterOptions) {\n        super();\n        this.options = getDefaultOptions();\n        // initialize hooks\n        this.hooks = this._getHooks();\n        this.setOption(options);\n\n        this.event = getInteraction(this.options.window);\n\n        // initialize cache\n        this.cache = new Cache();\n\n        const $root = this.options.$root;\n\n        // initialize event listener\n        addEventListener($root, this.event.PointerOver, this._handleHighlightHover);\n        // initialize event listener\n        addEventListener($root, this.event.PointerTap, this._handleHighlightClick);\n        eventEmitter.on(INTERNAL_ERROR_EVENT, this._handleError);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    static isHighlightSource = (d: any) => !!d.__isHighlightSource;\n\n    run = () => addEventListener(this.options.$root, this.event.PointerEnd, this._handleSelection);\n\n    stop = () => {\n        removeEventListener(this.options.$root, this.event.PointerEnd, this._handleSelection);\n    };\n\n    addClass = (className: string, id?: string) => {\n        this.getDoms(id).forEach($n => {\n            addClass($n, className);\n        });\n    };\n\n    removeClass = (className: string, id?: string) => {\n        this.getDoms(id).forEach($n => {\n            removeClass($n, className);\n        });\n    };\n\n    getIdByDom = ($node: HTMLElement): string => getHighlightId($node, this.options.$root);\n\n    getExtraIdByDom = ($node: HTMLElement): string[] => getExtraHighlightId($node, this.options.$root);\n\n    getDoms = (id?: string): HTMLElement[] =>\n        id\n            ? getHighlightById(this.options.$root, id, this.options.wrapTag)\n            : getHighlightsByRoot(this.options.$root, this.options.wrapTag);\n\n    dispose = () => {\n        const $root = this.options.$root;\n\n        removeEventListener($root, this.event.PointerOver, this._handleHighlightHover);\n        removeEventListener($root, this.event.PointerEnd, this._handleSelection);\n        removeEventListener($root, this.event.PointerTap, this._handleHighlightClick);\n        this.removeAll();\n    };\n\n    setOption = (options?: HighlighterOptions) => {\n        this.options = {\n            ...this.options,\n            ...options,\n        };\n        this.painter = new Painter(\n            {\n                $root: this.options.$root,\n                rootDocument: this.options.rootDocument,\n                wrapTag: this.options.wrapTag,\n                className: this.options.style.className,\n                exceptSelectors: this.options.exceptSelectors,\n            },\n            this.hooks,\n        );\n    };\n\n    fromRange = (range: Range): HighlightSource => {\n        const start: DomNode = {\n            $node: range.startContainer,\n            offset: range.startOffset,\n        };\n        const end: DomNode = {\n            $node: range.endContainer,\n            offset: range.endOffset,\n        };\n\n        const text = range.toString();\n        let id = this.hooks.Render.UUID.call(start, end, text);\n\n        id = typeof id !== 'undefined' && id !== null ? id : uuid();\n\n        const hRange = new HighlightRange(start, end, text, id);\n\n        if (!hRange) {\n            eventEmitter.emit(INTERNAL_ERROR_EVENT, {\n                type: ERROR.RANGE_INVALID,\n            });\n\n            return null;\n        }\n\n        return this._highlightFromHRange(hRange);\n    };\n\n    fromStore = (start: DomMeta, end: DomMeta, text: string, id: string, extra?: unknown): HighlightSource => {\n        const hs = new HighlightSource(start, end, text, id, extra);\n\n        try {\n            this._highlightFromHSource(hs);\n\n            return hs;\n        } catch (err: unknown) {\n            eventEmitter.emit(INTERNAL_ERROR_EVENT, {\n                type: ERROR.HIGHLIGHT_SOURCE_RECREATE,\n                error: err,\n                detail: hs,\n            });\n\n            return null;\n        }\n    };\n\n    remove(id: string) {\n        if (!id) {\n            return;\n        }\n\n        const doseExist = this.painter.removeHighlight(id);\n\n        this.cache.remove(id);\n\n        // only emit REMOVE event when highlight exist\n        if (doseExist) {\n            this.emit(EventType.REMOVE, { ids: [id] }, this);\n        }\n    }\n\n    removeAll() {\n        this.painter.removeAllHighlight();\n\n        const ids = this.cache.removeAll();\n\n        this.emit(EventType.REMOVE, { ids }, this);\n    }\n\n    private readonly _getHooks = (): HookMap => ({\n        Render: {\n            UUID: new Hook('Render.UUID'),\n            SelectedNodes: new Hook('Render.SelectedNodes'),\n            WrapNode: new Hook('Render.WrapNode'),\n        },\n        Serialize: {\n            Restore: new Hook('Serialize.Restore'),\n            RecordInfo: new Hook('Serialize.RecordInfo'),\n        },\n        Remove: {\n            UpdateNodes: new Hook('Remove.UpdateNodes'),\n        },\n    });\n\n    private readonly _highlightFromHRange = (range: HighlightRange): HighlightSource => {\n        const source: HighlightSource = range.serialize(this.options.$root, this.hooks);\n        const $wraps = this.painter.highlightRange(range);\n\n        if ($wraps.length === 0) {\n            eventEmitter.emit(INTERNAL_ERROR_EVENT, {\n                type: ERROR.DOM_SELECTION_EMPTY,\n            });\n\n            return null;\n        }\n\n        this.cache.save(source);\n        this.emit(EventType.CREATE, { sources: [source], type: CreateFrom.INPUT }, this);\n\n        return source;\n    };\n\n    private _highlightFromHSource(sources: HighlightSource | HighlightSource[] = []) {\n        const renderedSources: HighlightSource[] = this.painter.highlightSource(sources);\n\n        this.emit(EventType.CREATE, { sources: renderedSources, type: CreateFrom.STORE }, this);\n        this.cache.save(sources);\n    }\n\n    private readonly _handleSelection = () => {\n        // rootDocument is Document | undefined, but function types it as Document\n        // TS doesn't care atm since strictnNullChecks is off\n        // TODO (caly): fix this\n        const range = HighlightRange.fromSelection(this.hooks.Render.UUID, this.options.rootDocument);\n\n        if (range) {\n            this._highlightFromHRange(range);\n            HighlightRange.removeDomRange(this.options.rootDocument);\n        }\n    };\n\n    private readonly _handleHighlightHover = (e: MouseEvent | TouchEvent) => { const $target = e.target as HTMLElement;\n\n        if (!isHighlightWrapNode($target)) {\n            this._hoverId && this.emit(EventType.HOVER_OUT, { id: this._hoverId }, this, e);\n            this._hoverId = null;\n\n            return;\n        }\n\n        const id = getHighlightId($target, this.options.$root);\n\n        // prevent trigger in the same highlight range\n        if (this._hoverId === id) {\n            return;\n        }\n\n        // hover another highlight range, need to trigger previous highlight hover out event\n        if (this._hoverId) {\n            this.emit(EventType.HOVER_OUT, { id: this._hoverId }, this, e);\n        }\n\n        this._hoverId = id;\n        this.emit(EventType.HOVER, { id: this._hoverId }, this, e);\n    };\n\n    private readonly _handleError = (type: { type: ERROR; detail?: HighlightSource; error?: any }) => {\n        if (this.options.verbose) {\n            // eslint-disable-next-line no-console\n            console.warn(type);\n        }\n    };\n\n    private readonly _handleHighlightClick = (e: MouseEvent | TouchEvent) => {\n        const $target = e.target as HTMLElement;\n\n        if (isHighlightWrapNode($target)) {\n            const id = getHighlightId($target, this.options.$root);\n\n            this.emit(EventType.CLICK, { id }, this, e);\n        }\n    };\n}\n", "import type { DomNode } from '@src//types';\nimport type HighlightSource from '@src/model/source/index';\nimport { ROOT_IDX } from '@src/util/const';\n\n/**\n * Because of supporting highlighting a same area (range overlapping),\n * Highlighter will calculate which text-node and how much offset it actually be,\n * based on the origin website dom node and the text offset.\n *\n * @param {Node} $parent element node in the origin website dom tree\n * @param {number} offset text offset in the origin website dom tree\n * @return {DomNode} DOM a dom info object\n */\nexport const getTextChildByOffset = ($parent: Node, offset: number): DomNode => {\n    const nodeStack: Node[] = [$parent];\n\n    let $curNode: Node = null;\n    let curOffset = 0;\n    let startOffset = 0;\n\n    while (($curNode = nodeStack.pop())) {\n        const children = $curNode.childNodes;\n\n        for (let i = children.length - 1; i >= 0; i--) {\n            nodeStack.push(children[i]);\n        }\n\n        if ($curNode.nodeType === 3) {\n            startOffset = offset - curOffset;\n            curOffset += $curNode.textContent.length;\n\n            if (curOffset >= offset) {\n                break;\n            }\n        }\n    }\n\n    if (!$curNode) {\n        $curNode = $parent;\n    }\n\n    return {\n        $node: $curNode,\n        offset: startOffset,\n    };\n};\n\n/**\n * get start and end parent element from meta info\n *\n * @param {HighlightSource} hs\n * @param {HTMLElement | Document} $root root element, default document\n * @return {Object}\n */\nexport const queryElementNode = (hs: HighlightSource, $root: Document | HTMLElement): { start: Node; end: Node } => {\n    const start =\n        hs.startMeta.parentIndex === ROOT_IDX\n            ? $root\n            : $root.getElementsByTagName(hs.startMeta.parentTagName)[hs.startMeta.parentIndex];\n    const end =\n        hs.endMeta.parentIndex === ROOT_IDX\n            ? $root\n            : $root.getElementsByTagName(hs.endMeta.parentTagName)[hs.endMeta.parentIndex];\n\n    return { start, end };\n};\n", "/**\n * convert dash-joined string to camel case\n */\n\nexport default (a: string): string =>\n    a.split('-').reduce((str, s, idx) => str + (idx === 0 ? s : s[0].toUpperCase() + s.slice(1)), '');\n", "/**\n * Something about the Selection/Range API in browsers.\n * If you want to use <PERSON><PERSON><PERSON> in some old browsers, you may use a polyfill.\n * https://caniuse.com/#search=selection\n */\n\nexport const getDomRange = (rootDocument: Document): Range => {\n    const selection = rootDocument.getSelection();\n\n    if (selection.isCollapsed) {\n        // eslint-disable-next-line no-console\n        console.debug('no text selected');\n\n        return null;\n    }\n\n    return selection.getRangeAt(0);\n};\n\nexport const removeSelection = (rootDocument: Document): void => {\n    rootDocument.getSelection().removeAllRanges();\n};\n", "/**\n * some dom operations about HighlightRange\n */\n\nimport type { <PERSON><PERSON><PERSON>, DomNode } from '@src/types';\nimport { CAMEL_DATASET_IDENTIFIER, ROOT_IDX, UNKNOWN_IDX } from '@src/util/const';\n\nconst countGlobalNodeIndex = ($node: Node, $root: Document | HTMLElement): number => {\n    const tagName = ($node as HTMLElement).tagName;\n    const $list = $root.getElementsByTagName(tagName);\n\n    for (let i = 0; i < $list.length; i++) {\n        if ($node === $list[i]) {\n            return i;\n        }\n    }\n\n    return UNKNOWN_IDX;\n};\n\n/**\n * text total length in all predecessors (text nodes) in the root node\n * (without offset in current node)\n */\nconst getTextPreOffset = ($root: Node, $text: Node): number => {\n    const nodeStack: Node[] = [$root];\n\n    let $curNode: Node = null;\n    let offset = 0;\n\n    while (($curNode = nodeStack.pop())) {\n        const children = $curNode.childNodes;\n\n        for (let i = children.length - 1; i >= 0; i--) {\n            nodeStack.push(children[i]);\n        }\n\n        if ($curNode.nodeType === 3 && $curNode !== $text) {\n            offset += $curNode.textContent.length;\n        } else if ($curNode.nodeType === 3) {\n            break;\n        }\n    }\n\n    return offset;\n};\n\n/**\n * find the original dom parent node (none highlight dom)\n */\nconst getOriginParent = ($node: HTMLElement | Text): HTMLElement => {\n    if ($node instanceof HTMLElement && (!$node.dataset || !$node.dataset[CAMEL_DATASET_IDENTIFIER])) {\n        return $node;\n    }\n\n    let $originParent = $node.parentNode as HTMLElement;\n\n    while ($originParent?.dataset[CAMEL_DATASET_IDENTIFIER]) {\n        $originParent = $originParent.parentNode as HTMLElement;\n    }\n\n    return $originParent;\n};\n\nexport const getDomMeta = ($node: HTMLElement | Text, offset: number, $root: Document | HTMLElement): DomMeta => {\n    const $originParent = getOriginParent($node);\n    const index = $originParent === $root ? ROOT_IDX : countGlobalNodeIndex($originParent, $root);\n    const preNodeOffset = getTextPreOffset($originParent, $node);\n    const tagName = $originParent.tagName;\n\n    return {\n        parentTagName: tagName,\n        parentIndex: index,\n        textOffset: preNodeOffset + offset,\n    };\n};\n\nexport const formatDomNode = (n: DomNode): DomNode => {\n    if (\n        // Text\n        n.$node.nodeType === 3 ||\n        // CDATASection\n        n.$node.nodeType === 4 ||\n        // Comment\n        n.$node.nodeType === 8\n    ) {\n        return n;\n    }\n\n    // dont use this yet because it somehow causes the scroll to first highlight to break sometimes\n    const nodeToPass = n.$node.childNodes.length > n.offset ? n.$node.childNodes[n.offset] : n.$node\n\n    const closestTextNode = getClosestTextNode(n.$node.childNodes[n.offset])\n\n    return {\n        $node: closestTextNode,\n        offset: 0,\n    };\n};\n\nexport function getClosestTextNode(node: Node): Node | null {\n    if (node.nodeType == node.TEXT_NODE) {\n        return node\n    }\n\n    for (let childNode of node.childNodes) {\n        const closestTextNode = getClosestTextNode(childNode)\n\n        if (closestTextNode !== null && closestTextNode.nodeType == node.TEXT_NODE) {\n            return closestTextNode\n        }\n    }\n\n    return null\n}\n", "/**\n * simple hook\n * webpack-plugin-liked api\n */\n\ntype HookCallback<T> = (...args: unknown[]) => T;\n\nclass Hook<T = unknown> {\n    name = '';\n\n    private readonly ops: HookCallback<T>[] = [];\n\n    constructor(name?) {\n        this.name = name;\n    }\n\n    tap(cb: HookCallback<T>) {\n        if (this.ops.indexOf(cb) === -1) {\n            this.ops.push(cb);\n        }\n\n        return () => {\n            this.remove(cb);\n        };\n    }\n\n    remove(cb: HookCallback<T>) {\n        const idx = this.ops.indexOf(cb);\n\n        if (idx < 0) {\n            return;\n        }\n\n        this.ops.splice(idx, 1);\n    }\n\n    isEmpty() {\n        return this.ops.length === 0;\n    }\n\n    call(...args: unknown[]) {\n        let ret: T;\n\n        this.ops.forEach(op => {\n            ret = op(...args);\n        });\n\n        return ret;\n    }\n}\n\nexport default Hook;\n", "/**\n * adapter for mobile and desktop events\n */\n\nimport type { IInteraction } from '@src/types';\nimport { UserInputEvent } from '@src/types';\nimport detectMobile from '@src/util/is.mobile';\n\nexport default (window: Window): IInteraction => {\n    const isMobile = detectMobile(window.navigator.userAgent);\n\n    const interaction: IInteraction = {\n        PointerEnd: isMobile ? UserInputEvent.touchend : UserInputEvent.mouseup,\n        PointerTap: isMobile ? UserInputEvent.touchstart : UserInputEvent.click,\n        // hover and click will be the same event in mobile\n        PointerOver: isMobile ? UserInputEvent.touchstart : UserInputEvent.mouseover,\n    };\n\n    return interaction;\n};\n", "/**\n * is mobile device?\n */\n\nconst regMobile = /Android|iPhone|BlackBerry|BB10|Opera Mini|Phone|Mobile|Silk|Windows Phone|Mobile(?:.+)Firefox\\b/i;\n\nexport default (userAgent: string) => regMobile.test(userAgent);\n", "import EventEmitter from '@src/util/event.emitter';\nimport type HighlightSource from '@src/model/source';\nimport { ERROR } from '@src/types';\n\nclass Cache extends EventEmitter {\n    private _data: Map<string, HighlightSource> = new Map();\n\n    get data() {\n        return this.getAll();\n    }\n\n    set data(map) {\n        throw ERROR.CACHE_SET_ERROR;\n    }\n\n    save(source: HighlightSource | HighlightSource[]): void {\n        if (!Array.isArray(source)) {\n            this._data.set(source.id, source);\n\n            return;\n        }\n\n        source.forEach(s => this._data.set(s.id, s));\n    }\n\n    get(id: string): HighlightSource {\n        return this._data.get(id);\n    }\n\n    remove(id: string): void {\n        this._data.delete(id);\n    }\n\n    getAll(): HighlightSource[] {\n        const list: HighlightSource[] = [];\n\n        for (const pair of this._data) {\n            list.push(pair[1]);\n        }\n\n        return list;\n    }\n\n    removeAll(): string[] {\n        const ids: string[] = [];\n\n        for (const pair of this._data) {\n            ids.push(pair[0]);\n        }\n\n        this._data = new Map();\n\n        return ids;\n    }\n}\n\nexport default Cache;\n", "/**\n * Painter object is designed for some painting work about higlighting,\n * including rendering, cleaning...\n * No need to instantiate repeatly. A Highlighter instance will bind a Painter instance.\n */\n\nimport type HighlightRange from '@src/model/range';\nimport type { PainterOptions, HookMap } from '@src/types';\nimport HighlightSource from '@src/model/source';\nimport { wrapHighlight, getSelectedNodes, normalizeSiblingText } from '@src/painter/dom';\nimport { getHighlightsByRoot, forEach, addClass, removeAllClass } from '@src/util/dom';\nimport { ERROR } from '@src/types';\nimport { initDefaultStylesheet } from '@src/painter/style';\nimport {\n    ID_DIVISION,\n    eventEmitter,\n    DATASET_IDENTIFIER,\n    INTERNAL_ERROR_EVENT,\n    CAMEL_DATASET_IDENTIFIER,\n    CAMEL_DATASET_IDENTIFIER_EXTRA,\n} from '@src/util/const';\n\nexport default class Painter {\n    options: PainterOptions;\n\n    $style: HTMLStyleElement;\n\n    styleId: string;\n\n    hooks: HookMap;\n\n    constructor(options: PainterOptions, hooks: HookMap) {\n        this.options = {\n            $root: options.$root,\n            rootDocument: options.rootDocument,\n            wrapTag: options.wrapTag,\n            exceptSelectors: options.exceptSelectors,\n            className: options.className,\n        };\n        this.hooks = hooks;\n\n        initDefaultStylesheet(this.options.rootDocument);\n    }\n\n    /* =========================== render =========================== */\n    highlightRange(range: HighlightRange): HTMLElement[] {\n        if (!range.frozen) {\n            throw ERROR.HIGHLIGHT_RANGE_FROZEN;\n        }\n\n        const { $root, rootDocument, className, exceptSelectors } = this.options;\n        const hooks = this.hooks;\n\n        let $selectedNodes = getSelectedNodes($root, rootDocument, range.start, range.end, exceptSelectors);\n\n        if (!hooks.Render.SelectedNodes.isEmpty()) {\n            $selectedNodes = hooks.Render.SelectedNodes.call(range.id, $selectedNodes) || [];\n        }\n\n        return $selectedNodes.map(n => {\n            let $node = wrapHighlight(n, range, className, this.options.wrapTag, rootDocument);\n\n            if (!hooks.Render.WrapNode.isEmpty()) {\n                $node = hooks.Render.WrapNode.call(range.id, $node);\n            }\n\n            return $node;\n        });\n    }\n\n    highlightSource(sources: HighlightSource | HighlightSource[]): HighlightSource[] {\n        const list = Array.isArray(sources) ? sources : [sources];\n\n        const renderedSources: HighlightSource[] = [];\n\n        list.forEach(s => {\n            if (!(s instanceof HighlightSource)) {\n                eventEmitter.emit(INTERNAL_ERROR_EVENT, {\n                    type: ERROR.SOURCE_TYPE_ERROR,\n                });\n\n                return;\n            }\n\n            const range = s.deSerialize(this.options.$root, this.hooks);\n            const $nodes = this.highlightRange(range);\n\n            if ($nodes.length > 0) {\n                renderedSources.push(s);\n            } else {\n                eventEmitter.emit(INTERNAL_ERROR_EVENT, {\n                    type: ERROR.HIGHLIGHT_SOURCE_NONE_RENDER,\n                    detail: s,\n                });\n            }\n        });\n\n        return renderedSources;\n    }\n    /* ============================================================== */\n\n    /* =========================== clean =========================== */\n    // id: target id - highlight with this id should be clean\n    // if there is no highlight for this id, it will return false, vice versa\n    removeHighlight(id: string): boolean {\n        // whether extra ids contains the target id\n        const reg = new RegExp(`(${id}\\\\${ID_DIVISION}|\\\\${ID_DIVISION}?${id}$)`);\n\n        const hooks = this.hooks;\n        const wrapTag = this.options.wrapTag;\n\n        const $spans = this.options.rootDocument.querySelectorAll<HTMLElement>(\n            `${wrapTag}[data-${DATASET_IDENTIFIER}]`,\n        );\n\n        // nodes to remove\n        const $toRemove: HTMLElement[] = [];\n        // nodes to update main id\n        const $idToUpdate: HTMLElement[] = [];\n        // nodes to update extra id\n        const $extraToUpdate: HTMLElement[] = [];\n\n        for (const $s of $spans) {\n            const spanId = $s.dataset[CAMEL_DATASET_IDENTIFIER];\n            const spanExtraIds = $s.dataset[CAMEL_DATASET_IDENTIFIER_EXTRA];\n\n            // main id is the target id and no extra ids --> to remove\n            if (spanId === id && !spanExtraIds) {\n                $toRemove.push($s);\n            }\n            // main id is the target id but there is some extra ids -> update main id & extra id\n            else if (spanId === id) {\n                $idToUpdate.push($s);\n            }\n            // main id isn't the target id but extra ids contains it -> just remove it from extra id\n            else if (spanId !== id && reg.test(spanExtraIds)) {\n                $extraToUpdate.push($s);\n            }\n        }\n\n        $toRemove.forEach($s => {\n            const $parent = $s.parentNode;\n            const $fr = this.options.rootDocument.createDocumentFragment();\n\n            forEach($s.childNodes, ($c: Node) => $fr.appendChild($c.cloneNode(false)));\n\n            const $prev = $s.previousSibling;\n            const $next = $s.nextSibling;\n\n            $parent.replaceChild($fr, $s);\n            // there are bugs in IE11, so use a more reliable function\n            normalizeSiblingText($prev, true);\n            normalizeSiblingText($next, false);\n            hooks.Remove.UpdateNodes.call(id, $s, 'remove');\n        });\n\n        $idToUpdate.forEach($s => {\n            const dataset = $s.dataset;\n            const ids = dataset[CAMEL_DATASET_IDENTIFIER_EXTRA].split(ID_DIVISION);\n            const newId = ids.shift();\n\n            // find overlapped wrapper associated with \"extra id\"\n            const $overlapSpan = this.options.rootDocument.querySelector<HTMLElement>(\n                `${wrapTag}[data-${DATASET_IDENTIFIER}=\"${newId}\"]`,\n            );\n\n            if ($overlapSpan) {\n                // empty the current class list\n                removeAllClass($s);\n                // retain the class list of the overlapped wrapper which associated with \"extra id\"\n                addClass($s, [...$overlapSpan.classList]);\n            }\n\n            dataset[CAMEL_DATASET_IDENTIFIER] = newId;\n            dataset[CAMEL_DATASET_IDENTIFIER_EXTRA] = ids.join(ID_DIVISION);\n\n            hooks.Remove.UpdateNodes.call(id, $s, 'id-update');\n        });\n\n        $extraToUpdate.forEach($s => {\n            const extraIds = $s.dataset[CAMEL_DATASET_IDENTIFIER_EXTRA];\n\n            $s.dataset[CAMEL_DATASET_IDENTIFIER_EXTRA] = extraIds.replace(reg, '');\n            hooks.Remove.UpdateNodes.call(id, $s, 'extra-update');\n        });\n\n        return $toRemove.length + $idToUpdate.length + $extraToUpdate.length !== 0;\n    }\n\n    removeAllHighlight() {\n        const { wrapTag, $root, rootDocument } = this.options;\n        const $spans = getHighlightsByRoot($root, wrapTag);\n\n        $spans.forEach($s => {\n            const $parent = $s.parentNode;\n            const $fr = rootDocument.createDocumentFragment();\n\n            forEach($s.childNodes, ($c: Node) => $fr.appendChild($c.cloneNode(false)));\n            $parent.replaceChild($fr, $s);\n\n            // rejoin the text nodes to prevent mutating the DOM and causing issues with sentence highlights\n            $parent.normalize();\n        });\n    }\n    /* ============================================================== */\n}\n", "import type HighlightRange from '@src/model/range';\nimport type { SelectedNode, DomNode } from '@src/types';\nimport { SplitType, SelectedNodeType } from '@src/types';\nimport { hasClass, addClass as addElementClass, isHighlightWrapNode, removeAllClass } from '@src/util/dom';\nimport {\n    ID_DIVISION,\n    getDefaultOptions,\n    CAMEL_DATASET_IDENTIFIER,\n    CAMEL_DATASET_IDENTIFIER_EXTRA,\n    DATASET_IDENTIFIER,\n    DATASET_SPLIT_TYPE,\n    DATASET_IDENTIFIER_EXTRA,\n} from '../util/const';\nimport { unique } from '../util/tool';\n\n/**\n * 支持的选择器类型\n *  - class: .title, .main-nav\n *  - id: #nav, #js-toggle-btn\n *  - tag: div, p, span\n */\nconst isMatchSelector = ($node: HTMLElement, selector: string): boolean => {\n    if (!$node) {\n        return false;\n    }\n\n    if (/^\\./.test(selector)) {\n        const className = selector.replace(/^\\./, '');\n\n        return $node && hasClass($node, className);\n    } else if (/^#/.test(selector)) {\n        const id = selector.replace(/^#/, '');\n\n        return $node && $node.id === id;\n    }\n\n    const tagName = selector.toUpperCase();\n\n    return $node && $node.tagName === tagName;\n};\n\n/**\n * If start node and end node is the same, don't need to tranvers the dom tree.\n */\nconst getNodesIfSameStartEnd = (\n    $startNode: Text,\n    startOffset: number,\n    endOffset: number,\n    exceptSelectors?: string[],\n) => {\n    let $element = $startNode as Node;\n\n    const isExcepted = ($e: HTMLElement) => exceptSelectors?.some(s => isMatchSelector($e, s));\n\n    while ($element) {\n        if ($element.nodeType === 1 && isExcepted($element as HTMLElement)) {\n            return [];\n        }\n\n        $element = $element.parentNode;\n    }\n\n    $startNode.splitText(startOffset);\n\n    const passedNode = $startNode.nextSibling as Text;\n\n    passedNode.splitText(endOffset - startOffset);\n\n    return [\n        {\n            $node: passedNode,\n            type: SelectedNodeType.text,\n            splitType: SplitType.both,\n        },\n    ];\n};\n\n/**\n * get all the dom nodes between the start and end node\n */\nexport const getSelectedNodes = (\n    $root: Document | HTMLElement,\n    rootDocument: Document,\n    start: DomNode,\n    end: DomNode,\n    exceptSelectors: string[],\n): SelectedNode[] => {\n    const $startNode = start.$node;\n    const $endNode = end.$node;\n    const startOffset = start.offset;\n    const endOffset = end.offset;\n\n    // assumes web-highlighter is running in top-level page so document is the top-level document\n\n    // split current node when the start-node and end-node is the same\n    if ($startNode === $endNode && $startNode instanceof rootDocument.defaultView.Text) {\n        return getNodesIfSameStartEnd($startNode, startOffset, endOffset, exceptSelectors);\n    }\n\n    const nodeStack: (ChildNode | Document | HTMLElement | Text)[] = [$root];\n    const selectedNodes: SelectedNode[] = [];\n\n    const isExcepted = ($e: HTMLElement) => exceptSelectors?.some(s => isMatchSelector($e, s));\n\n    let withinSelectedRange = false;\n    let curNode: Node = null;\n\n    while ((curNode = nodeStack.pop())) {\n        // do not traverse the excepted node\n        if (curNode.nodeType === 1 && isExcepted(curNode as HTMLElement)) {\n            continue;\n        }\n\n        const children = curNode.childNodes;\n\n        for (let i = children.length - 1; i >= 0; i--) {\n            nodeStack.push(children[i]);\n        }\n\n        // only collect text nodes\n        if (curNode === $startNode) {\n            if (curNode.nodeType === 3) {\n                (curNode as Text).splitText(startOffset);\n\n                const node = curNode.nextSibling as Text;\n\n                selectedNodes.push({\n                    $node: node,\n                    type: SelectedNodeType.text,\n                    splitType: SplitType.head,\n                });\n            }\n\n            // meet the start-node (begin to traverse)\n            withinSelectedRange = true;\n        } else if (curNode === $endNode) {\n            if (curNode.nodeType === 3) {\n                const node = curNode as Text;\n\n                node.splitText(endOffset);\n                selectedNodes.push({\n                    $node: node,\n                    type: SelectedNodeType.text,\n                    splitType: SplitType.tail,\n                });\n            }\n\n            // meet the end-node\n            break;\n        }\n        // handle text nodes between the range\n        else if (withinSelectedRange && curNode.nodeType === 3) {\n            // skip nodes with only whitespace to avoid creating spurious wrapping nodes\n            // that shift the layout\n            if (curNode.textContent.trim() === '') continue;\n\n            selectedNodes.push({\n                $node: curNode as Text,\n                type: SelectedNodeType.text,\n                splitType: SplitType.none,\n            });\n        }\n    }\n\n    return selectedNodes;\n};\n\nconst addClass = ($el: HTMLElement, className?: string[] | string): HTMLElement => {\n    let classNames = Array.isArray(className) ? className : [className];\n\n    classNames = classNames.length === 0 ? [getDefaultOptions().style.className] : classNames;\n    classNames.forEach(c => {\n        addElementClass($el, c);\n    });\n\n    return $el;\n};\n\nconst isNodeEmpty = ($n: Node): boolean => !$n || !$n.textContent;\n\n/**\n * Wrap a common wrapper.\n */\nconst wrapNewNode = (\n    selected: SelectedNode,\n    range: HighlightRange,\n    className: string[] | string,\n    wrapTag: string,\n    rootDocument: Document,\n): HTMLElement => {\n    const $wrap = rootDocument.createElement(wrapTag);\n\n    addClass($wrap, className);\n\n    $wrap.appendChild(selected.$node.cloneNode(false));\n    selected.$node.parentNode.replaceChild($wrap, selected.$node);\n\n    $wrap.setAttribute(`data-${DATASET_IDENTIFIER}`, range.id);\n    $wrap.setAttribute(`data-${DATASET_SPLIT_TYPE}`, selected.splitType);\n    $wrap.setAttribute(`data-${DATASET_IDENTIFIER_EXTRA}`, '');\n\n    return $wrap;\n};\n\n/**\n * Split and wrapper each one.\n */\nconst wrapPartialNode = (\n    selected: SelectedNode,\n    range: HighlightRange,\n    className: string[] | string,\n    wrapTag: string,\n    rootDocument: Document,\n): HTMLElement => {\n    const $wrap: HTMLElement = rootDocument.createElement(wrapTag);\n\n    const $parent = selected.$node.parentNode as HTMLElement;\n    const $prev = selected.$node.previousSibling;\n    const $next = selected.$node.nextSibling;\n    const $fr = rootDocument.createDocumentFragment();\n    const parentId = $parent.dataset[CAMEL_DATASET_IDENTIFIER];\n    const parentExtraId = $parent.dataset[CAMEL_DATASET_IDENTIFIER_EXTRA];\n    const extraInfo = parentExtraId ? parentId + ID_DIVISION + parentExtraId : parentId;\n\n    $wrap.setAttribute(`data-${DATASET_IDENTIFIER}`, range.id);\n    $wrap.setAttribute(`data-${DATASET_IDENTIFIER_EXTRA}`, extraInfo);\n    $wrap.appendChild(selected.$node.cloneNode(false));\n\n    let headSplit = false;\n    let tailSplit = false;\n    let splitType: SplitType;\n\n    if ($prev) {\n        const $span = $parent.cloneNode(false);\n\n        $span.textContent = $prev.textContent;\n        $fr.appendChild($span);\n        headSplit = true;\n    }\n\n    const classNameList: string[] = [];\n\n    if (Array.isArray(className)) {\n        classNameList.push(...className);\n    } else {\n        classNameList.push(className);\n    }\n\n    addClass($wrap, unique(classNameList));\n    $fr.appendChild($wrap);\n\n    if ($next) {\n        const $span = $parent.cloneNode(false);\n\n        $span.textContent = $next.textContent;\n        $fr.appendChild($span);\n        tailSplit = true;\n    }\n\n    if (headSplit && tailSplit) {\n        splitType = SplitType.both;\n    } else if (headSplit) {\n        splitType = SplitType.head;\n    } else if (tailSplit) {\n        splitType = SplitType.tail;\n    } else {\n        splitType = SplitType.none;\n    }\n\n    $wrap.setAttribute(`data-${DATASET_SPLIT_TYPE}`, splitType);\n    $parent.parentNode.replaceChild($fr, $parent);\n\n    return $wrap;\n};\n\n/**\n * Just update id info (no wrapper updated).\n */\nconst wrapOverlapNode = (selected: SelectedNode, range: HighlightRange, className: string[] | string): HTMLElement => {\n    const $parent = selected.$node.parentNode as HTMLElement;\n    const $wrap: HTMLElement = $parent;\n\n    removeAllClass($wrap);\n    addClass($wrap, className);\n\n    const dataset = $parent.dataset;\n    const formerId = dataset[CAMEL_DATASET_IDENTIFIER];\n\n    dataset[CAMEL_DATASET_IDENTIFIER] = range.id;\n    dataset[CAMEL_DATASET_IDENTIFIER_EXTRA] = dataset[CAMEL_DATASET_IDENTIFIER_EXTRA]\n        ? formerId + ID_DIVISION + dataset[CAMEL_DATASET_IDENTIFIER_EXTRA]\n        : formerId;\n\n    return $wrap;\n};\n\n/**\n * wrap a dom node with highlight wrapper\n *\n * Because of supporting the highlight-overlapping,\n * Highlighter can't just wrap all nodes in a simple way.\n * There are three types:\n *  - wrapping a whole new node (without any wrapper)\n *  - wrapping part of the node\n *  - wrapping the whole wrapped node\n */\nexport const wrapHighlight = (\n    selected: SelectedNode,\n    range: HighlightRange,\n    className: string[] | string,\n    wrapTag: string,\n    rootDocument: Document,\n): HTMLElement => {\n    const $parent = selected.$node.parentNode as HTMLElement;\n    const $prev = selected.$node.previousSibling;\n    const $next = selected.$node.nextSibling;\n\n    let $wrap: HTMLElement;\n\n    // text node, not in a highlight wrapper -> should be wrapped in a highlight wrapper\n    if (!isHighlightWrapNode($parent)) {\n        $wrap = wrapNewNode(selected, range, className, wrapTag, rootDocument);\n    }\n    // text node, in a highlight wrap -> should split the existing highlight wrapper\n    else if (isHighlightWrapNode($parent) && (!isNodeEmpty($prev) || !isNodeEmpty($next))) {\n        $wrap = wrapPartialNode(selected, range, className, wrapTag, rootDocument);\n    }\n    // completely overlap (with a highlight wrap) -> only add extra id info\n    else {\n        $wrap = wrapOverlapNode(selected, range, className);\n    }\n\n    return $wrap;\n};\n\n/**\n * merge the adjacent text nodes\n * .normalize() API has some bugs in IE11\n */\nexport const normalizeSiblingText = ($s: Node, isNext = true) => {\n    if (!$s || $s.nodeType !== 3) {\n        return;\n    }\n\n    const $sibling = isNext ? $s.nextSibling : $s.previousSibling;\n\n    if ($sibling.nodeType !== 3) {\n        return;\n    }\n\n    const text = $sibling.nodeValue;\n\n    $s.nodeValue = isNext ? $s.nodeValue + text : text + $s.nodeValue;\n    $sibling.parentNode.removeChild($sibling);\n};\n", "/**\n * support IE 10\n */\nexport const unique = <T>(arr: T[]): T[] => {\n    const res: T[] = [];\n\n    for (const el of arr) {\n        if (res.indexOf(el) === -1) {\n            res.push(el);\n        }\n    }\n\n    return res;\n};\n", "/**\n * inject styles\n */\nimport { STYLESHEET_ID, getStylesheet } from '@src/util/const';\n\nexport const initDefaultStylesheet = (rootDocument: Document) => {\n    const styleId = STYLESHEET_ID;\n\n    let $style: HTMLStyleElement = rootDocument.getElementById(styleId) as HTMLStyleElement;\n\n    if (!$style) {\n        const $cssNode = rootDocument.createTextNode(getStylesheet());\n\n        $style = rootDocument.createElement('style');\n        $style.id = styleId;\n        $style.appendChild($cssNode);\n        rootDocument.head.appendChild($style);\n    }\n\n    return $style;\n};\n"], "sourceRoot": ""}