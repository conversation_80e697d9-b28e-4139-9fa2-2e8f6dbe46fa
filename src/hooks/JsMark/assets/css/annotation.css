.annotation {
  position: absolute;
  top: 0;
  right: -220px;
  z-index: 99;
  overflow: hidden;
  border-color: rgba(0, 0, 255, 30%);
  border-left: 8px solid #ffffff;
  padding: 5px;
  width: 200px;
  font-size: 14px;
  font-family: "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333333;
  background: white !important;
  box-sizing: border-box;
  line-height: 1.5;

  /* transition: top .1s; */
}

.annotation .color-block {
  margin-right: 1px;
  border-bottom: 2px solid #ffffff;
  width: 19px;
  height: 19px;
  cursor: pointer;
}

.annotation .save {
  padding-top: 8px;
  padding-right: 15px;
  font-size: 13px;
  text-decoration: none;
  color: #0768fd;
  cursor: pointer;
}

.annotation .delete {
  padding-top: 8px;
  padding-right: 15px;
  font-size: 13px;
  text-decoration: none;
  cursor: pointer;
}

.annotation.marked {
  background-color: #ffffff;
}
