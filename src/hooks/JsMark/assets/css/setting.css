.setting {
  position: fixed;
  top: 0;

  /* border:1px solid; */
  left: -300px;
  z-index: 2;
  padding: 20px;
  width: 300px;
  height: 100%;
  background-color: #ffffff;
  box-shadow: 0 4px 4px rgb(0 0 0 / 6%), 0 4px 16px rgb(0 0 0 / 5%);
  transition: left 0.5s;
}

.setting .switch {
  position: absolute;
  top: 100px;
  left: 300px;
  border-left: none;
  border-radius: 0 10px 10px 0;
  padding: 5px;
  text-align: right;
  background: #ffffff;
  box-shadow: 3px 0 3px 0 rgb(144 144 144 / 29%);
  cursor: pointer;
  box-sizing: border-box;
}

.switch .svg {
  display: block;
  margin-bottom: 10px;
  margin-left: auto;
}

.switch .title {
  font-size: 17px;
  font-family: Circular, "Segoe UI", Helvetica, Arial, sans-serif;
}
