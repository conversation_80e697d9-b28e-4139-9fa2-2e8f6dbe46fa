import {NumberOrString} from "@/common/interface";

export interface configKVType {
  configKey?: string | undefined
  configValue?: string | number | undefined
  tagName?: NumberOrString
}

export interface antRecordsType {
  text?: string
  desc?: configKVType | string | undefined
  isEdit?: boolean
  uid: string
  offset?: number
  offsetTop?: number
  className?: string
  tagId?: string
  tagCode?: string
  position?: number
  content?: string
  itemId?: string | number | undefined
}

export interface markConfigItemType extends antRecordsType, configKVType {
  index?: number
  auditCommentInfo: {
    commentUserName: string
    comment: string
    commentTime: string
    isTypicalSample: boolean
  }
  id: NumberOrString
}

export interface markConfigItemBackendType extends antRecordsType, configKVType {
  index?: number
  auditCommentInfo: string
  id: NumberOrString
}
