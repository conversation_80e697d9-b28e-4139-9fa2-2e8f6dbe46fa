const LocalDataKey = 'data'
export function setLocal(markData) {
  const data = getLocal()
  const hitIndex = data.findIndex((item) => item.uid === markData.uid)
  if (hitIndex > -1) {
    data[hitIndex] = { ...data[hitIndex], ...markData }
  } else {
    data.push(markData)
  }
  localStorage.setItem(LocalDataKey, JSON.stringify(data))
}

export function resetLocalData(data=[]) {
  clearLocalData()
  localStorage.setItem(LocalDataKey, JSON.stringify(data))
}

export function getLocal() {
  return JSON.parse(localStorage.getItem(LocalDataKey)) || []
}

export function clearItemLocal(id) {
  let data = getLocal()
  data = data.filter((item) => item.uid !== id)
  localStorage.setItem(LocalDataKey, JSON.stringify(data))
}

export function clearLocalData() {
  localStorage.removeItem(LocalDataKey)
}
