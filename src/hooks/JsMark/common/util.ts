export function deepCopy(obj) {
  if (typeof obj !== 'object') return
  var newObj = obj instanceof Array ? [] : {}
  for (var key in obj) {
    if (obj.hasOwnProperty(key)) {
      newObj[key] =
        Object.prototype.toString.call(obj[key]) === '[object Array]'
          ? deepCopy(obj[key])
          : obj[key]
    }
  }
  return newObj
}

export function closest(target: HTMLElement | ParentNode, selector: string): string {
  let parentNode: ParentNode | null = target.parentNode
  if (parentNode === null) {
    return ''
  } else {
    parentNode = parentNode as ParentNode
    if ((parentNode as HTMLElement).classList.contains(selector)) {
      return (parentNode as HTMLElement).dataset['id'] || ''
    } else if ((parentNode as HTMLElement).classList.contains('mark-edit')) {
      return ''
    } else {
      return closest(parentNode, selector)
    }
  }
}