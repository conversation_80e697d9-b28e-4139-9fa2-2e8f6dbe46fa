import { nextTick, ref } from 'vue'

import rearrangeAnt from './common/IntervalAlgo.js'
import { deepCopy } from './common/util'

import { antRecordsType } from '@/hooks/JsMark/common/interface'
import { clearItemLocal, setLocal } from '@/hooks/JsMark/common/localStorage'
import useMark from '@/hooks/JsMark/useMark'
const { replaceMarkClass, deleteMark, markSelected, markSelector, markActive } =
  useMark()

let markedArr: HTMLElement[] = []

export default function () {
  const antRecords = ref<Array<antRecordsType>>([])

  //清除编辑状态的备注器
  function clearEditAnt() {
    const noDescIdxS: number[] = []
    antRecords.value.map((item, i) => {
      if (item.desc === '') {
        noDescIdxS.push(i)
      }
      item.isEdit = false
    })
  }

  //重新计算所有的位置
  function calcAntPositions({ offsetTop, hasEdit, uid }) {
    return
    const marked = document.querySelector(`[data-uid="${uid}"]`) as HTMLElement
    const height = marked.offsetHeight
    let top = offsetTop
    if (!offsetTop) {
      top = window.getComputedStyle(marked).top
      top = Number(top.slice(0, top.length - 2))
    }
    let markedData: HTMLElement[] = []
    const data = [top, height + top, marked]
    const dataS = markedArr.filter((item) => item[2] !== marked)
    if (hasEdit) {
      markedData = rearrangeAnt(data, deepCopy(dataS), 2)
    } else {
      markedData = markedArr = rearrangeAnt(data, dataS, 2)
    }

    markedData.forEach((item) => {
      item[2].style.top = item[0] + 'px'
    })
  }
  function addEditRecord(uid, top) {
    clearEditAnt()
    antRecords.value.push({
      uid,
      desc: '',
      isEdit: true,
    })
    nextTick(() => {
      // debugger
      calcAntPositions({ offsetTop: top, hasEdit: true, uid })
    })
  }
  function addAntRecords({
    uid,
    desc,
    offsetTop,
    isEdit = false,
  }: antRecordsType) {
    // debugger
    const activeAntIndex = antRecords.value.findIndex(
      (item) => item.uid === uid
    )

    if (activeAntIndex > -1) {
      if (isEdit) {
        clearEditAnt()
        markedArr.forEach((item) => {
          item[2].style.top = item[0] + 'px'
        })
      }
      const activeAnt = antRecords.value[activeAntIndex]
      desc && (activeAnt.desc = desc)
      activeAnt.isEdit = isEdit
    } else {
      antRecords.value.push({
        uid,
        desc: desc,
        isEdit: false,
      })
    }

    nextTick(() => {
      // debugger
      calcAntPositions({ offsetTop, uid, hasEdit: isEdit })
    })
  }

  function removeAnt(uid) {
    const activeAntIndex = antRecords.value.findIndex(
      (item) => item.uid === uid
    )
    if (activeAntIndex > -1) {
      antRecords.value.splice(activeAntIndex, 1)
    }
    const marked = document.querySelector(`[data-uid="${uid}"]`)
    const markedIndex = markedArr.findIndex((item) => item[2] === marked)
    if (activeAntIndex > -1) {
      markedArr.splice(markedIndex, 1)
    }
  }
  //设置侧边颜色
  function setAntBorderColor(uid) {
    return
    const eleArr = document.querySelector(
      `span[data-selector="${uid}"]`
    ) as HTMLElement
    const bg = window.getComputedStyle(eleArr).backgroundColor
    const marked = document.querySelector(`[data-uid="${uid}"]`) as HTMLElement
    marked.style.borderColor = bg
  }
  function scrollToCurrentConfig(uid) {
    const ele = document.querySelector(`.config-uid-${uid}`)
    ele?.scrollIntoView({
      behavior: 'smooth',
      inline: 'center',
      // block: 'center',
    })
  }

  function addSelectedClassByUid(uid: string) {
    const oldEleArr = document.querySelectorAll(`.${markActive}`)
    oldEleArr.length &&
      [...oldEleArr].forEach((node) => {
        node?.classList.remove(markActive)
      })
    const eleArr = document.querySelectorAll(`span[${markSelector}="${uid}"]`)
    eleArr.length &&
      [...eleArr].forEach((node) => {
        node?.classList.add(markActive)
      })
  }
  function deleteAnnotation(uid) {
    deleteMark(uid)
    removeAnt(uid)
    clearItemLocal(uid)
  }

  function setMarkClassByUid(uid, className) {
    replaceMarkClass(uid, `${className} ${markSelected} ${markActive}`)
    setLocal({ uid, className: className })

    nextTick(() => {
      setAntBorderColor(uid)
    })
  }
  return {
    setAntBorderColor,
    antRecords,
    addEditRecord,
    addAntRecords,
    removeAnt,
    scrollToCurrentConfig,
    addSelectedClassByUid,
    deleteAnnotation,
    setMarkClassByUid,
  }
}
