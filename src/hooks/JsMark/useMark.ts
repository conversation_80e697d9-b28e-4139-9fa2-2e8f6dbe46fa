import JsMark from '@/plugins/JsMark'

import type { antRecordsType } from '@/hooks/JsMark/common/interface'
import { setLocal } from '@/hooks/JsMark/common/localStorage'

let jsMark

export default function () {
  const SearchUid = 'pro-checked-uid'
  const markSelector = 'data-selector'
  const markSelected = 'mark-selected'
  const markActive = 'mark-active'
  const ignoreClass = ['disabled', 'ignore']
  function initMark(el, className, cb) {
    jsMark = new JsMark({
      el,
      options: {
        isCover: true,
        beautify: true,
        ignoreClass,
        className,
      },
    })

    //选中文本后回调
    jsMark.onSelected = function (res) {
      // console.log('on selected: ', res)
      const classNames = res.storeRenderOther.className || jsMark.getClassName()
      const { uid, offsetTop } = jsMark.repaintRange({
        uuid: res.storeRenderOther.uid,
        textNodes: res.textNodes,
        className: `${classNames} ${markSelected}`,
      })
      cb.onSelected &&
        cb.onSelected({
          ...res,
          id: res.storeRenderOther.id,
          auditCommentInfo: res.storeRenderOther.auditCommentInfo,
          uid,
          offsetTop,
          desc: res.storeRenderOther.desc,
          hasStoreRender: res.hasStoreRender,
          text: res.text,
          className: classNames,
        } as antRecordsType)

      if (!res.hasStoreRender) {
        setLocal({
          offset: res.offset,
          text: res.text,
          id: res.storeRenderOther.id,
          auditCommentInfo: res.storeRenderOther.auditCommentInfo,
          uid,
          desc: '',
          className: classNames,
        })
      }
    }
    //点击高亮节点后显示
    jsMark.onClick = function ({ uid, offsetTop, offsetLeft }) {
      cb.onClick && cb.onClick({ uid, offsetTop, offsetLeft })
    }
  }

  function setClassName(className) {
    jsMark.setClassName(className)
  }

  function deleteMark(uid) {
    jsMark.deleteMark(uid)
    const ignoreEle = document.getElementById(uid)
    if (ignoreEle) {
      ignoreEle?.parentNode?.removeChild(ignoreEle)
    }
    jsMark.beautifyHTML()
  }

  function clearMarkAll() {
    const eleArr = document.querySelectorAll(`.${markSelected}`)
    ;[...eleArr].forEach((item) => {
      const uid = item.getAttribute(markSelector)
      deleteMark(uid)
    })
  }

  function renderStore(data) {
    jsMark && jsMark.renderStore(data)
  }

  function search(value) {
    jsMark.repaintRange({
      uuid: SearchUid,
      className: 'annotator-h3',
      textNodes: jsMark.findWord(value),
    })
  }

  function replaceMarkClass(uid, className) {
    jsMark.replaceMarkClass(uid, className)
  }

  function destroy() {
    jsMark.destroy()
  }

  return {
    initMark,
    search,
    deleteMark,
    clearMarkAll,
    renderStore,
    destroy,
    replaceMarkClass,
    SearchUid,
    markSelector,
    markSelected,
    setClassName,
    markActive,
  }
}
