import { useRoute } from 'vue-router'

import { RoutePathCodeEnum } from '@/router/routes'

import { useUserStore } from '@/store/useUserStore'

export const useAuth = (path = '') => {
  const Route = useRoute()
  const store = useUserStore()
  const code = RoutePathCodeEnum[path || Route.path]
  // console.log({ code })
  const isAuth = store.permissionCode.includes(code)

  return code && !isAuth
}
