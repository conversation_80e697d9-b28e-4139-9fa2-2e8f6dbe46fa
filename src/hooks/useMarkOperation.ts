import { computed } from 'vue'
import { useRoute } from 'vue-router'

export const useMarkOperation = () => {
  const route = useRoute()
  const routeInfo = computed(() => route.query)

  return computed(() => {
    return (
      (routeInfo.value.status === 'update' &&
        (routeInfo.value.viewMode === 'audit' ||
          routeInfo.value.viewMode === 'anno') &&
        (routeInfo.value.annoStatus === '1' ||
          routeInfo.value.annoStatus === '0' ||
          routeInfo.value.annoStatus === '4')) ||
      (routeInfo.value.status === 'approve' &&
        (routeInfo.value.viewMode === 'audit' ||
          routeInfo.value.viewMode === 'anno'))
    )
  })
}
