import { createApp } from 'vue'
import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

import App from '@/App.vue'

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { ajax } from '@/api/ajax'

import './styles/index.scss'
import './styles/theme.scss'
import 'uno.css'
// import 'virtual:uno.css';
import 'element-plus/es/components/message/style/css'
import 'element-plus/es/components/loading/style/css'
import 'element-plus/es/components/message-box/style/css'
import 'element-plus/es/components/drawer/style/css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { createPinia } from 'pinia'

import '@/assets/css/style.scss'

import { routes } from '@/router/routes'

const pinia = createPinia()
// const dev = process.env.NODE_ENV === 'development'

let app: any = null
let router: any = null
let history: any = null

const baseRoute = window.__MICRO_APP_ENVIRONMENT__
  ? 'aso-annotation'
  : 'annotation'

function onMount() {
  app = createApp(App)
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  app.config.globalProperties.$http = { ajax }
  history = createWebHistory(baseRoute)
  router = createRouter({
    history,
    routes: routes as RouteRecordRaw[],
  })
  router.beforeEach((to, _from, next) => {
    document.title = to.meta.title as string
    window.scroll(0, 0)
    next()
  })

  app.use(pinia)
  app.use(router)
  app.mount('#annotation')

  if (window.microApp) {
    window.microApp.forceSetGlobalData({ skeletonState: false }, () => {
      console.log('skeletonState false success')
    })
  }
}
//如果是开发环境，直接挂载
// if (dev) {
//   onMount()
// }

if (window.__MICRO_APP_ENVIRONMENT__) {
  // 如果在微前端环境，则监听mount事件
  // 👇 将渲染操作放入 mount 函数，子应用初始化时会自动执行
  window.mount = onMount
} else {
  onMount()
}
window.$SDK_ALL.beforeInit({
  businessType: 'lantu',
  envType: process.env.NODE_ENV === 'development' ? 'dev' : 'pro',
})

// 👇 将卸载操作放入 unmount 函数，就是上面步骤2中的卸载函数
window.unmount = () => {
  app.unmount()
  history.destroy()
  app = null
  router = null
  history = null
}
