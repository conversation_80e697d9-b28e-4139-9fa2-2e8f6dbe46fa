import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

import { routes } from './routes'

export const baseRoute = window.__MICRO_APP_ENVIRONMENT__
  ? 'aso-annotation'
  : 'annotation'
export const history = createWebHistory(baseRoute)
const router = createRouter({
  history,
  routes: routes as RouteRecordRaw[],
})
router.beforeEach((to, _from, next) => {
  document.title = to.meta.title as string
  window.scroll(0, 0)
  next()
})

export default router
