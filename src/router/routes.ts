import { type DefineComponent, defineAsyncComponent } from 'vue';

import NotAuth from '@/views/401.vue';
import ViewTask from '@/views/annotation/task/components/viewTask.vue';
import TaskManage from '@/views/annotation/task/index.vue';
import ErrorCorrectionExecuteView from '@/views/evaluateAll/correction/components/executeView.vue';
import ErrorEvaluationDetail from '@/views/evaluateAll/correction/components/viewEvaluation.vue';
import EvaluationError from '@/views/evaluateAll/correction/index.vue';
import MessageCorrectionExecuteView from '@/views/evaluateAll/message/components/executeView.vue';
import MessageEvaluationDetail from '@/views/evaluateAll/message/components/viewEvaluation.vue';
import EvaluationMessage from '@/views/evaluateAll/message/index.vue';
import TextCorrectionExecuteView from '@/views/evaluateAll/text/components/correctionExecuteView.vue';
import TextEvaluationDetail from '@/views/evaluateAll/text/components/viewEvaluation.vue';
import EvaluationText from '@/views/evaluateAll/text/index.vue';
import Index from '@/views/index.vue';
import MarkTaskList from '@/views/mark/markTaskList.vue';
import SampleApprovalList from '@/views/mark/sampleApprovalList.vue';
import Session from '@/views/model/session.vue';
import SessionTask from '@/views/model/sessionTask.vue';
import TaskDetail from '@/views/model/taskDetail.vue';
import TaskPush from '@/views/model/taskPush.vue';
import CreatePrompt from '@/views/prompt/components/CreatePrompt.vue';
import PromptDetail from '@/views/prompt/detail.vue';
import Prompt from '@/views/prompt/list.vue';
import ImportManage from '@/views/sample/importManage/index.vue';
import Enum from '@/views/tag/enum.vue';
import Quality from '@/views/tag/quality.vue';
// import Mark from '@/views/mark/index.vue'
import { Coin, Document, Edit, EditPen, Guide } from '@element-plus/icons-vue';

interface routeType {
  path: string;
  name: string;
  redirect?: string;
  props?: boolean;
  meta?: {
    title: string;
    menus?: boolean;
    code?: string;
  };
  icon?: DefineComponent | any;
  component?: DefineComponent | any;
  children?: routeType[];
}

const routes: routeType[] = [
  {
    path: '/',
    name: 'Index',
    meta: {
      title: '首页',
      menus: true,
    },
    component: Index,
  },
  // 对话类型 标注
  {
    path: '/401',
    name: 'NotAuth',
    meta: {
      title: '无权限',
      menus: false,
    },
    component: NotAuth,
  },
  {
    path: '/mark',
    name: 'Mark',
    meta: {
      title: '标注',
      code: '23.80.437.438',
      menus: false,
    },
    component: defineAsyncComponent(
      () => import('@/views/mark/index.chat.vue')
    ),
  },
  // 固定标注
  {
    path: '/fixed-mark',
    name: 'FixedMark',
    meta: {
      title: '标注',
      code: '23.80.437.438',
      menus: false,
    },
    component: defineAsyncComponent(() => import('@/views/mark/fixedMark.vue')),
  },
  // 划词标注
  {
    path: '/underline-mark',
    name: 'underlineMark',
    meta: {
      title: '标注',
      code: '23.80.437.438',
      menus: false,
    },
    component: defineAsyncComponent(
      () => import('@/views/mark/underlineMark.vue')
    ),
  },
  // 问答类型 标注
  {
    path: '/mark-ask',
    name: 'MarkAsk',
    meta: {
      title: '标注',
      code: '23.80.437.438',
      menus: false,
    },
    component: defineAsyncComponent(() => import('@/views/mark/index.ask.vue')),
  },
  // 语音文本类型 标注
  {
    path: '/mark-audio',
    name: 'MarkAudio',
    meta: {
      title: '标注',
      code: '23.80.437.438',
      menus: false,
    },
    component: defineAsyncComponent(
      () => import('@/views/mark/index.audio.vue')
    ),
  },
  {
    path: '/mark-task-list',
    name: 'mark-task-list',
    meta: {
      title: '质检任务明细',
      code: '23.80.437.438.443',
      menus: false,
    },
    component: MarkTaskList,
  },
  {
    path: '/sample',
    name: 'Sample',
    meta: {
      title: '样本管理',
      menus: true,
      code: '23.80.433.434',
    },
    icon: Document,
    component: ImportManage,
    children: [
      {
        name: 'ImportManage',
        path: '/sample/importManage',
        meta: {
          title: '样本管理',
          menus: true,
          code: '23.80.433.434.435',
        },
        component: ImportManage,
      },
    ],
  },
  {
    path: '/task',
    name: 'task',
    icon: Edit,
    meta: {
      title: '标注管理',
      menus: true,
      code: '23.80.437.438',
    },
    // component: TaskManage,
    children: [
      {
        name: 'TaskManage',
        path: '/task/taskManage',
        meta: {
          title: '标注任务',
          menus: true,
          code: '23.80.437.438',
        },
        component: TaskManage,
        props: true,
      },
      {
        name: 'ViewTask',
        path: '/task/viewTask',
        meta: {
          title: '查看任务',
          code: '23.80.437.438.440',
          menus: false,
        },
        component: ViewTask,
        props: true,
      },
      {
        path: '/sample-approval-list',
        name: 'sample-approval-list',
        meta: {
          title: '样本审批列表',
          menus: true,
          code: '23.80.437.439',
        },
        component: SampleApprovalList,
      },
    ],
  },
  {
    path: '/label',
    name: 'Label',
    meta: {
      title: '标签管理',
      menus: true,
      code: '23.80.445',
    },
    icon: Guide,
    // component: Quality,
    children: [
      {
        path: '/quality-list',
        name: 'quality-list',
        meta: {
          title: '标注项管理',
          menus: true,
          code: '23.80.445.446',
        },
        component: Quality,
      },
      {
        path: '/enum-list',
        name: 'enum-list',
        meta: {
          title: '枚举值管理',
          menus: true,
          code: '23.80.445.447',
        },
        component: Enum,
      },
    ],
  },

  {
    path: '/evaluation',
    name: 'evaluation',
    meta: {
      title: '评测管理',
      menus: true,
      code: '23.81.448.450',
    },
    icon: EditPen,
    children: [
      //信息提取类型 ---start
      {
        path: '/evaluation/message',
        name: 'EvaluationMessageList',
        meta: {
          title: '信息提取类评测',
          menus: true,
          code: '23.81.520.521',
        },
        component: EvaluationMessage,
      },

      {
        path: '/messageEvaluation/detail',
        name: 'MessageEvaluationDetail',
        meta: {
          title: '查看文本评测',
          code: '23.81.520.521.522',
          menus: false,
        },
        component: MessageEvaluationDetail,
        props: true,
      },

      {
        path: '/messageEvaluation/execute',
        name: 'MessageCorrectionExecuteView',
        meta: {
          title: '查看信息提取评测',
          code: '23.81.520.521.522.523',
          menus: false,
        },
        component: MessageCorrectionExecuteView,
        props: true,
      },
      //信息提取类型 ---end
      //纠错类型 ---start
      {
        path: '/evaluation/error',
        name: 'EvaluationErrorList',
        meta: {
          title: '纠错提取类评测',
          menus: true,
          code: '23.81.514.515',
        },
        component: EvaluationError,
      },

      {
        path: '/errorEvaluation/detail',
        name: 'ErrorEvaluationDetail',
        meta: {
          title: '查看文本评测',
          code: '23.81.514.515.516',
          menus: false,
        },
        component: ErrorEvaluationDetail,
        props: true,
      },

      {
        path: '/errorEvaluation/execute',
        name: 'ErrorCorrectionExecuteView',
        meta: {
          title: '查看信息提取评测',
          code: '23.81.514.515.516.517',
          menus: false,
        },
        component: ErrorCorrectionExecuteView,
        props: true,
      },
      //纠错类型 ---end
      //文本类型 ---start
      {
        path: '/evaluation/text',
        name: 'EvaluationTextList',
        meta: {
          title: '文本生成评测',
          menus: true,
          code: '23.81.508.509',
        },
        component: EvaluationText,
      },

      {
        path: '/textEvaluation/detail',
        name: 'TextEvaluationDetail',
        meta: {
          title: '查看文本评测',
          code: '23.81.508.509.510',
          menus: false,
        },
        component: TextEvaluationDetail,
        props: true,
      },

      {
        name: 'TextCorrectionExecuteView',
        path: '/textEvaluation/execute',
        meta: {
          title: '查看文本评测执行信息',
          code: '23.81.508.509.510.511',
          menus: false,
        },
        component: TextCorrectionExecuteView,
        props: true,
      },

      //文本类型 ---end
    ],
  },
  {
    path: '/evaluat',
    name: 'evaluat',
    meta: {
      title: '模型评测',
      menus: true,
      code: '23.81.465',
    },
    icon: Coin,
    // component: Evaluation,
    children: [
      {
        path: '/session-evaluation',
        name: 'session-evaluation',
        meta: {
          title: '会话评测',
          menus: true,
          code: '23.81.465',
        },
        component: Session,
      },
      {
        path: '/session-task',
        name: 'session-task',
        meta: {
          title: '任务详情',
          menus: false,
          code: '23.81.465.470',
        },
        component: SessionTask,
      },
      {
        path: '/task-detail',
        name: 'task-detail',
        meta: {
          title: '明细数据',
          menus: false,
          code: '23.81.465.470',
        },
        component: TaskDetail,
      },
      {
        path: '/task-push',
        name: 'task-push',
        meta: {
          title: '推送问题库',
          menus: false,
          code: '23.81.465.470',
        },
        component: TaskPush,
      },
    ],
  },
  {
    path: '/model',
    name: 'model',
    meta: {
      title: '评测中心',
      menus: true,
      // code: '23.81.465',
    },
    icon: Coin,
    // component: Evaluation,
    children: [
      {
        path: '/prompt-evaluation',
        name: 'prompt-evaluation',
        meta: {
          title: 'prompt评测',
          menus: true,
          // code: '23.81.465',
        },
        component: Prompt,
      },
      {
        path: '/prompt-detail',
        name: 'prompt-detail',
        meta: {
          title: '评测任务',
          menus: false,
          // code: '23.81.465',
        },
        component: PromptDetail,
      },
      {
        path: '/prompt/create',
        name: 'promptCreate',
        meta: {
          title: '创建prompt评测任务',
          menus: false,
          // code: '23.81.465',
        },
        component: CreatePrompt,
      },
    ],
  },
  // {
  //   path: '/:pathMatch(.*)*',
  //   name: 'page404',
  //   redirect: '/',
  //   meta: {
  //     title: '页面丢失',
  //     menus: false,
  //   },
  // },
];

export const MenuList = [
  {
    path: '/sample',
    name: 'Sample',
    meta: {
      title: '样本管理',
    },
    icon: Document,
    children: [
      {
        name: 'ImportManage',
        path: '/sample/importManage',
        meta: {
          title: '导入管理',
        },
      },
    ],
  },
  {
    path: '/task',
    name: 'task',
    icon: Edit,
    meta: {
      title: '标注管理',
    },
    children: [
      {
        name: 'TaskManage',
        path: '/task/taskManage',
        meta: {
          title: '标注任务',
        },
      },
      {
        path: '/sample-approval-list',
        name: 'sample-approval-list',
        meta: {
          title: '样本审批列表',
        },
      },
    ],
  },
  {
    path: '/label',
    name: 'Label',
    meta: {
      title: '标签管理',
    },
    icon: Guide,
    children: [
      {
        path: '/quality-list',
        name: 'quality-list',
        meta: {
          title: '标注项管理',
        },
      },
      {
        path: '/enum-list',
        name: 'enum-list',
        meta: {
          title: '枚举值管理',
        },
      },
    ],
  },
  {
    path: '/evaluation',
    name: 'evaluation',
    meta: {
      title: '评测管理',
    },
    icon: EditPen,
    children: [
      {
        path: '/evaluation/error',
        name: 'EvaluationErrorList',
        meta: {
          title: '纠错准度评测',
        },
      },
      {
        path: '/evaluation/message',
        name: 'EvaluationMessageList',
        meta: {
          title: '信息提取类评测',
        },
      },
      {
        path: '/evaluation/text',
        name: 'EvaluationTextList',
        meta: {
          title: '文本生成评测',
        },
      },
    ],
  },
  {
    path: '/model',
    name: 'model',
    meta: {
      title: '模型评测',
    },
    icon: Coin,
    children: [
      {
        path: '/session-evaluation',
        name: 'session-list',
        meta: {
          title: '会话评测',
        },
      },
    ],
  },
  {
    path: '/prompt',
    name: 'prompt',
    meta: {
      title: '评测中心',
    },
    icon: EditPen,
    children: [
      {
        path: '/prompt-evaluation',
        name: 'prompt-evaluation',
        meta: {
          title: 'prompt评测',
        },
      },
    ],
  },
];

export enum RoutePathCodeEnum {
  '/sample/importManage' = '23.80.433.434.435',
  '/task/taskManage' = '23.80.437.438',
  '/sample-approval-list' = '23.80.437.439',
  '/quality-list' = '23.80.445.446',
  '/enum-list' = '23.80.445.447',
  '/session-evaluation' = '23.81.465',
}
export { routes, type routeType };
