import { getItemTypeMapApi } from '@/api/common'

import { TagTypeEnum } from '@/common/constants'

import { defineStore } from 'pinia'

export const useCommonStore = defineStore('common', {
  state: () => ({
    enumMap: {} as Record<string, number[]>,
    LIMITED_DROPDOWN: [] as number[],
    USER_INPUT_STRING: [] as number[],
    USER_INPUT_NUMBER: [] as number[],
    INTERFACE: [] as number[],
  }),
  getters: {},
  actions: {
    async getItemTypeMap() {
      try {
        const res = await getItemTypeMapApi()
        console.log(res)
        this.enumMap = res
        this.setDropdown()
      } catch (e) {
        console.log(e)
      }
    },
    setDropdown() {
      for (const key in TagTypeEnum) {
        if (isNaN(Number(key))) {
          this[key] = this.enumMap[key]
        }
      }
    },
    clearData() {
      console.log('clearData')
    },
  },
})
