import { getItemDropDownForAnno, getTagListForAnno } from '@/api/tag'

import { SelectListItemType, TagsListResType } from '@/common/interface'
import Utils from '@/common/utils'

import { defineStore } from 'pinia'

const TokenKey = 'passport-token'
const localToken = Utils.getCookie(TokenKey) || ''

interface MapType<T> {
  [k: string | string]: T[]
}

export const useConfigStore = defineStore('mark-config', {
  state: () => ({
    qualityList: [] as TagsListResType[],
    qualityItemMap: {} as MapType<SelectListItemType>,
    itemCodeMap: new Map() as Map<string, string>,
  }),
  getters: {
    getQualityList: (state): TagsListResType[] => state.qualityList || [],
    getQualityMap: (state) => {
      return state.qualityList.reduce((prev, cur) => {
        prev[cur.id] = cur
        return prev
      }, {})
    },
  },
  actions: {
    setItemCodeMap(key: string, value: string) {
      this.itemCodeMap.set(key, value)
    },
    getItemCodeMap(key: string) {
      return this.itemCodeMap.get(key)
    },
    async setQualityList(annoTaskId) {
      try {
        const res = await getTagListForAnno(annoTaskId)
        this.qualityList = res || []
      } catch (e) {
        console.log(e)
      }
    },
    async getQualityItemList(itemId) {
      try {
        if (!this.qualityItemMap?.[itemId]) {
          await this.setQualityByItemId(itemId)
        }
        return this.qualityItemMap[itemId]
      } catch (e) {
        console.log(e)
        return []
      }
    },
    async setQualityByItemId(itemId: string | number) {
      try {
        const res = await getItemDropDownForAnno(itemId)
        this.qualityItemMap[itemId] = res
      } catch (e) {
        console.log(e)
      }
    },
  },
})
