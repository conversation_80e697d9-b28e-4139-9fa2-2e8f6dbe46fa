import { getAnnoTaskList, getDropDownList } from '@/api/common'

import { defineStore } from 'pinia'
export const useEnumsStore = defineStore('counter', {
  state: () => {
    return {
      count: 4,
    }
  },

  getters: {
    double: (state) => state.count * 2,
    square: (state) => {
      return (count) => {
        return count * state.count
      }
    },
  },
  actions: {
    async fetchDropDownList(dropDownType) {
      try {
        const res = await getDropDownList(dropDownType)
        return res
      } catch (e) {
        throw new Error('error')
      }
    },

    async fetchAnnoTaskList(
      appId: string | null = null,
      serviceId: string | null = null,
      taskStatus: string | null = null
    ) {
      try {
        const res = await getAnnoTaskList(appId, serviceId, taskStatus)
        return res
      } catch (e) {
        throw new Error('error')
      }
    },
  },
})
