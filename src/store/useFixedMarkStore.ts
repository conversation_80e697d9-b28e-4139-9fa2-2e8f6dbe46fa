import { ref, toRaw } from 'vue'

import { defineStore } from 'pinia'

// 标注位置类型
enum PositionType {
  Null = -1,
  OneDim = 1,
  TwoDim = 2,
  ThreeDim = 3,
  Free = 10, // 自由划词标注
  Full = 11, // 自由无词全标
}
type UserAnnoDetailsItem = {
  auditCommentInfo: string
  className: string
  content: string
  createTime: string
  creatorDomain: string
  frontProperties: string
  id: number
  position: string
  positionType: PositionType
  tagCode: string
  tagId: number
  tagIdName: string
  tagName: string
  uid: string
  updateTime: string
  updatorDomain: string
  userAnnoId: string
}

// 标注项耽搁节点
type MarkNode = {
  tagId: number // 标签ID
  tagName: string // 标签名称
  position: string // 位置 rowIndex_selectIndex  0_1 第一行的第二个选项
  positionType: PositionType
  content: string
  tagCode: string
  uid: string
  className: string
  frontProperties: string
}

/**
 * {
 *   0: {MarkNode}
 * }
 */
type MarkNodes = {
  [key: string]: MarkNode[]
}

/**
 * {
 *   0: {MarkNodes}
 * }
 */

// const data = {
//   rowIndex: [{ columnIndex: 0, rowIndex: 0, data: data }],
// }

const useFixedMarkStore = defineStore('fixedMarkStore', () => {
  const store = ref<MarkNodes>({})

  function initRows(data: []) {
    data.map((_, index) => {
      store.value[index] = []
    })
  }

  function updateStore(rowIndex: string, data: MarkNode[]) {
    store.value[rowIndex] = data
  }

  function getAllRawData() {
    return toRaw(store.value)
  }

  function validateData() {
    const formattedData = getFormatData()
    const emptyCount = formattedData.filter((item) => !item.tagCode).length
    return !emptyCount
  }

  function getFormatData() {
    const data = toRaw(store.value)
    return Object.values(data).map(Object.values).flat()
  }

  function setToMap(data: UserAnnoDetailsItem[]) {
    clearData()
    // 根据positionType字段,"0_0"切分成rowIndex和index,
    data.forEach((item) => {
      const {
        position,
        tagId,
        tagName,
        tagCode,
        uid,
        content,
        className,
        frontProperties,
        positionType,
      } = item
      const [rowIndex] = position.split('_')
      if (!store.value[rowIndex]) {
        store.value[rowIndex] = [
          {
            position,
            tagId,
            tagName,
            tagCode,
            uid,
            content,
            className,
            frontProperties,
            positionType,
          },
        ]
      } else {
        store.value[rowIndex].push({
          position,
          tagId,
          tagName,
          tagCode,
          uid,
          content,
          className,
          frontProperties,
          positionType,
        })
      }
    })
  }

  function getSelectedDataByRowIndex(rowIndex: number) {
    return toRaw(store.value[rowIndex])
  }

  function clearData() {
    store.value = {}
  }

  return {
    store,
    initRows,
    validateData,
    updateStore,
    getAllRawData,
    getFormatData,
    setToMap,
    clearData,
    getSelectedDataByRowIndex,
  }
})

export default useFixedMarkStore
