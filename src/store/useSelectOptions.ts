import { ref } from 'vue'

import { getItemDropDownForAnno } from '@/api/tag'

import { SelectListItemType } from '@/common/interface'

import { defineStore } from 'pinia'

type CacheOptionsType = {
  [key: string]: { value: string; label: string }[]
}

const useSelectOptions = defineStore('selectOptionsStore', () => {
  const store = ref<CacheOptionsType>({})

  async function getOptionsByItemId(itemId: string | number) {
    if (!store.value[itemId]) {
      const res = await getItemDropDownForAnno(itemId)
      store.value[itemId] = res.map((item) => ({
        value: item.key as string,
        label: item.value as string,
      }))
      return store.value[itemId]
    } else {
      return store.value[itemId]
    }
  }

  function setOptionsByItemId(
    itemId: string | number,
    res: SelectListItemType[]
  ) {
    if (!store.value[itemId]) {
      store.value[itemId] = res.map((item) => ({
        value: item.key as string,
        label: item.value as string,
      }))
    }
  }

  return { store, getOptionsByItemId, setOptionsByItemId }
})

export default useSelectOptions
