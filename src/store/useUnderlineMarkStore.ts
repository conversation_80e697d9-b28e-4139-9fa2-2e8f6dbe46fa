import { ref, toRaw } from 'vue'

import { groupBy } from 'lodash-es'
import { defineStore } from 'pinia'

// 标注位置类型
enum PositionType {
  Null = -1,
  OneDim = 1,
  TwoDim = 2,
  ThreeDim = 3,
  Free = 10, // 自由划词标注
  Full = 11, // 自由无词全标
}
type UserAnnoDetailsItem = {
  auditCommentInfo: string
  className: string
  content: string
  createTime: string
  creatorDomain: string
  frontProperties: string
  id: number
  position: string
  positionType: PositionType
  tagCode: string
  tagId: number
  tagIdName: string
  tagName: string
  uid: string
  updateTime: string
  updatorDomain: string
  userAnnoId: string
}

// 标注项耽搁节点
type MarkNode = {
  tagId: number // 标签ID
  tagName: string // 标签名称
  position: string // 位置 rowIndex_columnIndex_offset_index  0_1_12_index 第一行_第二列_偏移量_索引
  positionType: PositionType
  content: string
  tagCode: string
  uid: string
  className: string
  frontProperties: string
  itemType?: number
}

type HighlightItem = {
  parentIndex: number
  parentTagName: string
  textOffset: number
}
type HighlightNode = {
  startMeta: HighlightItem
  endMeta: HighlightItem
  text: string
  id: string
}
type StoreType = {
  rowIndex: string
  columnIndex: string
  oldIndex?: number
} & HighlightNode &
  MarkNode

const useUnderlineMarkStore = defineStore('underlineMarkStore', () => {
  const store = ref<{ [key: string]: StoreType[] }>({})

  function initRows(rowIndex: string, columnIndex: string, data: StoreType[]) {
    const key = data[0].id
    if (!store.value[key]) {
      store.value[key] = data.map((item) => ({
        ...item,
        rowIndex,
        columnIndex,
      }))
    } else {
      store.value[key].push(
        ...data.map((item) => ({
          ...item,
          rowIndex,
          columnIndex,
        }))
      )
    }
  }

  function getRowData(rowIndex: string) {
    const result: StoreType[] = []
    const keys = Object.keys(store.value)
    for (const key of keys) {
      const temp = store.value[key]
      for (let i = 0; i < temp.length; i++) {
        if (temp[i].rowIndex == rowIndex) {
          temp[i].oldIndex = i
          result.push(temp[i])
        }
      }
    }
    return result
  }

  function validateData() {
    const formattedData = getFormatData()
    const emptyCount = formattedData.filter((item) => !item.tagCode).length
    return !emptyCount
  }

  function getFormatData() {
    const result: MarkNode[] = []
    const data = toRaw(store.value)
    const keys = Object.keys(data)
    for (const key of keys) {
      const temp = data[key]
      console.log('temp: ', temp)
      for (let i = 0; i < temp.length; i++) {
        result.push({
          tagId: temp[i].tagId,
          tagCode: temp[i].tagCode,
          position: `${temp[i].rowIndex}_${temp[i].columnIndex}_${temp[i].startMeta.textOffset}_${i}`,
          tagName: temp[i].tagName,
          positionType: temp[i].positionType,
          content: temp[i].text,
          uid: temp[i].id,
          className: '',
          frontProperties: JSON.stringify(temp[i]),
        })
      }
    }
    return result
  }

  function recoverData(data: UserAnnoDetailsItem[]) {
    clearData()
    const groupedData = groupBy(data, 'uid')
    const keys = Object.keys(groupedData)
    for (const key of keys) {
      const temp = groupedData[key]
      temp.forEach((item) => {
        const { frontProperties } = item
        if (frontProperties) {
          const objFrontProperties = JSON.parse(frontProperties)
          if (!store.value[key]) {
            store.value[key] = [
              {
                ...objFrontProperties,
              },
            ]
          } else {
            store.value[key].push({
              ...objFrontProperties,
            })
          }
        }
      })
    }
  }

  function getSquareData(rowIndex: string, columnIndex: string) {
    const result: HighlightNode[] = []
    const keys = Object.keys(store.value)
    for (const key of keys) {
      const temp = store.value[key]
      const targetData = temp.filter(
        (item) => item.rowIndex == rowIndex && item.columnIndex == columnIndex
      )
      if (targetData.length) {
        result.push(...targetData)
      }
    }
    return result
  }

  function getSelectedDataByRowIndex(rowIndex: number) {
    return toRaw(store.value[rowIndex])
  }

  function deleteOneItemByHighlightId(id: string, index: number) {
    store.value[id].splice(index, 1)
    console.log('delete one item by highlight id: ', id, index)
  }
  function getHighlightLeftItemsLength(id: string) {
    return store.value[id].length
  }

  function clearData() {
    store.value = {}
  }

  return {
    store,
    initRows,
    getRowData,
    validateData,
    getSquareData,
    getFormatData,
    recoverData,
    clearData,
    getSelectedDataByRowIndex,
    deleteOneItemByHighlightId,
    getHighlightLeftItemsLength,
  }
})

export default useUnderlineMarkStore
