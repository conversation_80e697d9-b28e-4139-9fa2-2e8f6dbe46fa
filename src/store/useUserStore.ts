import { getCurUserRoles, getPermissionCode } from '@/api/common'
import { getCurrentEmployee, getUserToken, loginCheck } from '@/api/user'

import { getSsoCheckUrl } from '@/common/helper'
import { SelectListItemType } from '@/common/interface'
import Utils from '@/common/utils'

import axios from 'axios'
import { defineStore } from 'pinia'

import { RoutePathCodeEnum } from '@/router/routes'

const TokenKey = 'passport-token'
const localToken =
  Utils.getCookie(TokenKey) ||
  '****************************************************************************************************************************************************************************************************************************************************************************'

interface UserInfoType {
  userId: string
  realName: string
}

export const useUserStore = defineStore('user', {
  state: () => ({
    user: '',
    token: localToken,
    permissionCode: [] as string[],
    userRoles: [] as SelectListItemType[], // put("管理人员",384) put("审批人员",386) put("标注人员",385)
    userInfo: {} as UserInfoType,
    picSrc: '',
    isMicroApp: !!window.__MICRO_APP_ENVIRONMENT__,
  }),
  getters: {
    getToken: (state) => state.token,
    getPermissionCode: (state) => state.permissionCode,
    getUserRoleList: (state) => state.userRoles.map((u) => u.key),
    getUserName: (state) => state.userInfo.realName,
  },
  actions: {
    setToken(token: string) {
      this.token = token
      Utils.setCookie(TokenKey, token || '')
    },
    haveAuth(path: string) {
      const code = RoutePathCodeEnum[path]
      console.log({ code })
      const isAuth = this.permissionCode.includes(code)
      return !(code && !isAuth)
    },
    async getUserToken() {
      try {
        const ticket = new URLSearchParams(window.location.search).get('ticket')
        if (ticket) {
          const res = await getUserToken({
            ticket,
            platform: getSsoCheckUrl(),
          })
          this.token = res
          Utils.setCookie(TokenKey, res)
          await this.getUserInfo()
        }
      } catch (e) {
        console.log(e)
      }
    },
    async getUserPermissionCode() {
      try {
        const res = await getPermissionCode()
        // console.log(res)
        this.permissionCode = res || []
      } catch (err) {
        console.log(err)
      }
    },
    async getCurUserRoles() {
      try {
        const res = await getCurUserRoles()
        this.userRoles = res || []
      } catch (err) {
        console.log(err)
      }
    },
    async getUserInfo() {
      try {
        const userInfo = await getCurrentEmployee()
        // console.log(userInfo)
        this.userInfo = userInfo
        window.$SDK_ALL.setUserInfo({
          userId: userInfo.domainAccount,
          data: {
            ...userInfo,
          },
        })
      } catch (e) {
        console.log(e)
      }
      // setWaterMark(`${userInfo.realName} ${userInfo.userId}`)
      // window.$SDK_ALL.setUserInfo({
      //   userId: userInfo.domainAccount,
      //   data: {
      //     ...userInfo,
      //   },
      // })
      //如果本地userId不等于请求用户id则获取最新头像
      await this.getAvatar()
      //获取权限
      await this.getUserPermissionCode()
      await this.getCurUserRoles()
      // dispatch('getCurrentUser')
      //获取已接入产品
      // dispatch('Page/getCutInProduct', {}, { root: true })
    },
    async getSsoUrl() {
      try {
        const res = await loginCheck()
        const initStr = 'aso-init=true'
        const { pathname, search } = window.location
        if (search && pathname !== '/') {
          localStorage.setItem('fromUrl', pathname + search + '&' + initStr)
        }
        if (!search && pathname !== '/') {
          localStorage.setItem('fromUrl', pathname + search + '?' + initStr)
        }
        window.location.href = res.redirectUrl
        return res
      } catch (e) {
        console.log(e)
      }
    },
    async getAvatar() {
      if (localStorage.getItem('aso-userId') === this.userInfo.userId) {
        this.picSrc = JSON.parse(localStorage.getItem('aso-head') as string)
      } else {
        try {
          const res = await axios({
            method: 'get',
            url: `/allemployeeapi.ashx?Operation=avatar&usercode=${this.userInfo.userId}`,
            responseType: 'blob',
          })
          Utils.blobToBase64(res.data).then((img) => {
            this.picSrc = img
            localStorage.setItem('aso-head', JSON.stringify(img))
            localStorage.setItem('aso-userId', this.userInfo.userId)
          })
        } catch (e) {
          console.log(e)
        }
      }
    },
    clearUserData() {
      this.userInfo = {} as UserInfoType
      this.token = ''
      Utils.removeCookie(TokenKey)
      this.getSsoUrl()
    },
  },
})
