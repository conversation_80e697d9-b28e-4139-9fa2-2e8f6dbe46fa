@import 'element/index.scss';
.flex-space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.yellow {
    color: #f1db11f8;
}

.blue {
    color: #4455ff;
}

.green {
    color: #00a700;
}

.red {
    color: #ff4a37;
}

.gray {
    color: #999999;
}

.orange {
    color: #d57d0b;
}

.flex-start {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.flex-end {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.overflow-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.overflow-ellipsis-2 {
    //display: box;
    overflow: hidde/n;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    word-break: break-all;
    word-wrap: break-word;
    -webkit-box-orient: vertical;
}

//禁止复制
.select-none {
    -webkit-touch-callout: none;
    user-select: none;
}

.title {
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 500;
    color: #333333;
}

.noscroll {
    &::-webkit-scrollbar {
        display: none;
    }
}