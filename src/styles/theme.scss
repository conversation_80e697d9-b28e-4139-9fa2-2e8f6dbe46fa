$navwidth: 220px;
$back_color_blank: #333;
$back_color: #fff;
$back_color2: #1c724f;

.btn_hover:hover {
  box-shadow: 2px 2px 8px 0 rgba(85, 81, 81, 80%);
}

.text_hover:hover {
  text-shadow: 2px 2px 4px rgb(163, 162, 162);

  .el-checkbox__label:hover {
    text-shadow: 2px 2px 4px rgb(163, 162, 162);
  }
}

.page-content {
  border-radius: 6px;
  padding: 20px;
  background-color: $back_color;
}

.search-content {
  margin-bottom: 30px;
}

.book_pagination {

  ::v-deep .el-pager {
    &>li {
      border-radius: 2px;
    }
  }
}

.book_header {
  position: relative;
  background: #ffffff;
  box-shadow: 2px 0 5px 0 rgba(3, 33, 136, 20%);

  // z-index: 2;
  .top {
    position: relative;
    display: flex;
    padding: 0 15px;
    flex: 1;

    .right {
      position: absolute;
      right: 45px;

      li {
        position: relative;
        float: left;
        height: 50px;

        // &:hover {
        //   background: $back_color2;

        .more {
          display: block;
        }

        // }
      }

      .more {
        .theme_btn {
          margin-top: 10px;
          border: 1px solid rgba(233, 233, 233, 100%);
          border-radius: 0 0 2px 2px;
          height: 32px;
          background: rgba(247, 247, 247, 100%);
          line-height: 32px;

          span {
            display: inline-block;
            width: 50%;
            text-align: center;
            cursor: pointer;
          }

          span:hover {
            color: $back_color;
          }
        }
      }
    }
  }
}

// #409EFF

.zy-el-aside {
  //position: fixed;
  //top: 60px;
  //z-index: 3;
  overflow-x: hidden;
  //overflow-y: scroll;
  padding-bottom: 60px;
  flex: 0 0 200px;
  width: 200px;
  height: calc(100vh - 60px) !important;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 6%);
  transition: max-width 0.4s ease-in-out;

  .el-menu {
    border: 0;

    // height: calc(100vh - 120px);
  }
}

//el-tab的选中色与hover字颜色
::v-deep .el-tabs__item:hover {
  color: $back_color !important;
}

::v-deep .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
  border-color: $back_color !important;
  color: $back_color !important;
}

//重置分页组件样式
.book_pagination {
  text-align: center;

  .el-pagination {
    padding: 15px 12px;

    .el-pagination__editor.el-input .el-input__inner {
      height: 28px !important;
    }

    .el-pagination__jump {
      margin-left: 0;
    }

    .active {
      color: #ffffff;
      background-color: #409eff;
    }
  }
}