<template>
  <el-button type="success" @click="dialogFormVisible = true">
    + 新建任务
  </el-button>

  <div>
    <el-dialog
      class="importDialog"
      @close="resetForm(ruleFormRef)"
      :append-to-body="true"
      v-model="dialogFormVisible"
      title="任务创建"
    >
      <el-form
        :model="form"
        ref="ruleFormRef"
        label-suffix="："
        :rules="rules"
        @close="resetForm(ruleFormRef)"
        label-width="130"
      >
        <p class="mb-10px font-bold">基础信息</p>
        <el-form-item label="任务名称" prop="taskName">
          <el-input
            placeholder="请输入"
            :style="{ width: '100%' }"
            v-model="form.taskName"
          />
        </el-form-item>
        <el-form-item label="所属应用" prop="appId">
          <el-select
            @change="onChange('appId', $event)"
            :style="{ width: '100%' }"
            v-model="form.appId"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in applyList"
              :key="index"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="对应服务" prop="serviceId">
          <el-select
            :style="{ width: '100%' }"
            v-model="form.serviceId"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in importList"
              :key="index"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="期望完成时间" prop="expectFinishTime">
          <el-date-picker
            class="datePicker"
            v-model="form.expectFinishTime"
            :disabled-date="disabledDate"
            type="date"
            placeholder="请选择"
            :style="{ width: '100%' }"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="任务描述" prop="taskDesc">
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入"
            v-model="form.taskDesc"
          />
        </el-form-item>

        <p class="mb-10px font-bold">样本选择</p>
        <el-form-item label="样本分类" prop="sampleType">
          <el-radio-group
            v-model="form.sampleType"
            @change="onChange('sampleType', $event)"
          >
            <el-radio
              v-for="item in SampleTypeOptions"
              :label="item.value"
              :key="item.value"
              >{{ item.label }}({{ item.desc }})</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="样本选择" prop="batchIds">
          <el-select
            :style="{ width: '100%' }"
            v-model="form.batchIds"
            placeholder="请选择"
            multiple
          >
            <el-option
              v-for="(item, index) in serviceList"
              :key="index"
              :label="`${item.value}（${item.appendix}）`"
              :value="item.key"
            />
          </el-select>
        </el-form-item>

        <p class="mt-30px mb-10px font-bold">标注配置</p>
        <el-form-item label="标注方式" prop="annoMethod">
          <el-radio-group v-model="form.annoMethod">
            <el-radio
              v-for="item in MarkTypes"
              :label="item.value"
              :key="item.value"
              :disabled="
                ['0', '1001', '1002'].includes(form.sampleType) &&
                item.value === 1
              "
              >{{ item.label }}({{ item.desc }})</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="样本用途" prop="purposeId">
          <el-select
            v-model="form.purposeId"
            :style="{ width: '100%' }"
            placeholder="请选择"
          >
            <el-option label="训练集" :value="1" />
            <el-option label="评测集" :value="2" />
          </el-select>
        </el-form-item>
        <template v-if="+form.sampleType === 1001">
          <div class="flex items-center pt-30px font-bold pb-10px">
            高级配置
            <div
              @click="toggle"
              class="flex items-center cursor-pointer text-#409eff font-nor mal ml-20px"
            >
              <template v-if="open">
                收起<el-icon><IEpArrowUp /></el-icon>
              </template>
              <template v-else>
                展开<el-icon><IEpArrowDown /></el-icon>
              </template>
            </div>
          </div>

          <el-collapse-transition>
            <div v-show="open" class="flex items-center h-40px overflow-hidden">
              <div class="mr-20px pl-40px">单次选中，是否弹出全部标注项</div>

              <el-switch
                v-model="form.tagShowMethod"
                inline-prompt
                :active-value="1"
                :inactive-value="0"
                active-text="开"
                inactive-text="关"
              />
            </div>
          </el-collapse-transition>
        </template>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitForm(ruleFormRef)">
            提 交
          </el-button>
          <el-button @click="resetForm(ruleFormRef)">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'

import { MarkTypes, SampleTypeOptions } from '@/common/constants'

import type { FormInstance, FormRules } from 'element-plus'

import useCopy from '@/store/useCopy'
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http
const dialogFormVisible = ref(false)
const serviceList = ref<{ value: string; key: string; appendix: string }[]>([])

const ruleFormRef = ref<FormInstance>()
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}
const form = reactive({
  appId: null,
  taskDesc: null,
  batchIds: [],
  taskName: null,
  expectFinishTime: null,
  serviceId: null,
  purposeId: null,
  sampleType: '0',
  tagShowMethod: 0,
  annoMethod: 0,
})
const rules = reactive<FormRules>({
  appId: [{ required: true, message: '请选择所属应用' }],
  taskDesc: [{ required: true, message: '请输入任务描述' }],
  taskName: [{ required: true, message: '请输入任务名称' }],
  batchIds: [{ required: true, message: '请选择样本' }],
  expectFinishTime: [{ required: true, message: '请选择期望完成时间' }],
  serviceId: [{ required: true, message: '请选择对应服务' }],
  purposeId: [{ required: true, message: '请选择样本用途' }],
})

const open = ref(false)

const copyStore = useCopy()

defineProps({
  applyList: {
    type: Object,
  },
  importBatch: {
    type: Object,
  },

  importList: {
    type: Object,
  },
})
const emit = defineEmits(['fetchList'])

async function getServiceList() {
  if (!form.appId) return
  const res = await $http.ajax({
    url: '/nbi/anno/annoTask/getBatchListByAppId',
    method: 'GET',
    params: {
      appId: form.appId,
      sampleType: form.sampleType || '0',
    },
  })

  serviceList.value = res
  return res
}
const onChange = (type: string, value) => {
  // console.log(type, value)
  form.batchIds = []
  serviceList.value = []
  getServiceList()
  if (type === 'sampleType') {
    if (value !== '1003') {
      form.annoMethod = 0
    }
    if (value !== 1001) {
      form.tagShowMethod = 0
    }
  }
}
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  await formEl.validate()

  try {
    await formEl.validate()
    const res = await $http.ajax({
      url: '/nbi/anno/annoTask/newAnnoTask',
      method: 'POST',
      data: {
        ...form,
        batchIds: form.batchIds.join(','),
        expectFinishTime: form.expectFinishTime + ' 23:59:59',
      },
    })
    if (res.code === -1) {
      ElMessage({
        message: res.msg || '网络异常，请重试~',
        type: 'warning',
      })
    } else {
      // ElMessage({
      //   message: '操作成功~',
      //   type: 'success',
      // })
      emit('fetchList')
      form.tagShowMethod = 0
      dialogFormVisible.value = false
    }
  } catch (error) {
    console.log(error)
  }
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  form.tagShowMethod = 0
  form.taskName = null
  form.appId = null
  form.serviceId = null
  form.expectFinishTime = null
  form.taskDesc = null
  dialogFormVisible.value = false
}

function toggle() {
  open.value = !open.value
}

function handleCopy(row) {
  dialogFormVisible.value = true
  form.taskName = row.taskName
  form.appId = row.appId
  form.serviceId = row.serviceId
  form.expectFinishTime = row.expectFinishTime.split(' ')[0]
  form.taskDesc = row.taskDesc
  getServiceList()
}
onMounted(() => {
  copyStore.initClickMethod(handleCopy)
})
</script>

<style lang="less">
.importDialog {
  .el-button--text {
    margin-right: 15px;
  }

  .el-select {
    width: 100%;
  }

  .dialog-footer button:first-child {
    margin-right: 10px;
  }

  .el-input__wrapper {
    width: 100%;
  }
}

.el-date-table__row {
  .ignore,
  .disabled {
    background: #ffffff;
  }
}
</style>
