<template>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column label="任务ID" width="100" fixed="left">
      <template #default="scope">
        <span
          v-if="![4].includes(scope.row.taskStatus)"
          class="id-text"
          @click="handleRouter(scope.row)"
          >{{ scope.row.taskIdDisplay }}</span
        >

        <span v-else>{{ scope.row.taskIdDisplay }}</span>
      </template>
    </el-table-column>
    <el-table-column
      label="任务名称"
      width="220"
      prop="taskName"
    ></el-table-column>
    <el-table-column label="所属应用" width="200">
      <template #default="scope">
        {{
          applyList.length &&
          applyList.find((e) => e.key === scope.row.appId)?.value
        }}
      </template>
    </el-table-column>
    <el-table-column label="对应服务" width="200">
      <template #default="scope">
        {{
          importList.length &&
          importList.find((e) => e.key === scope.row.serviceId)?.value
        }}
      </template>
    </el-table-column>
    <el-table-column label="样本用途">
      <template #default="scope">
        {{
          [
            { name: '训练集', value: 1 },
            { name: '评测集', value: 2 },
          ].find((e) => e.value === scope.row.purposeId)!.name
        }}
      </template>
    </el-table-column>
    <el-table-column label="任务进度" width="200">
      <template #default="scope">
        <div :style="{ color: colorObj[scope.row.taskStatus] }">
          <span>{{ scope.row.taskStatusName }} </span>
          <span v-if="scope.row.taskStatus"
            >（ {{ scope.row.finishedTaskCount }} /
            {{ scope.row.totalTaskCount - scope.row.abandonedCount }}）</span
          >
        </div>
      </template>
    </el-table-column>

    <el-table-column label="未分配样本" width="130">
      <template #default="scope">
        <div class="flex">
          <div class="mr-10px">{{ scope.row.unAssignedCount }}</div>
          <el-popconfirm
            v-if="
              ![0, 3, 4].includes(scope.row.taskStatus) &&
              scope.row.unAssignedCount
            "
            width="240"
            confirm-button-text="确认"
            cancel-button-text="取消"
            icon-color="#626AEF"
            title="确认废弃未分配样本?"
            @confirm="(event) => abandonUNAssignedSample(event, scope.row)"
          >
            <template #reference>
              <div class="flex">
                <el-button link type="primary" :icon="Delete" />
              </div>
            </template>
          </el-popconfirm>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="已废弃样本数" width="110" prop="abandonedCount">
    </el-table-column>

    <el-table-column label="我的待标样本" width="110">
      <template #default="scope">
        {{ scope.row.myWaitingAnnoCount }}
      </template>
    </el-table-column>
    <el-table-column label="期望完成时间" width="260">
      <template #default="scope">
        <template v-if="editing.id === scope.row.id">
          <div>
            <el-date-picker
              v-model="editing.time"
              type="date"
              format="YYYY/MM/DD"
              :prefix-icon="''"
              :clearable="false"
              style="width: fit-content"
              :disabled-date="disabledDate"
            ></el-date-picker>
            <Check
              @click="handleSubmit(scope.row)"
              class="ml-2 w-4 hover:cursor-pointer"
              color="#409EFF"
            ></Check>
          </div>
        </template>
        <template v-else>
          <div>
            {{ scope.row.expectFinishTime }}
            <EditPen
              v-if="store.getUserRoleList.includes(384)"
              @click="handleEdit(scope.row)"
              class="w-3 hover:cursor-pointer"
              color="#409EFF"
            ></EditPen>
          </div>
        </template>
      </template>
    </el-table-column>
    <el-table-column label="标注人" width="110">
      <template #default="scope">
        <el-popover effect="light" trigger="hover" placement="top" width="auto">
          <template #default>
            <div class="blue text-12px">标注人：</div>
            <div
              class="mr-10px mt-5px text-12px pl-5px"
              v-for="item in scope.row.annotatorList"
              :key="item.id"
            >
              {{ item }}
            </div>
          </template>
          <template #reference>
            <div class="overflow-ellipsis">
              {{ scope.row.annotatorCount || 0 }}人
            </div>
          </template>
        </el-popover>
      </template>
    </el-table-column>
    <el-table-column label="导入批次" width="160">
      <template #default="scope">
        <el-popover effect="light" trigger="hover" placement="top" width="auto">
          <template #default>
            <div>{{ scope.row.batchIds }}</div>
          </template>
          <template #reference>
            <div class="overflow-ellipsis">
              {{ scope.row.batchIds }}
            </div>
          </template>
        </el-popover>
      </template>
    </el-table-column>
    <el-table-column label="标注方式">
      <template #default="scope">
        <span>{{ scope.row.annoMethodName }}</span>
      </template>
    </el-table-column>
    <el-table-column label="任务描述" width="200">
      <template #default="scope">
        <el-popover effect="light" trigger="hover" placement="top" width="auto">
          <template #default>
            <div>{{ scope.row.taskDesc }}</div>
          </template>
          <template #reference>
            <div class="overflow-ellipsis">
              {{ scope.row.taskDesc }}
            </div>
          </template>
        </el-popover>
      </template>
    </el-table-column>
    <el-table-column label="创建时间" width="130">
      <template #default="scope">
        {{ scope.row.createTime.split(' ')[0] }}
      </template>
    </el-table-column>
    <el-table-column label="创建人">
      <template #default="scope">
        {{ scope.row.creatorName }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="180" fixed="right">
      <template #default="scope">
        <el-button
          v-if="store.permissionCode.includes('23.80.437.438.440')"
          @click="handleRouter(scope.row)"
          :disabled="[4].includes(scope.row.taskStatus)"
          class="mr-20px"
          type="text"
          >查看</el-button
        >

        <el-button
          v-if="
            store.getUserRoleList.includes(384) &&
            ![3].includes(scope.row.taskStatus)
          "
          @click="
            handleTranslateStatus(
              [4].includes(scope.row.taskStatus) ? 5 : 4,
              scope.row
            )
          "
          type="text"
          >{{ [4].includes(scope.row.taskStatus) ? '开启' : '关闭' }}</el-button
        >
        <el-button type="text" @click="handleCopy(scope.row)">复制</el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="pagination">
    <el-pagination
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, prev, pager, next, jumper, sizes"
      :total="total"
      :background="'#4455FF'"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'

import { Check, Delete, EditPen } from '@element-plus/icons-vue'

import useCopy from '@/store/useCopy'
import { useUserStore } from '@/store/useUserStore'

const colorObj = {
  2: '#93158e',
  0: '#1E90FF',
  1: '#f1db11f8',
  3: '#00a700',
  4: '#ff4a37',
}

const copyStore = useCopy()
const store = useUserStore()
//defineProps
const props = defineProps({
  listData: {
    type: Object,
  },

  applyList: {
    type: Object,
    default: () => {
      return []
    },
  },

  importList: {
    type: Object,
    default: () => {
      return []
    },
  },
})

const isAdmin = ref(false)

const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

watch(
  () => props.listData,
  () => {
    if (props.listData?.elements) {
      tableData.value = props.listData.elements
    }

    currentPage.value = props.listData?.currentPage
    total.value = props.listData?.total
    pageSize.value = props.listData?.pageSize
  }
)

const emit = defineEmits(['getPage'])
const router = useRouter()
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(10)
const viewDialogVisible = ref(false)
const tableData = ref([])
const viewData = ref()
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http
const editing = reactive({
  time: '',
  id: '',
})

const handleEdit = (row) => {
  editing.time = row.expectFinishTime
  editing.id = row.id
}
const handleSubmit = async (row) => {
  try {
    const res = await $http.ajax({
      url: '/nbi/anno/annoTask/updateExpectFinishTimeByUser',
      method: 'POST',
      data: {
        id: editing.id,
        expectFinishTime: dayjs(editing.time)
          .endOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
      },
    })
    if (res === 'OK') {
      editing.id = ''
      emit('getPage', { page: currentPage.value, pageSize: pageSize.value })
    }
  } catch (e) {
    console.error(e)
  }
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  emit('getPage', { page: currentPage.value, pageSize: pageSize.value })
}

function handleSizeChange(size) {
  currentPage.value = 1
  pageSize.value = size
  emit('getPage', { page: currentPage.value, pageSize: pageSize.value })
}

const handleRouter = (row) => {
  if (!store.permissionCode.includes('23.80.437.438.440')) {
    return
  }
  router.push({
    path: '/task/viewTask',
    query: {
      id: row.id,
    },
  })
}

const handleTranslateStatus = (type: number, row: any) => {
  ElMessageBox.confirm(
    '确定要' + (type === 5 ? '开启' : '关闭') + '这条任务吗?',
    type === 5 ? '开启' : '关闭',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    $http
      .ajax({
        url: '/nbi/anno/annoTask/toggleAnnoTask',
        method: 'POST',
        data: {
          annoTaskIds: [row.id],
          toggleToStatusId: type,
        },
      })
      .then(() => {
        // ElMessage({
        //   type: 'success',
        //   message: '操作成功',
        // })
        emit('getPage', { page: currentPage.value, pageSize: pageSize.value })
      })
      .catch((e) => {
        ElMessage({
          type: 'info',
          message: e || '操作失败',
        })
      })
  })
}

const abandonUNAssignedSample = async (event, row) => {
  const res = await $http.ajax({
    url: '/nbi/anno/annoTask/abandonUNAssignedSample',
    method: 'POST',
    data: {
      annoTaskId: row.id,
    },
  })
  if (!res.code) {
    // ElMessage({
    //   message: '操作成功~',
    //   type: 'success',
    // })
    emit('getPage', { page: currentPage.value, pageSize: pageSize.value })
  } else {
    ElMessage({
      message: '网络异常，请重试~',
      type: 'warning',
    })
  }
}

const handleCopy = (row) => {
  copyStore.getClickMethod()(row)
}
</script>

<style lang="less" scoped>
.id-text {
  color: #4455ff;
  cursor: pointer;
}

.pagination {
  display: flex;
  justify-content: end;
  margin-top: 40px;
}

.total {
  margin-left: 30px;
  font-size: 12px;
  font-weight: 500;
  color: #686464;
  margin-bottom: 10px;
}
</style>
