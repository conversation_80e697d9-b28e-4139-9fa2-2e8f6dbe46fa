<template>
  <el-form label-suffix="：" ref="ruleFormRef" :model="form">
    <el-row :gutter="30">
      <el-col :span="8">
        <el-form-item label="信息搜索">
          <el-input
            clearable
            @keyup.enter="onSubmit"
            placeholder="任务ID/任务名称"
            v-model="form.taskIdNameInVague"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="所属应用">
          <el-select v-model="form.appId" placeholder="全部">
            <el-option label="全部" :value="null" />
            <el-option
              v-for="(item, index) in applyList"
              :key="index"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="样本用途">
          <el-select v-model="form.purposeId" placeholder="全部">
            <el-option label="全部" :value="null" />
            <el-option label="训练集" value="1" />
            <el-option label="评测集" value="2" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="30">
      <el-col :span="8">
        <el-form-item label="对应服务">
          <el-select v-model="form.serviceId" placeholder="请选择">
            <el-option label="全部" :value="null" />
            <el-option
              v-for="(item, index) in importList"
              :key="index"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="标注进度">
          <el-select v-model="form.taskStatus" placeholder="请选择">
            <el-option label="全部" :value="null" />
            <el-option label="待分配人员" value="0" />
            <el-option label="进行中" value="1" />
            <el-option label="已延期" value="2" />
            <el-option label="已完成" value="3" />
            <el-option label="已关闭" value="4" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="创 建 人 ">
          <el-select
            class="importBatches"
            v-model="form.creatorUserInVague"
            placeholder="全部"
            collapse-tags
            collapse-tags-tooltip
            filterable
            :max-collapse-tags="3"
          >
            <el-option label="全部" :value="null" />
            <el-option
              v-for="(item, index) in creatorsList"
              :key="index"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="30">
      <el-col :span="8">
        <el-form-item label="我的待标样本">
          <el-select v-model="form.myWaitingAnnoCount" placeholder="请选择">
            <el-option label="全部" :value="null" />
            <!-- <el-option label="=0" value="0" /> -->
            <el-option label=">0" value="1" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="search-btns">
      <el-form-item>
        <el-button @click="reset">重 置</el-button>
        <el-button type="primary" @click="onSubmit">搜 索</el-button>
        <ImportDialog
          v-if="store.getUserRoleList.includes(384)"
          :applyList="applyList"
          :importList="importList"
          :importBatch="importBatch"
          @fetchList="fetchList"
        />
      </el-form-item>
    </div>
  </el-form>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, reactive, ref } from "vue";

import ImportDialog from "./importDialog.vue";

import type { FormInstance } from "element-plus";
import { reset } from "picocolors";

import { useEnumsStore } from "@/store/useEnumsStore";
import { useUserStore } from "@/store/useUserStore";
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const ruleFormRef = ref<FormInstance>();
const applyList = ref<{ value: string; key: string }[]>([]);
const importList = ref<{ value: string; key: string }[]>([]);
const importBatch = ref<{ value: string; key: string }[]>([]);
const creatorsList = ref<{ value: string; key: string }[]>([]);
const datePicker = ref();
const enumsStore = useEnumsStore();
const store = useUserStore();
const form = ref({
  taskIdNameInVague: null,
  appId: null,
  serviceId: null,
  taskStatus: null,
  purposeId: null,
  creatorUserInVague: null,
  myWaitingAnnoCount: null,
});

//defineEmits

const emit = defineEmits(["onSubmit", "getEnums"]);

const searchDataKey = "searchTaskData";
let searchLocalData = {};

try {
  searchLocalData = JSON.parse(localStorage.getItem(searchDataKey) as string);
} catch (err) {
  console.log(err);
  localStorage.setItem(searchDataKey, "");
  searchLocalData = {};
}

const search = reactive({
  searchData: searchLocalData,
});

onMounted(() => {
  Promise.all([
    enumsStore.fetchDropDownList(1),
    enumsStore.fetchDropDownList(4),
    enumsStore.fetchDropDownList(7),
    getAllCreatorsData(),
  ])
    .then((values) => {
      applyList.value = values[0] as [];
      importList.value = values[1] as [];
      importBatch.value = values[2] as [];
      creatorsList.value = values[3] as [];
      emit("getEnums", {
        applyList: applyList.value,
        importList: importList.value,
        importBatch: importBatch.value,
      });

      if (localStorage.getItem(searchDataKey)) {
        form.value = JSON.parse(localStorage.getItem(searchDataKey) as string);
      }
      emit("onSubmit", form.value);
    })
    .catch((err) => {
      console.log(err);
    });
});

const getAllCreatorsData = async () => {
  const res = await $http.ajax({
    url: "/nbi/anno/annoTask/getAllCreators",
    method: "GET",
  });
  if (!res.code) {
    console.log(res, "res");
    return res;
  } else {
    console.log("error");
  }
};

function fetchList() {
  emit("onSubmit", form.value);
}

const onSubmit = () => {
  localStorage.setItem(searchDataKey, JSON.stringify(form.value));
  emit("onSubmit", form.value);
};
const reset = () => {
  localStorage.setItem(searchDataKey, "");
  datePicker.value = [];
  form.value = {
    taskIdNameInVague: null,
    appId: null,
    serviceId: null,
    taskStatus: null,
    purposeId: null,
    creatorUserInVague: null,
    myWaitingAnnoCount: null,
  };
  emit("onSubmit", form.value);
};
</script>
<style lang="less" scoped>
.el-select {
  width: 100%;
}

.search-btns {
  display: flex;
  justify-content: right;

  .el-button {
    width: 85px;
  }
}
</style>
