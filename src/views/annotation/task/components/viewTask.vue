<template>
  <div>
    <el-breadcrumb style="margin: 10px 0 20px" :separator-icon="ArrowRight">
      <el-breadcrumb-item :to="{ path: '/task/taskManage' }"
        >任务列表</el-breadcrumb-item
      >
      <el-breadcrumb-item>{{ taskData?.taskName }}</el-breadcrumb-item>
    </el-breadcrumb>

    <ViewTaskSearch
      :role="store.getUserRoleList"
      :taskData="taskData"
      @onSubmit="onSubmit"
      @getEnums="getEnums"
      @onTaskAllocationChange="onTaskAllocationChange"
    />
    <div class="page-content">
      <ViewTaskList
        ref="viewTaskList"
        @getPage="getPage"
        @onTaskAllocationChange="onTaskAllocationChange"
        @fetchTaskData="getTaskDetail"
        :taskData="taskData"
        :role="store.getUserRoleList"
        :id="route.query && route.query.id"
        :annotationList="annotationList"
        :listData="listData"
        :hasList="hasList"
        :condition="condition"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

import ViewTaskList from './viewTaskList.vue'
import ViewTaskSearch from './viewTaskSearch.vue'

import { ArrowRight } from '@element-plus/icons-vue'

import { useUserStore } from '@/store/useUserStore'
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http
const listData = ref<any>()
const taskData = ref<any>()
const route: any = useRoute()
const hasList = ref(true)
let viewTaskList = ref()
const searchData = ref({
  sampleBusiId: null,
  annoStatus: null,
  annoTaskId: null,
  page: 1,
  size: 10,
})
const condition = ref('')

const store = useUserStore()

onMounted(async () => {
  await getTaskDetail()
  searchData.value = {
    ...searchData.value,
    annoTaskId: route.query && route.query.id,
  }
  await fetchData()
  hasList.value = Boolean(listData.value.total) && taskData.value.taskStatus
})

const annotationList = ref<{ value: string; key: string }[]>([])
function getPage(pageData) {
  const { page, pageSize } = pageData
  searchData.value.page = page
  searchData.value.size = pageSize
  fetchData()
}

function getEnums(pageData) {
  annotationList.value = pageData.annotationList
}
async function onSubmit(data) {
  searchData.value = { ...searchData.value, ...data, page: 1, size: 10 }
  viewTaskList.value.pageSize = 10
  viewTaskList.value.currentPage = 1
  await getTaskDetail()
  await fetchData()
  // hasList.value = Boolean(listData.value.total) && taskData.value.taskStatus
}

async function onTaskAllocationChange(data) {
  await onSubmit(data)
  hasList.value = Boolean(listData.value.total) && taskData.value.taskStatus
}

async function fetchData() {
  condition.value = JSON.stringify({
    sampleBusiId: searchData.value.sampleBusiId,
    annoStatus: searchData.value.annoStatus,
  })
  const res = await $http.ajax({
    url: '/nbi/anno/annoTaskDetail/getUserAnnoPage',
    method: 'GET',
    params: searchData.value,
  })
  if (!res.code) {
    // ElMessage({
    //   message: '操作成功~',
    //   type: 'success',
    // })
  } else {
    ElMessage({
      message: '网络异常，请重试~',
      type: 'warning',
    })
  }
  listData.value = res
  return res
}

async function getTaskDetail() {
  const annoTaskId = route.query && route.query.id

  if (!annoTaskId) return

  const res = await $http.ajax({
    url: '/nbi/anno/annoTaskDetail/getUserAnnoBanner',
    method: 'GET',
    params: { annoTaskId },
  })
  if (res.code) {
    ElMessage({
      message: '网络异常，请重试~',
      type: 'warning',
    })
    return
  }
  console.log(res)
  taskData.value = res
  return res
}
</script>
