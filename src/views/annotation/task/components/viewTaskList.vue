<template>
  <div
    v-if="!hasList && role.includes(384)"
    class="flex items-center flex-col mt-32 h-140"
  >
    <div class="color-[#333] font-bold mb-15px text-24px">
      该项目暂未分配标注人员
    </div>
    <div class="text-14px font-bold mb-15px color-[#333]">
      请点击下方按钮分配人员
    </div>
    <el-button type="primary" @click="toTaskAllocation"> + 人员分配</el-button>
  </div>
  <el-table v-else :data="tableData" style="width: 100%">
    <el-table-column label="样本ID">
      <template #default="scope">
        <span v-if="store.permissionCode.includes('23.80.437.438.443')"
          ><a
            href="javascript:;"
            class="id-text"
            style="text-decoration: none"
            @click="handleRouter('view', scope.row)"
          >
            {{ scope.row.sampleBusiId }}
          </a></span
        >

        <span v-else class="id-text">{{ scope.row.sampleBusiId }}</span>
      </template>
    </el-table-column>
    <el-table-column label="状态">
      <template #default="scope">
        <span :style="{ color: colorObj[scope.row.annoStatus] }">{{
          scope.row.annoStatusName
        }}</span>
      </template>
    </el-table-column>
    <el-table-column label="标注人">
      <template #default="scope">
        {{ scope.row.userName }}
      </template>
    </el-table-column>
    <el-table-column label="提交审批时间">
      <template #default="scope">
        {{ scope.row.submitAuditTime?.split('.')[0] }}
      </template>
    </el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <el-button
          v-if="store.permissionCode.includes('23.80.437.438.443')"
          @click="handleRouter('view', scope.row)"
          class="mr-20px"
          type="text"
          >查看</el-button
        >

        <el-button
          v-if="store.permissionCode.includes('23.80.437.438.442')"
          @click="handleRouter('update', scope.row)"
          :disabled="
            !Boolean(scope.row.annotatable) ||
            [3, -2].includes(scope.row.annoStatus)
          "
          class="mr-20px"
          type="text"
          >标注</el-button
        >

        <el-button
          :disabled="[3, -2].includes(scope.row.annoStatus)"
          @click="handleTranslateStatus(scope.$index, scope.row)"
          type="text"
          >废弃</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <div class="pagination" v-if="hasList && tableData.length">
    <el-pagination
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, prev, pager, next, jumper, sizes"
      :total="total"
      :background="'#4455FF'"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <TaskAllocation
    :taskData="taskData"
    @change="onTaskAllocationChange"
    ref="taskAllocationRef"
  />
</template>

<script lang="ts" setup>
import { getCurrentInstance, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

import TaskAllocation from '@/views/mark/components/task-allocation.vue'

import useFixedMarkStore from '@/store/useFixedMarkStore'
import { useUserStore } from '@/store/useUserStore'
const colorObj = {
  '-2': '#ff4a37',
  2: '#912CEE',
  0: '#1E90FF',
  1: '#f1db11f8',
  3: '#00a700',
  4: '#93158e',
}
const router = useRouter()
const store = useUserStore()
//defineProps
const props = defineProps({
  listData: {
    type: Object,
  },

  role: {
    type: Array,
    default: () => {
      return []
    },
  },

  applyList: {
    type: Object,
    default: () => {
      return []
    },
  },

  taskData: {
    type: Object,
  },

  id: {
    type: String,
  },

  hasList: {
    type: Boolean,
  },

  condition: {
    type: String,
  },
})

watch(
  () => props.listData,
  () => {
    if (props.listData?.elements) {
      tableData.value = props.listData.elements
    }

    currentPage.value = props.listData?.currentPage
    total.value = props.listData?.total
    pageSize.value = props.listData?.pageSize
  }
)
let taskAllocationRef = ref()

const userAnnoDetailsStore = useFixedMarkStore()

const emit = defineEmits(['getPage', 'fetchTaskData', 'onTaskAllocationChange'])
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(10)
const viewDialogVisible = ref(false)
const tableData = ref([])
const viewData = ref()
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  emit('getPage', { page: currentPage.value, pageSize: pageSize.value })
}

const handleRouter = (type, row) => {
  console.log(props.condition, row)
  const { sampleType } = row
  // 1003: 自由问答类
  let path = '/mark'
  if (sampleType === 1003) {
    if (props.taskData?.annoMethod === 1) {
      userAnnoDetailsStore.clearData()
      path = '/fixed-mark'
    } else {
      path = '/underline-mark'
    }
  }
  router.push({
    path,
    query: {
      id: row.id,
      taskId: props.id,
      status: type,
      viewMode: type === 'view' ? 'anno_view' : 'anno',
      page: 'viewTask',
      taskName: props.taskData!.taskName,
      condition: encodeURIComponent(props.condition as string),
      annoStatus: row.annoStatus,
    },
  })
}

function handleSizeChange(size) {
  currentPage.value = 1
  pageSize.value = size
  emit('getPage', { page: currentPage.value, pageSize: pageSize.value })
}

function toTaskAllocation() {
  // console.log(taskAllocationRef.value)
  taskAllocationRef.value.dialogData.show = true
  taskAllocationRef.value.getAnnoTaskAssignData()
}

function onTaskAllocationChange(status) {
  console.log(status)
  emit('onTaskAllocationChange', {
    page: currentPage.value,
    pageSize: pageSize.value,
  })
}
const handleTranslateStatus = (index: number, row: any) => {
  ElMessageBox.confirm('确定要废弃这条样本吗?', '废弃', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    $http
      .ajax({
        url: '/nbi/anno/sampleAnno/abandonAnAnno',
        method: 'POST',
        data: {
          id: row.id,
        },
      })
      .then(() => {
        // ElMessage({
        //   type: 'success',
        //   message: '操作成功',
        // })
        emit('fetchTaskData')
        emit('getPage', { page: currentPage.value, pageSize: pageSize.value })
      })
      .catch((e) => {
        ElMessage({
          type: 'info',
          message: e || '操作失败',
        })
      })
  })
}

const handleCheck = (index: number, row: object) => {
  viewData.value = row
  viewDialogVisible.value = true
}
</script>

<style lang="less" scoped>
.id-text {
  color: #4455ff;
}

.pagination {
  display: flex;
  justify-content: end;
  margin-top: 40px;
}

.total {
  margin-left: 30px;
  font-size: 12px;
  font-weight: 500;
  color: #686464;
  margin-bottom: 10px;
}
</style>
