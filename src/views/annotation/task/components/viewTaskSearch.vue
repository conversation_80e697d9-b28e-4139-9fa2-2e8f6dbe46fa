<template>
  <div class="flex-space-between mb-20px">
    <div class="flex mt-20px items-center" v-if="taskData">
      <div class="color-#666">
        任务ID：
        <span class="color-dark font-600">{{ taskData!.taskIdDisplay }}</span>
      </div>
      <div class="ml-20px color-#666">
        所属应用：
        <span class="color-dark font-600"> {{ taskData!.applicationName }}</span>
      </div>
      <div class="ml-20px color-#666">
        对应服务：
        <span class="color-dark font-600">{{ taskData!.serviceName }}</span>
      </div>
      <div class="ml-20px color-#666">
        任务进度：
        <span class="color-dark font-600">{{ taskData.taskStatusName }}</span>
        <span v-if="taskData.taskStatus" class="color-dark font-600"
          >（ {{ taskData.finishedTaskCount }} /
          {{ taskData.totalTaskCount - taskData.abandonedCount }} ）</span
        >
      </div>
      <div class="ml-20px color-#666">
        我的待完成量：
        <span class="color-dark font-600">{{
          taskData!.myWaitingAnnoCount
        }}</span>
      </div>
      <div class="ml-20px color-#666 mr-20px">
        期望完成时间：
        <span class="color-dark font-600">{{
          taskData!.expectFinishTime
        }}</span>
      </div>
    </div>
    <el-button
      v-if="role.includes(384) && store.permissionCode.includes('23.80.437.438.441')"
      type="success"
      @click="toTaskAllocation"
    >
      + 人员分配</el-button
    >
  </div>

  <div class="page-content search-content">
    <el-form label-suffix="：" ref="ruleFormRef" :model="form">
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="信息搜索">
            <el-input
              placeholder="样本ID"
              @keyup.enter="onSubmit"
              v-model="form.sampleBusiId"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="标注状态">
            <el-select v-model="form.annoStatus" placeholder="全部">
              <el-option label="全部" :value="null" />
              <el-option
                v-for="(item, index) in annotationList"
                :key="index"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="标注人">
            <el-select
              v-model="form.annoUserDomain"
              placeholder="请选择"
              @focus="getUsers"
            >
              <el-option label="全部" :value="null" />
              <el-option
                v-for="(item, index) in users"
                :key="index"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <div class="search-btns flex flex-justify-end">
        <el-button
          type="success"
          v-if="role.includes(384) && taskData && +taskData.taskStatus === 3"
          @click="exportToLocalDb"
          >推送数仓</el-button
        >
        <div class="flex-grow flex flex-justify-end">
          <el-button @click="reset">重 置</el-button>
          <el-button type="primary" @click="onSubmit">搜 索</el-button>
        </div>
      </div>
    </el-form>

    <TaskAllocation
      :taskData="taskData"
      @change="onTaskAllocationChange"
      ref="taskAllocationRef"
    />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'

import TaskAllocation from '@/views/mark/components/task-allocation.vue'

import type { FormInstance } from 'element-plus'

import { useUserStore } from '@/store/useUserStore'

const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const ruleFormRef = ref<FormInstance>();
const annotationList = ref<{ value: string; key: string }[]>([]);
const users = ref([])
const datePicker = ref();
let taskAllocationRef = ref();
const exportBoo = ref<boolean>(false);
const store = useUserStore();
const form = ref({
  sampleBusiId: null,
  annoStatus: null,
  applicationId: null,
  annoUserDomain: null,
})

function onTaskAllocationChange(status) {
  emit('onTaskAllocationChange', form.value)
}

async function getApplyList(dropDownType: number) {
  const res = await $http.ajax({
    url: '/nbi/anno/common/getDropDownList',
    method: 'GET',
    params: { dropDownType },
  })
  return res
}

async function getUsers() {
  const res = await $http.ajax({
    url: '/nbi/anno/annoTaskDetail/getUserNames',
    method: 'GET',
    params: {
      annoTaskId: props.taskData?.id,
    },
  })
  users.value = res
  console.log('getUsers: ', res)
}

const props = defineProps({
  taskData: {
    type: Object,
  },
  role: {
    type: Array,
    default: () => {
      return []
    },
  },
})

//defineEmits
const emit = defineEmits(['onSubmit', 'getEnums', 'onTaskAllocationChange'])
function toTaskAllocation() {
  // console.log(taskAllocationRef.value)
  taskAllocationRef.value.getAnnoTaskAssignData()
  taskAllocationRef.value.dialogData.show = true
}
onMounted(() => {
  Promise.all([getApplyList(6)])
    .then((values) => {
      annotationList.value = values[0]
      emit('getEnums', {
        annotationList: annotationList.value,
      })
      // emit('onSubmit', form.value)
    })
    .catch((err) => {
      console.log(err)
    })
})

const onSubmit = () => {
  emit('onSubmit', form.value)
}
const reset = () => {
  datePicker.value = []
  form.value = {
    sampleBusiId: null,
    annoStatus: null,
    applicationId: null,
    annoUserDomain: null,
  }
  emit('onSubmit', form.value)
}

const exportToLocalDb = async () => {
  if (exportBoo.value) {
    return
  }

  ElMessage({
    message: '正在推送中，请稍等~',
    type: 'warning',
  })
  exportBoo.value = true
  const res = await $http.ajax({
    url: '/nbi/anno/export/exportToLocalDb',
    method: 'POST',
    data: { annoTaskId: props.taskData?.id },
  })
  if (!res.code) {
    // ElMessage({
    //   message: '操作成功~',
    //   type: 'success',
    // })
  } else {
    ElMessage({
      message: '网络异常，请重试~',
      type: 'warning',
    })
  }
  exportBoo.value = false
  return res
}
</script>
<style lang="less" scoped>
.el-select {
  width: 100%;
}

.search-btns {
  .el-button {
    width: 85px;
  }
}
</style>
