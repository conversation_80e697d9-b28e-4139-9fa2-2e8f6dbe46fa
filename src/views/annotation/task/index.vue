<template>
  <div>
    <div class="page-content task-content search-content">
      <!-- <PageTabs :data="MarkTaskTabs" value="21" /> -->

      <NotAuth v-if="useAuth()" />

      <Search v-if="!useAuth()" @onSubmit="onSubmit" @getEnums="getEnums" />
    </div>
    <div v-if="!useAuth()" class="page-content">
      <SampleList
        ref="sampleList"
        @getPage="getPage"
        :applyList="applyList"
        :importList="importList"
        :listData="listData"
        @copy="handleCopy"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { getCurrentInstance, onMounted, ref } from 'vue'

import PageTabs from '@/components/PageTabs.vue'
import NotAuth from '@/views/401.vue'
import SampleList from './components/list.vue'
import Search from './components/search.vue'

import { MarkTaskTabs } from '@/common/constants'

import { useAuth } from '@/hooks/useAuth'
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http
const listData = ref<any>()
const sampleList = ref()
const searchData = ref({
  applicationId: null,
  taskStatus: null,
  taskDateTimeBegin: null,
  taskDateTimeEnd: null,
  importBatch: null,
  page: 1,
  size: 10,
})

const applyList = ref<{ value: string; key: string }[]>([])
const importList = ref<{ value: string; key: string }[]>([])
const importBatch = ref<{ value: string; key: string }[]>([])

function handleCopy(row) {
  console.log('handle copy: ', row)
}

function getPage(pageData) {
  const { page, pageSize } = pageData
  searchData.value.page = page
  searchData.value.size = pageSize
  fetchData()
}

function getEnums(pageData) {
  applyList.value = pageData.applyList
  importList.value = pageData.importList
  importBatch.value = pageData.importBatch
}
function onSubmit(data) {
  searchData.value = { ...searchData.value, ...data, page: 1, size: 10 }
  sampleList.value.pageSize = 10
  sampleList.value.currentPage = 1
  fetchData()
}

async function fetchData() {
  const res = await $http.ajax({
    url: '/nbi/anno/annoTask/getAnnoTaskPage',
    method: 'GET',
    params: searchData.value,
  })
  if (!res.code) {
    // ElMessage({
    //   message: '操作成功~',
    //   type: 'success',
    // })
  } else {
    ElMessage({
      message: '网络异常，请重试~',
      type: 'warning',
    })
  }
  listData.value = res
  return res
}

onMounted(() => {
  window.$SDK_ALL.sendPage({
    event_id: 'lantu_view',
    data: {
      page: 'taskManage',
    },
  })
})
</script>
