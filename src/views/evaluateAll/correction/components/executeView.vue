<template>
  <div>
    <el-breadcrumb style="margin: 10px 0 20px" :separator-icon="ArrowRight">
      <el-breadcrumb-item :to="{ path: '/evaluation/error' }">评测列表</el-breadcrumb-item>
      <el-breadcrumb-item
        :to="{
          path: '/errorEvaluation/detail',
          query: { id: route.query && route.query.taskId },
        }"
        >{{ (route.query && route.query.taskName) || '_' }}</el-breadcrumb-item
      >
      <el-breadcrumb-item>{{ taskData && taskData?.id }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div v-if="taskData" class="page-content" style="position: relative">
      <div class="mb-60px" v-if="taskData">
        <div class="text-14px mt-16px">执行信息：</div>
        <div class="flex items-center flex-wrap gap-20px justify-start mt-10px mb-30px">
          <div class="color-#666">
            执行ID：
            <span class="color-dark font-600">{{ taskData?.id }}</span>
          </div>
          <!-- <div class="color-#666">
            服务接口：
            <span class="color-dark font-600">{{
              taskData?.taskIdDisplay
            }}</span>
          </div> -->

          <div class="color-#666">
            执行状态：
            <span class="color-dark font-600">{{
              taskStatusOptions.find((e) => e.value === taskData.batchStatus)?.label
            }}</span>
          </div>
          <div class="color-#666 mr-20px">
            执行时间：
            <span class="color-dark font-600">{{ moment(taskData!.submitTime).format('YYYY-MM-DD HH:mm') }}</span>
          </div>

          <div class="color-#666 mr-20px">
            耗时：
            <span v-if="!taskData.timeCostMs" class="color-dark font-600">{{ taskData.timeCostMs }}秒</span>
            <span class="color-dark font-600" v-else-if="taskData.timeCostMs < 0">0秒</span>
            <span v-else class="color-dark font-600">
              {{
                taskData.timeCostMs / 1000 < 60
                  ? `${taskData.timeCostMs / 1000}秒`
                  : `${Math.floor(taskData.timeCostMs / 60000)}分${Math.round(
                      +((taskData.timeCostMs % 60000) / 1000).toFixed(1)
                    )}秒`
              }}
            </span>
          </div>

          <div class="color-#666 mr-20px">
            是否输入额外参数：

            <span class="color-dark font-600">
              {{ taskData.parentBusiColumnsJson && taskData.parentBusiColumnsJson.userAddtionalParam ? '是' : '否' }}
            </span>
          </div>

          <div class="color-#666">
            评测方法：
            <span class="color-dark font-600"> 纠错评测方法 </span>
          </div>
        </div>

        <div class="text-14px">推送蓝图：</div>
        <div class="mt-10px mb-30px">
          <div class="color-#666">
            <div v-if="taskData.busiColumnsJson && taskData.busiColumnsJson.markedValidBatch">
              <span>已推送，推送版本号：</span>
              <span class="color-dark font-600">{{
                taskData.busiColumnsJson && taskData.busiColumnsJson.markedValidBatch
              }}</span>
            </div>
            <span v-else>未推送</span>
          </div>
        </div>
      </div>

      <div class="button-box">
        <el-button
          v-if="taskData.busiColumnsJson && taskData.busiColumnsJson.markedValidBatch"
          type="warning"
          @click="toExecute"
        >
          取消推送</el-button
        >
        <el-button v-else type="warning" @click="toExecute"> 推送蓝图</el-button>
      </div>
    </div>
  </div>

  <div class="page-content mt-20px" style="position: relative">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="评估报告" name="1"></el-tab-pane>
      <el-tab-pane label="样本明细" name="2"></el-tab-pane>
    </el-tabs>
    <div>
      <div v-if="activeName == 1">
        <template v-if="store.permissionCode.includes('23.81.514.515.516.517.518')">
          <div class="text-12px color-#666 mt-10px mb-5px">整体指标：</div>
          <el-table :data="tableWholeData" stripe style="width: 100%">
            <el-table-column prop="tagId" label="实体类型">
              <template #default="scope">
                <span class="color-#666">{{ scope.row.entityType }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="orgRecallRate" label="原文召回率">
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="原文标注一致量/标注命中量*100%" placement="bottom">
                  <div class="box-center">
                    <span class="color-#666">原文召回率</span>
                    <el-icon :size="15"><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
              </template>
              <template #default="scope">
                {{ Math.round(scope.row.orgRecallRate * 100) + '%' }}
              </template>
            </el-table-column>
            <el-table-column prop="llmRecallRate" label="模型召回率">
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="模型标注一致量/标注命中量*100%" placement="bottom">
                  <div class="box-center">
                    <span class="color-#666">模型召回率</span>
                    <el-icon :size="15"><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
              </template>
              <template #default="scope">
                {{ Math.round(scope.row.llmRecallRate * 100) + '%' }}
              </template>
            </el-table-column>
            <el-table-column prop="orgPrecisionRate" label="原文精准率">
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="原文标注一致量/原文命中量*100%" placement="bottom">
                  <div class="box-center">
                    <span class="color-#666">原文精准率</span>
                    <el-icon :size="15"><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
              </template>
              <template #default="scope">
                {{ Math.round(scope.row.orgPrecisionRate * 100) + '%' }}
              </template>
            </el-table-column>
            <el-table-column prop="llmPrecisionRate" label="模型精准率">
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="模型标注一致量/模型命中量*100%" placement="bottom">
                  <div class="box-center">
                    <span class="color-#666">模型精准率</span>
                    <el-icon :size="15"><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
              </template>
              <template #default="scope">
                {{ Math.round(scope.row.llmPrecisionRate * 100) + '%' }}
              </template>
            </el-table-column>

            <el-table-column prop="orgF1" label="原文F1">
              <template #header>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="2*精准率*召回率/（精准率+召回率）"
                  placement="bottom"
                >
                  <div class="box-center">
                    <span class="color-#666">原文F1</span>
                    <el-icon :size="15"><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="llmF1" label="模型F1">
              <template #header>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="2*精准率*召回率/（精准率＋召回率）"
                  placement="bottom"
                >
                  <div class="box-center">
                    <span class="color-#666">模型F1</span>
                    <el-icon :size="15"><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
              </template>
            </el-table-column>

            <el-table-column prop="orgRecallNum" label="原文命中量">
              <template #header>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="原文命中标准词总量，包括品牌实体词，车型实体词。同一句子命中多个标准词，会被计算多次。"
                  placement="bottom"
                >
                  <div class="box-center">
                    <span class="color-#666">原文命中量</span>
                    <el-icon :size="15"><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
              </template>
              <template #default="scope">
                {{ scope.row.orgRecallNum }}
              </template>
            </el-table-column>

            <el-table-column prop="dimEntityNum" label="标注命中量">
              <template #header>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="标注命中标准词总量，包括品牌实体词，车型实体词。同一句子命中多个标准词，会被计算多次。"
                  placement="bottom"
                >
                  <div class="box-center">
                    <span class="color-#666">标注命中量</span>
                    <el-icon :size="15"><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
              </template>
            </el-table-column>

            <el-table-column prop="llmRecallNum" label="模型命中量">
              <template #header>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="模型命中标准词总量，包括品牌实体词，车型实体词。同一句子命中多个实体词，会被计算多次。"
                  placement="bottom"
                >
                  <div class="box-center">
                    <span class="color-#666">模型命中量</span>
                    <el-icon :size="15"><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
              </template>
              <template #default="scope">
                {{ scope.row.llmRecallNum }}
              </template>
            </el-table-column>

            <el-table-column prop="dimVsOrgCount" label="标注原文一致量">
              <template #header>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="相同位置 原文命中标准词 与标注命中标准词 一致的量。"
                  placement="bottom"
                >
                  <div class="box-center">
                    <span class="color-#666">标注原文一致量</span>
                    <el-icon :size="15"><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
              </template>
            </el-table-column>

            <el-table-column prop="dimVsLlmCount" label="标注模型一致量">
              <template #header>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="相同位置 模型命中标准词总量，与标注命中标准词一致的量。"
                  placement="bottom"
                >
                  <div class="box-center">
                    <span class="color-#666">标注模型一致量</span>
                    <el-icon :size="15"><QuestionFilled /></el-icon>
                  </div>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <IndexTableFirst />
        </template>
        <template v-else>
          <NoPermission />
        </template>
      </div>

      <div v-if="activeName == 2">
        <template v-if="store.permissionCode.includes('23.81.514.515.516.517.519')">
          <SampleDetailTable :taskData="taskData" />
        </template>
        <template v-else>
          <NoPermission />
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import moment from 'moment';
import { ElMessage, ElMessageBox } from 'element-plus';

import NoPermission from '@/views/401.vue';

import IndexTableFirst from './indexTableFirst';
import SampleDetailTable from './sampleDetailTable';
import { ArrowRight } from '@element-plus/icons-vue';
import type { TabsPaneContext } from 'element-plus';

import { useEnumsStore } from '@/store/useEnumsStore';
import { useUserStore } from '@/store/useUserStore';
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const store = useUserStore();
const enumsStore = useEnumsStore();
const route: any = useRoute();
const taskData = ref<any>({});
const annoTaskList = ref<{ value: string; key: string }[]>([]);
const tableWholeData = ref([]);

const activeName = ref('1');
const taskStatusOptions = [
  { label: '未开始', value: 10 },
  { label: '进行中', value: 20 },
  { label: '准备中', value: 30 },
  { label: '成功', value: 40 },
  { label: '失败', value: 50 },
  { label: '正在停止', value: 60 },
  { label: '已停止', value: 70 },
];
const toExecute = () => {
  const str =
    taskData.value.busiColumnsJson && taskData.value.busiColumnsJson.markedValidBatch ? '取消推送蓝图' : '推送蓝图';
  ElMessageBox.confirm(`确定要${str}展示吗?`, str, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    $http
      .ajax({
        url: '/nbi/evaluate/taskBatch/markDataStatus',
        method: 'POST',
        data: {
          batchId: taskData.value.id,
        },
      })
      .then((res) => {
        if (res.msg) {
          ElMessage({
            type: 'error',
            message: res.msg || '操作失败',
          });
          return;
        }
        ElMessage({
          type: 'success',
          message:
            taskData.value.busiColumnsJson && taskData.value.busiColumnsJson.markedValidBatch
              ? '取消推送成功'
              : '推送成功',
        });
        fetchDetail();
      })
      .catch((e) => {
        ElMessage({
          type: 'info',
          message: e || '操作失败',
        });
      });
  });
};

onMounted(async () => {
  await fetchDetail();
  await fetchData();
  const res = await enumsStore.fetchAnnoTaskList();
  annoTaskList.value = res as any;
});

async function fetchDetail() {
  const batchId = route.query && route.query.id;
  const res = await $http.ajax({
    url: '/nbi/evaluate/taskBatch/getOneDetailByBatchId',
    params: {
      batchId,
    },
  });

  taskData.value = res;
}
async function fetchData() {
  const batchId = route.query && route.query.id;
  Promise.all([
    await $http.ajax({
      url: '/nbi/evaluate/evalResult/correctError/whole',
      params: {
        batchId,
      },
    }),
  ]).then((values) => {
    tableWholeData.value = values[0];
  });
}

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event);
};
</script>
<style>
.button-box {
  position: absolute;
  right: 20px;
  top: 20px;
}
</style>
