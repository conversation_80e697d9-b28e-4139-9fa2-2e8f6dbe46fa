<template>
  <div>
    <div v-if="dialogFormVisible">
      <el-dialog
        show-close
        class="importDialog"
        :model-value="true"
        @close="resetForm(ruleFormRef)"
        :close-on-click-modal="false"
        :append-to-body="true"
        title="创建评测任务-纠错准度评测"
      >
        <div class="text-[15px] font-medium mb-[10px]">基础信息</div>
        <el-form
          :model="form"
          ref="ruleFormRef"
          label-suffix="："
          :rules="rules"
          @close="resetForm(ruleFormRef)"
          label-width="150"
        >
          <el-form-item label="任务名称" prop="taskName">
            <el-input placeholder="请输入" :style="{ width: '100%' }" v-model="form.taskName" />
          </el-form-item>

          <div class="text-[15px] font-medium mb-[10px] mt-[30px]">评测配置</div>
          <el-form-item label="评测应用" prop="appId">
            <el-select
              @change="appIdChange"
              :style="{ width: '100%' }"
              v-model="form.appId"
              placeholder="请选择"
              filterable
            >
              <el-option v-for="(item, index) in applyList" :key="index" :label="item.appName" :value="item.appId" />
            </el-select>
          </el-form-item>

          <el-form-item label="评测服务" prop="serviceId">
            <el-select
              @change="serviceChange"
              :style="{ width: '100%' }"
              v-model="form.serviceId"
              placeholder="请选择"
              filterable
            >
              <el-option
                v-for="(item, index) in importList"
                :key="index"
                :label="item.serviceName"
                :value="item.serviceId"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="样本来源" prop="resource">
            <el-radio-group v-model="form.resource">
              <el-radio value="biaozhu">标注中心</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="评测样本选择" prop="sampleId">
            <el-select :style="{ width: '100%' }" v-model="form.sampleId" placeholder="请选择" multiple>
              <el-option
                v-for="(item, index) in annoTaskList"
                :key="index"
                :label="`${item.value}（${item.key}）`"
                :value="item.key"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="评测场景" prop="occasion">
            <el-radio-group v-model="form.occasion">
              <el-radio value="line">线上服务</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="是否输入额外参数" prop="userAddtionalParam">
            <el-radio-group v-model="form.userAddtionalParam">
              <el-radio :value="1">是</el-radio>
              <el-radio :value="0">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <div class="text-[15px] font-medium mb-[10px] mt-[30px]">评测方法(单选)</div>
          <el-form-item label="人工评测" prop="method">
            <el-checkbox
              :model-value="Boolean(form.method === 'message')"
              @change="(e) => changeCheckbox(e, 'message')"
              class="checkbox-style"
              name="Labour"
            >
              <el-card
                style="width: 207px"
                :class="['h-[107px]', 'relative', Boolean(form.method === 'message') ? 'check-active' : '']"
                shadow="always"
              >
                <div class="text-[14px] color-[#666666] mb-[10px] ml-[16px]">纠错评测方法</div>
                <div class="text-[12px] color-[#666666] whitespace-break-spaces leading-[18px]">
                  基于ASR 原文、模型纠错结果，与人工标注结果作对比，计算纠错召回、精准率。
                </div>
              </el-card>
            </el-checkbox>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="submitForm(ruleFormRef)"> 提 交 </el-button>
            <el-button @click="resetForm(ruleFormRef)">取 消</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, reactive, ref, defineExpose, watch } from 'vue';
import { ElMessage } from 'element-plus';
import PopoverComponent from './popoverComponent.vue';
import type { FormInstance, FormRules } from 'element-plus';
import { useEnumsStore } from '@/store/useEnumsStore';
import { useUserStore } from '@/store/useUserStore';
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const dialogFormVisible = ref(false);
const serviceList = ref<{ value: string; key: string; appendix: string }[]>([]);
const annoTaskList = ref<{ value: string; key: string }[]>([]);
const ruleFormRef = ref(null);
const enumsStore = useEnumsStore();
const userStore = useUserStore();
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
};
const form = reactive({
  appId: null,
  taskName: null,
  taskType: 0,
  serviceId: null,
  sampleId: [],
  resource: 'biaozhu',
  occasion: 'line',
  method: 'message',
  userAddtionalParam: 0,
});

const rules = reactive<FormRules>({
  appId: [{ required: true, message: '请选择所属应用' }],
  taskName: [{ required: true, message: '请输入任务名称' }],
  sampleId: [{ required: true, message: '请选择评测样本' }],
  serviceId: [{ required: true, message: '请选择对应服务' }],
  occasion: [{ required: true, message: '请选择评测场景' }],
  resource: [{ required: true, message: '请选择样本来源' }],
  userAddtionalParam: [{ required: true, message: '是否输入额外参数为必填' }],
});
const props = defineProps({
  applyList: {
    type: Object,
  },
  importBatch: {
    type: Object,
  },

  importList: {
    type: Object,
  },
});
const emit = defineEmits(['fetchList']);

function changeCheckbox(e, type) {
  if (!e) {
    form.method = null;
  }

  form.method = type;
}

function showRouge(type) {
  if (type === 'Rouge规则打分') {
  }
}
const appIdChange = (value) => {
  form.appId = value;
  form.sampleId = [];
  annoTaskList.value = [];
  form.userAddtionalParam = null;
  form.appId && form.serviceId && getAnnoTaskList();
};

const serviceChange = (value) => {
  form.serviceId = value;
  form.sampleId = [];
  annoTaskList.value = [];
  form.userAddtionalParam = null;
  form.appId && form.serviceId && getAnnoTaskList();
};

async function getAnnoTaskList() {
  if (!form.serviceId) {
    return;
  }
  if (!form.serviceId) return;
  const id = props.applyList?.find((item) => item.appId === form.appId)?.appId;

  const serviceId = props.importList?.find((item) => item.serviceId === form.serviceId)?.serviceId;
  const res = await enumsStore.fetchAnnoTaskList(id, serviceId, '3');
  annoTaskList.value = res as any;
}
const submitForm = async (formEl: FormInstance | undefined) => {
  console.log(form.userAddtionalParam, 444);
  if (!formEl) return;
  try {
    await formEl.validate();
    const new_anno_task_id = {};
    form.sampleId.forEach((e) => {
      new_anno_task_id[e] = annoTaskList.value.find((item) => item.key == e)?.value;
    });
    const res = await $http.ajax({
      url: '/nbi/evaluate/task/createOrUpdate',
      method: 'POST',
      data: {
        taskName: form.taskName,
        appId: form.appId,
        serviceId: form.serviceId,
        strategyId: 6,
        taskType: 0,
        busiColumnsJson: {
          userAddtionalParam: form.userAddtionalParam,
        },
        tblConfValues: {
          dataValueJson: {
            anno_task_id: {
              ...new_anno_task_id,
            },
          },
          strategyId: 6,
        },
      },
    });
    if (res.code === -1) {
      ElMessage({
        message: res.msg || '网络异常，请重试~',
        type: 'warning',
      });
    } else {
      ElMessage({
        message: '操作成功~',
        type: 'success',
      });
      dialogFormVisible.value = false;
      setTimeout(() => {
        emit('fetchList');
      }, 1000);
    }
  } catch (error) {
    console.log(error, 44);
  }
};

async function open(id) {
  form.appId = null;
  form.serviceId = null;
  form.taskName = null;
  form.sampleId = [];
  form.userAddtionalParam = 0;

  if (id) {
    const res = await $http.ajax({
      url: '/nbi/evaluate/task/getOneDetailByTaskId',
      method: 'get',
      params: {
        taskId: id,
      },
    });
    if (res) {
      form.appId = res.appId;
      form.serviceId = res.serviceId;
      form.appId && form.serviceId && getAnnoTaskList();
      form.taskName = res.taskName;
      form.taskType = res.taskType;
      form.userAddtionalParam = res.busiColumnsJson.userAddtionalParam || 0;
      console.log(res.busiColumnsJson.userAddtionalParam, 33);
      form.sampleId = Object.keys(res.tblConfValues.dataValueJson.anno_task_id).map((key) => +key);
    }
  }
  dialogFormVisible.value = true;
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  dialogFormVisible.value = false;
};

defineExpose({
  open,
});
</script>
<style lang="less">
.importDialog {
  width: 860px;
  .el-button--text {
    margin-right: 15px;
  }

  .el-select {
    width: 100%;
  }

  .dialog-footer button:first-child {
    margin-right: 10px;
  }

  .el-input__wrapper {
    width: 100%;
  }
}

.el-checkbox {
  margin-right: 15px !important;
}

.el-card__body {
  padding: 3px 3px !important;
}

.check-active {
  border: 1px solid #376aff;
}
.checkbox-style {
  height: 107px;
  position: relative;

  .el-checkbox__input {
    position: absolute;
    top: 1px;
    left: 8px;
    .el-checkbox__inner {
      width: 18px;
      height: 18px;

      &::after {
        left: 6px;
        top: 2px;
      }
    }
  }
}

.el-date-table__row {
  .ignore,
  .disabled {
    background: #ffffff;
  }
}
</style>
