<template>
  <div class="text-12px mt-40px color-#666 mt-10px mb-5px">明细指标一：</div>

  <div class="mt-10px mb-20px flex space-between flex-content-center flex-items-center">
    <span class="text-14px color-#606266 w-[60px]">标准词：</span>
    <el-select
      class="w-[300px]"
      v-model="standardWords"
      multiple
      collapse-tags
      placeholder="请选择"
      @change="getStandardWordsList"
    >
      <!-- <el-option label="全部" :value="option.value" /> -->
      <el-option
        v-for="option in statusOptionsEnum"
        :key="option"
        :label="option"
        :value="option"
      />
    </el-select>
  </div>
  <el-table :data="indexTableFirstList" style="width: 100%">
    <el-table-column prop="standardWord" label="">
      <el-table-column prop="standardWord" label="标准词" />
    </el-table-column>

    <el-table-column prop="entityType" label="">
      <el-table-column prop="entityType" label="实体类型" />
    </el-table-column>

    <el-table-column prop="orgPrecisionRate" label="原文指标">
      <el-table-column prop="orgPrecisionRate" label="精准率">
        <template #default="scope">
          {{ Math.round(scope.row.orgPrecisionRate * 100) + "%" }}
        </template>
      </el-table-column>
    </el-table-column>

    <el-table-column prop="orgRecallRate" label="">
      <el-table-column prop="orgRecallRate" label="召回率">
        <template #default="scope">
          {{ Math.round(+scope.row.orgRecallRate * 100) + "%" }}
        </template>
      </el-table-column>
    </el-table-column>

    <el-table-column prop="orgF1" label="">
      <el-table-column prop="orgF1" label="F1"></el-table-column>
    </el-table-column>

    <el-table-column prop="llmPrecisionRate" label="模型指标">
      <el-table-column prop="llmPrecisionRate" label="精准率">
        <template #default="scope">
          {{ Math.round(scope.row.llmPrecisionRate * 100) + "%" }}
        </template>
      </el-table-column>
    </el-table-column>

    <el-table-column prop="llmRecallRate" label="">
      <el-table-column prop="llmRecallRate" label="召回率">
        <template #default="scope">
          {{ Math.round(scope.row.llmRecallRate * 100) + "%" }}
        </template>
      </el-table-column>
    </el-table-column>

    <el-table-column prop="llmF1" label="">
      <el-table-column prop="llmF1" label="F1"></el-table-column>
    </el-table-column>

    <el-table-column prop="dimEntityNum" label="统计数据">
      <el-table-column prop="dimEntityNum" label="标注命中量"></el-table-column>
    </el-table-column>

    <el-table-column prop="orgRecallNum" label="">
      <el-table-column prop="orgRecallNum" label="原文命中量"></el-table-column>
    </el-table-column>
    <el-table-column prop="llmRecallNum" label="">
      <el-table-column prop="llmRecallNum" label="模型命中量"></el-table-column>
    </el-table-column>

    <el-table-column prop="dimVsOrgCount" label="">
      <el-table-column prop="dimVsOrgCount" label="标注原文一致量"></el-table-column>
    </el-table-column>

    <el-table-column label="" prop="llmRecallNum">
      <el-table-column prop="llmRecallNum" label="纠错后识别量"></el-table-column>
    </el-table-column>

    <el-table-column prop="dimVsLlmCount" label="">
      <el-table-column prop="dimVsLlmCount" label="标注纠错一致量"></el-table-column>
    </el-table-column>
  </el-table>
  <div class="pagination">
    <el-pagination
      :current-page="pagination.page"
      :page-size="pagination.size"
      layout="total, prev, pager, next, jumper"
      :total="pagination.total"
      :background="'#4455FF'"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from "vue";
import { useRoute } from "vue-router";

const indexTableFirstList = ref([]);
const pagination = ref({
  page: 1,
  size: 5,
  total: 0,
});
const route: any = useRoute();
const standardWords = ref([]);
const statusOptionsEnum = ref([]);
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const batchId = route.query && route.query.id;
onMounted(async () => {
  fetchList();
  fetchEnum();
});

const handleCurrentChange = (val: number) => {
  pagination.value.page = val;
  fetchList();
};
async function fetchList() {
  const res = await $http.ajax({
    url: "/nbi/evaluate/evalResult/correctError/detailPage",
    params: {
      page: pagination.value.page,
      size: pagination.value.size,
      batchId,
      standardWords: standardWords.value.join(","),
    },
  });
  if (!res) return;

  pagination.value.total = res.total;
  indexTableFirstList.value = res.list;
}

async function fetchEnum() {
  const res = await $http.ajax({
    url: "/nbi/evaluate/evalResult/correctError/getStandardWord",
    params: {
      batchId,
    },
  });
  if (!res) return;
  statusOptionsEnum.value = res;
}

function getStandardWordsList(data) {
  standardWords.value = data;
  pagination.value.page = 1;
  fetchList();
}

const tableData = [
  {
    date: "2016-05-03",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036",
  },
  {
    date: "2016-05-01",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036",
  },
  {
    date: "2016-05-08",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036",
  },
  {
    date: "2016-05-06",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036",
  },
  {
    date: "2016-05-07",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036",
  },
];
</script>
<style lang="less" scoped>
.id-text {
  color: #4455ff;
}

.pagination {
  display: flex;
  justify-content: end;
  margin-top: 40px;
}

.total {
  margin-left: 30px;
  font-size: 12px;
  font-weight: 500;
  color: #686464;
  margin-bottom: 10px;
}
</style>
