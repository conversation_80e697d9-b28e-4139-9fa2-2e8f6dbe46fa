<template>
  <div class="flex items-center mt-25px">
    <div class="text-12px color-#666">评测明细示例：</div>
    <div class="flex items-center">
      <el-button type="text" @click="handleDownload">
        <el-icon size="18px"><Download /></el-icon> 全量明细下载
      </el-button>
    </div>
  </div>

  <el-table :data="sampleList" style="width: 100%">
    <el-table-column prop="callId" label="通话ID"> </el-table-column>

    <el-table-column prop="sentenceId" label="句子ID"> </el-table-column>

    <el-table-column prop="orgText" label="原文文本" />

    <el-table-column prop="dimText" label="人工标注文本" />
    <el-table-column prop="llmText" label="模型纠错文本" />
    <el-table-column prop="orgTagReg" label="原文识别词" />
    <el-table-column prop="dimTagReg" label="标注识别词" />
    <el-table-column prop="llmTagReg" label="模型识别词" />
    <el-table-column prop="orgStandard" label="原文标准词" />
    <el-table-column prop="dimStandard" label="标注标准词" />
    <el-table-column prop="llmStandard" label="模型标准词" />
    <el-table-column prop="orgTagRegType" label="原文实体类型" />
    <el-table-column prop="dimTagRegType" label="标注实体类型" />
    <el-table-column prop="llmTagRegType" label="模型实体类型" />

    <el-table-column prop="dimVsOrg" label="标注 VS 原文" />

    <el-table-column prop="dimVsLlm" label="标注 VS 模型" />
  </el-table>
  <div class="pagination">
    <el-pagination
      :current-page="pagination.page"
      :page-size="pagination.size"
      layout="total, prev, pager, next, jumper"
      :total="pagination.total"
      :background="'#4455FF'"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
const sampleList = ref([]);
const pagination = ref({
  page: 1,
  size: 5,
  total: 0,
});
const route: any = useRoute();
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const batchId = route.query && route.query.id;
onMounted(async () => {
  fetchList();
});

const props = defineProps({
  taskData: {
    type: Object,
  },
});

const handleDownload = async () => {
  const res = await $http.ajax({
    url: "/nbi/evaluate/taskBatch/downLoadDetails",
    // method: 'POST',
    params: {
      batchId,
      downLoadType: 0,
    },
    responseType: "blob",
  });

  if (res) {
    const blob = new Blob([res.data], {
      type: "application/vnd.ms-excel;charset=utf-8",
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = url;
    link.setAttribute("id", "downloadLink");
    link.setAttribute(
      "download",
      `${props.taskData?.taskName}_${props.taskData.id}.xlsx`
    );
    document.body.appendChild(link);
    link.click();
    const objLink = document.getElementById("downloadLink");
    objLink && document.body.removeChild(objLink);
    ElMessage({
      type: "success",
      message: "导出成功",
    });
  }
};

const handleCurrentChange = (val: number) => {
  pagination.value.page = val;
  fetchList();
};
async function fetchList() {
  const res = await $http.ajax({
    url: "/nbi/evaluate/evalDetail/correctError/page",
    params: {
      page: pagination.value.page,
      size: pagination.value.size,
      batchId,
    },
  });
  if (!res) return;

  pagination.value.total = res.total;
  sampleList.value = res.list;
}
</script>
<style lang="less" scoped>
.id-text {
  color: #4455ff;
}

.pagination {
  display: flex;
  justify-content: end;
  margin-top: 40px;
}

.total {
  margin-left: 30px;
  font-size: 12px;
  font-weight: 500;
  color: #686464;
  margin-bottom: 10px;
}
</style>
