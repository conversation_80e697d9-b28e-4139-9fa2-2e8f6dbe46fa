<template>
  <el-form label-suffix="：" ref="ruleFormRef" :model="form">
    <el-row :gutter="30">
      <el-col :span="8">
        <el-form-item label="任务ID/任务名称">
          <el-input
            clearable
            @keyup.enter="onSubmit"
            placeholder="任务名称/任务ID"
            v-model="form.task"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="任务状态">
          <el-select v-model="form.taskStatusList" placeholder="全部">
            <el-option label="全部" :value="null" />
            <el-option label="未执行" value="20" />
            <el-option label="计算中" value="30" />
            <el-option label="成功" value="40" />
            <el-option label="失败" value="50" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="创建时间">
          <el-date-picker
            @change="createdTimeChange"
            v-model="createdTime"
            :shortcuts="shortcuts"
            type="daterange"
            range-separator="至"
            start-placeholder="起始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="30">
      <el-col :span="8">
        <el-form-item label="应用">
          <el-select v-model="form.appId" placeholder="全部">
            <el-option label="全部" :value="null" />
            <el-option
              v-for="(item, index) in applyList"
              :key="index"
              :label="item.appName"
              :value="item.appId"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="服务">
          <el-select v-model="form.serviceId" placeholder="全部">
            <el-option label="全部" :value="null" />
            <el-option
              v-for="(item, index) in importList"
              :key="index"
              :label="item.serviceName"
              :value="item.serviceId"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="创 建 人 ">
          <el-select
            class="importBatches"
            v-model="form.createBy"
            placeholder="全部"
            collapse-tags
            collapse-tags-tooltip
            filterable
            :max-collapse-tags="3"
          >
            <el-option label="全部" :value="null" />
            <el-option
              v-for="(item, index) in creatorsList"
              :key="index"
              :label="item.name"
              :value="item.account"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="search-btns">
      <el-form-item>
        <el-button @click="reset">重 置</el-button>
        <el-button type="primary" @click="onSubmit">搜 索</el-button>
      </el-form-item>
    </div>
  </el-form>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref, defineExpose } from "vue";
import { useRoute } from "vue-router";
import moment from "moment";
import { sleep } from "@/common/helper";
import type { FormInstance } from "element-plus";
import { useAuth } from "@/hooks/useAuth";
import { useEnumsStore } from "@/store/useEnumsStore";
import { useUserStore } from "@/store/useUserStore";

const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const ruleFormRef = ref<FormInstance>();
const applyList = ref<{ appId: string; appName: string; id: string }[]>([]);
const importList = ref<{ serviceName: string; serviceId: string }[]>([]);
const creatorsList = ref<{ value: string; account: string }[]>([]);
const annoTaskList = ref<{ value: string; key: string }[]>([]);

const applySearchList = ref<{ appId: string; appName: string; id: string }[]>([]);
const importSearchList = ref<{ serviceName: string; serviceId: string }[]>([]);
const creatorsSearchList = ref<{ value: string; key: string }[]>([]);
const annoTaskSearchList = ref<{ value: string; key: string }[]>([]);
const enumsStore = useEnumsStore();
const store = useUserStore();

const Route = useRoute();

const form = ref({
  task: null,
  appId: null,
  serviceId: null,
  taskStatusList: null,
  createBy: null,
  startTime: "",
  endTime: "",
});

const createdTime = ref([
  new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 14),
  new Date(),
]);

const createdTimeChange = (val: any) => {
  createdTime.value = val;
  if (
    !val ||
    !val.length ||
    val.filter((e) => moment(e).format("YYYY-MM-DD") === "Invalid date").length
  ) {
    form.value.startTime = "";
    form.value.endTime = "";
    return;
  }
  form.value.startTime = moment(val[0]).format("YYYY-MM-DD");
  form.value.endTime = moment(val[1]).format("YYYY-MM-DD");
};
const shortcuts = [
  {
    text: "近14天",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 14);
      return [start, end];
    },
  },
  {
    text: "近30天",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "近60天",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 60);
      return [start, end];
    },
  },
  {
    text: "全部",
    value: () => {
      createdTime.value = [];
      return [null, null];
    },
  },
];

//defineEmits

const emit = defineEmits(["onSubmit", "getEnums"]);
defineExpose({
  getAppList,
});
onMounted(async () => {
  getAppList();
  Promise.all([getSearchAppList(), getServiceList(), enumsStore.fetchAnnoTaskList()])
    .then((values) => {
      applySearchList.value = values[0] as [];
      importSearchList.value = values[1] as [];
      annoTaskSearchList.value = values[2] as [];
      emit("getEnums", {
        applyList: applySearchList.value,
        importList: importSearchList.value,
        annoTaskList: annoTaskSearchList.value,
      });
      if (localStorage.getItem("searchErrorEvaluateData")) {
        const search = JSON.parse(
          localStorage.getItem("searchErrorEvaluateData") as string
        );
        if (typeof search === "object") {
          createdTime.value = [
            search.startTime && moment(search.startTime).format("YYYY-MM-DD"),
            search.endTime && moment(search.endTime).format("YYYY-MM-DD"),
          ];
          form.value = {
            ...search,
          };
          emit("onSubmit", form.value);
        }
      } else {
        reset();
      }
    })
    .catch((err) => {
      console.log(err);
    });
  await sleep(1000);
});

async function getSearchAppList() {
  const res = await $http.ajax({
    url: "/nbi/aiplat/app/appIdMapping",
    method: "GET",
  });
  return res;
}

async function getAppList() {
  const res = await $http.ajax({
    url: "/nbi/evaluate/task/searchCondition",
    method: "GET",
    params: {
      taskType: 0,
    },
  });

  if (res.code) {
    return;
  }
  applyList.value = res.appList as [];
  importList.value = res.serviceList as [];
  creatorsList.value = res.creators as [];
  return res;
}

const getServiceList = async () => {
  const res = await $http.ajax({
    url: "/nbi/aiplat/svc/serviceIdMapping",
    method: "GET",
  });
  if (!res.code) {
    return res;
  } else {
    console.log("error");
  }
};

const onSubmit = async () => {
  emit("onSubmit", form.value);
};
const reset = () => {
  createdTime.value = [
    new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 14),
    new Date(),
  ];
  form.value = {
    task: null,
    appId: null,
    serviceId: null,
    taskStatusList: null,
    createBy: null,
    startTime: moment(createdTime.value[0]).format("YYYY-MM-DD"),
    endTime: moment(createdTime.value[1]).format("YYYY-MM-DD"),
  };
  emit("onSubmit", form.value);
};
</script>
<style lang="less" scoped>
.el-select {
  width: 100%;
}

.search-btns {
  display: flex;
  justify-content: right;

  .el-button {
    width: 85px;
  }
}
</style>
