<template>
  <div class="warning-message">
    <el-text class="mx-1" type="warning"
      >【说明】：面向文本纠错场景，如语音转文本纠错、翻译纠错等。</el-text
    >
  </div>

  <NotAuth v-if="useAuth()" />
  <template v-else>
    <div class="page-content search-content">
      <Search ref="sampleSearch" @onSubmit="onSubmit" @getEnums="getEnums" />
    </div>
    <div class="page-content">
      <SampleList
        ref="sampleList"
        @getPage="getPage"
        :loading="loading"
        :applyList="applyList"
        :importList="importList"
        :listData="listData"
        :annoTaskList="annoTaskList"
      />
    </div>
  </template>
</template>
<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from "vue";
import { ElMessage } from "element-plus";

import SampleList from "./components/list.vue";
import Search from "./components/search.vue";
import NotAuth from "@/views/401.vue";

import { useAuth } from "@/hooks/useAuth";
import { relativeNode } from "../../../plugins/JsMark/util/index";
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const listData = ref<any>();
let sampleList = ref();
let sampleSearch = ref();
const searchData = ref({
  task: null,
  appId: null,
  serviceId: null,
  taskStatusList: null,
  createBy: null,
  startTime: null,
  endTime: null,
  page: 1,
  size: 10,
});

const loading = ref(false);
const applyList = ref<{ value: string; key: string }[]>([]);
const importList = ref<{ value: string; key: string }[]>([]);
const annoTaskList = ref<{ value: string; key: string }[]>([]);
function getPage(pageData) {
  const { page, pageSize } = pageData;
  searchData.value.page = page;
  searchData.value.size = pageSize;
  fetchData();
  sampleSearch.value.getAppList();
}

function getEnums(pageData) {
  applyList.value = pageData.applyList;
  importList.value = pageData.importList;
  annoTaskList.value = pageData.annoTaskList;
}
function onSubmit(data) {
  searchData.value = { ...searchData.value, ...data, page: 1, size: 10 };
  fetchData();
}

async function fetchData() {
  loading.value = true;
  localStorage.setItem("searchErrorEvaluateData", JSON.stringify(searchData.value));
  const res = await $http.ajax({
    url: "/nbi/evaluate/task/taskPage",
    method: "get",
    params: {
      ...searchData.value,
      startTime: searchData.value.startTime ? searchData.value.startTime : null,
      endTime: searchData.value.endTime ? searchData.value.endTime : null,
      taskType: 0,
      page: +searchData.value.page,
      size: searchData.value.size,
    },
  });
  if (!res.code) {
    // ElMessage({
    //   message: '操作成功~',
    //   type: 'success',
    // })
  } else {
    ElMessage({
      message: "网络异常，请重试~",
      type: "warning",
    });
  }
  loading.value = false;
  listData.value = res;
  return res;
}
</script>
<style lang="less" scoped>
.warning-message {
  // background-color: #FFFFFF;
  padding: 0px 4px 20px;
  position: relative;
  left: -20px;
  top: 0px;
  // width: calc(100vw - 200px);
  z-index: 999;
}

.search-content {
}
</style>
