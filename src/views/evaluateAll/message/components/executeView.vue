<template>
  <div>
    <el-breadcrumb style="margin: 10px 0 20px" :separator-icon="ArrowRight">
      <el-breadcrumb-item :to="{ path: '/evaluation/message' }">评测列表</el-breadcrumb-item>
      <el-breadcrumb-item
        :to="{
          path: '/messageEvaluation/detail',
          query: { id: route.query && route.query.taskId },
        }"
        >{{ (route.query && route.query.taskName) || '_' }}</el-breadcrumb-item
      >
      <el-breadcrumb-item>{{ taskData && taskData?.id }}</el-breadcrumb-item>
    </el-breadcrumb>

    <div v-if="taskData" class="page-content" style="position: relative">
      <div class="mb-60px" v-if="taskData">
        <div class="text-14px mt-16px">执行信息：</div>
        <div class="flex items-center flex-wrap gap-20px justify-start mt-10px mb-30px">
          <div class="color-#666">
            执行ID：
            <span class="color-dark font-600">{{ taskData?.id }}</span>
          </div>

          <div class="color-#666">
            执行状态：
            <span class="color-dark font-600">{{
              taskStatusOptions.find((e) => e.value === taskData.batchStatus)?.label
            }}</span>
          </div>
          <div class="color-#666 mr-20px">
            执行时间：
            <span class="color-dark font-600">{{ moment(taskData!.submitTime).format('YYYY-MM-DD HH:mm') }}</span>
          </div>

          <div class="color-#666 mr-20px">
            耗时：
            <span v-if="!taskData.timeCostMs" class="color-dark font-600">{{ taskData.timeCostMs }}秒</span>
            <span class="color-dark font-600" v-else-if="taskData.timeCostMs < 0">0秒</span>
            <span v-else class="color-dark font-600">
              {{
                taskData.timeCostMs / 1000 < 60
                  ? `${taskData.timeCostMs / 1000}秒`
                  : `${Math.floor(taskData.timeCostMs / 60000)}分${Math.round(
                      +((taskData.timeCostMs % 60000) / 1000).toFixed(1)
                    )}秒`
              }}
            </span>
          </div>

          <div class="color-#666">
            评测方法：
            <span class="color-dark font-600"> 信息提取评测方法 </span>
          </div>
        </div>

        <div class="text-14px">推送蓝图：</div>
        <div class="mt-10px mb-30px">
          <div class="color-#666">
            <div v-if="taskData.busiColumnsJson && taskData.busiColumnsJson.markedValidBatch">
              <span>已推送，推送版本号：</span>
              <span class="color-dark font-600">{{
                taskData.busiColumnsJson && taskData.busiColumnsJson.markedValidBatch
              }}</span>
            </div>
            <span v-else>未推送</span>
          </div>
        </div>
      </div>

      <div class="button-box">
        <el-button
          v-if="taskData.busiColumnsJson && taskData.busiColumnsJson.markedValidBatch"
          type="warning"
          @click="toExecute"
        >
          取消推送</el-button
        >
        <el-button v-else type="warning" @click="toExecute"> 推送蓝图</el-button>
      </div>
    </div>

    <div class="page-content mt-20px" style="position: relative">
      <el-tabs v-model="activeName">
        <el-tab-pane label="评估报告" name="1"></el-tab-pane>
        <el-tab-pane label="样本明细" name="2"></el-tab-pane>
      </el-tabs>
      <div>
        <div v-if="activeName == 1">
          <template v-if="store.permissionCode.includes('23.81.520.521.522.523.524')">
            <div class="text-12px color-#666 mt-10px mb-5px">整体指标：</div>
            <div>
              <div class="flex items-center">
                <div class="text-14px">评测结果：</div>
                <el-button type="text" @click="handleDownload('detail')">
                  <el-icon size="18px"><Download /></el-icon> 下载
                </el-button>
              </div>

              <div class="text-12px color-#666 mt-10px mb-5px">整体效果：</div>
              <el-table :data="tableData" stripe style="width: 80%">
                <el-table-column prop="tagId" label="评测质检项数" width="180" />
                <el-table-column prop="precision" label="精准率" />
                <el-table-column prop="recall" label="召回率" />
                <el-table-column prop="F1" label="F1" />
              </el-table>

              <div class="text-12px color-#666 mb-5px mt-60px">各质检项效果：</div>
              <el-table :data="tagsResults" stripe style="width: 80%">
                <!-- <el-table-column prop="tagId" label="模型来源" width="180" /> -->
                <el-table-column prop="tagName" label="质检项" width="180" />
                <el-table-column prop="precisionRate" label="精准率">
                  <template #default="scope">
                    {{
                      !isNaN(scope.row.precisionRate)
                        ? Math.fround(+scope.row.precisionRate * 100)
                        : scope.row.precisionRate
                    }}%
                  </template>
                </el-table-column>
                <el-table-column prop="callRate" label="召回率">
                  <template #default="scope">
                    {{ !isNaN(scope.row.callRate) ? Math.fround(+scope.row.callRate * 100) : scope.row.callRate }}%
                  </template>
                </el-table-column>
                <el-table-column prop="f1" label="F1">
                  <template #default="scope">{{ scope.row.f1 }}</template>
                </el-table-column>
              </el-table>
            </div>
          </template>
          <template v-else>
            <NoPermission />
          </template>
        </div>

        <div v-if="activeName == 2">
          <template v-if="store.permissionCode.includes('23.81.520.521.522.523.525')">
            <div class="flex items-center mt-25px">
              <div class="text-12px color-#666">评测明细示例：</div>
              <div class="flex items-center">
                <el-button type="text" @click="handleDownload('tag')">
                  <el-icon size="18px"><Download /></el-icon> 全量明细下载
                </el-button>
              </div>
            </div>
            <SampleDetailTable :taskData="taskData" />
          </template>
          <template v-else>
            <NoPermission />
          </template>
        </div>
      </div>
    </div>
  </div>
  <!-- 添加的闭合标签 -->
</template>

<script lang="ts" setup>
import SampleDetailTable from './sampleDetailTable';
import { getCurrentInstance, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import moment from 'moment';
import { ElMessage, ElMessageBox } from 'element-plus';
import NoPermission from '@/views/401.vue';
import { ArrowRight, Download } from '@element-plus/icons-vue';

import { useEnumsStore } from '@/store/useEnumsStore';
import { useUserStore } from '@/store/useUserStore';
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const store = useUserStore();
const enumsStore = useEnumsStore();
const route: any = useRoute();
const taskData = ref<any>({});
const tagsResults = ref([]);
const activeName = ref('1');
const annoTaskList = ref<{ value: string; key: string }[]>([]);
const tableData = ref();
const taskStatusOptions = [
  { label: '未执行', value: 20 },
  { label: '计算中', value: 30 },
  { label: '完成', value: 40 },
  { label: '失败', value: 50 },
];
const toExecute = () => {
  const str =
    taskData.value.busiColumnsJson && taskData.value.busiColumnsJson.markedValidBatch ? '取消推送蓝图' : '推送蓝图';
  ElMessageBox.confirm(`确定要${str}展示吗?`, str, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    $http
      .ajax({
        url: '/nbi/evaluate/taskBatch/markDataStatus',
        method: 'POST',
        data: {
          batchId: taskData.value.id,
        },
      })
      .then((res) => {
        if (res.msg) {
          ElMessage({
            type: 'error',
            message: res.msg || '操作失败',
          });
          return;
        }
        ElMessage({
          type: 'success',
          message:
            taskData.value.busiColumnsJson && taskData.value.busiColumnsJson.markedValidBatch
              ? '取消推送成功'
              : '推送成功',
        });
        fetchDetail();
      })
      .catch((e) => {
        ElMessage({
          type: 'info',
          message: e || '操作失败',
        });
      });
  });
};

onMounted(async () => {
  await fetchDetail();
  await fetchWhole();
  await fetchEach();
  const res = await enumsStore.fetchAnnoTaskList();
  annoTaskList.value = res as any;
});

const handleDownload = async (type) => {
  let res = null;
  if (type === 'tag') {
    res = await $http.ajax({
      url: '/nbi/evaluate/taskBatch/downLoadDetails',
      params: {
        batchId: taskData.value.id,
        downLoadType: 1,
      },
      responseType: 'blob',
    });
  } else {
    res = await $http.ajax({
      url: '/nbi/evaluate/evalResult/nlp/downLoadResult',
      params: {
        batchId: taskData.value.id,
      },
      responseType: 'blob',
    });
  }

  if (res) {
    toExport(type, res.data);
  }
};

async function toExport(type, data) {
  const blob = new Blob([data], {
    type: 'application/vnd.ms-excel;charset=utf-8',
  });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('id', 'downloadLink');
  link.setAttribute(
    'download',
    `${route.query.taskName}_${type === 'tag' ? '明细数据' : '评测结果'}_${moment(taskData!.createdTime).format(
      'YYYY.MM.DD'
    )}.xlsx`
  );
  document.body.appendChild(link);
  link.click();
  const objLink = document.getElementById('downloadLink');
  objLink && document.body.removeChild(objLink);
  ElMessage({
    type: 'success',
    message: '导出成功',
  });
}

async function fetchWhole() {
  const batchId = route.query && route.query.id;
  const res = await $http.ajax({
    url: '/nbi/evaluate/evalResult/nlp/whole',
    params: {
      batchId,
    },
  });

  if (res.msg) {
    ElMessage.error(res.msg);
    return;
  }

  tableData.value = [
    {
      tagId: res.qualityCount,
      precision: (!isNaN(res.precisionRate) ? Math.fround(+res.precisionRate * 100) : res.precisionRate) + '%',
      recall: (!isNaN(res.callRate) ? Math.fround(+res.callRate * 100) : res.callRate) + '%',
      F1: res.f1,
    },
  ];
}

async function fetchEach() {
  const batchId = route.query && route.query.id;
  const res = await $http.ajax({
    url: '/nbi/evaluate/evalResult/nlp/each',
    params: {
      batchId,
    },
  });

  tagsResults.value = res;
}

async function fetchDetail() {
  const batchId = route.query && route.query.id;
  const res = await $http.ajax({
    url: '/nbi/evaluate/taskBatch/getOneDetailByBatchId',
    params: {
      batchId,
    },
  });

  taskData.value = res;
}
</script>
<style>
.button-box {
  position: absolute;
  right: 20px;
  top: 20px;
}
</style>
