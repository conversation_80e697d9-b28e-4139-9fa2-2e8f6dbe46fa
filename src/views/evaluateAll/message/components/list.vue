<template>
  <div class="flex text-[15px] font-medium justify-items justify-between mb-[10px]">
    <div class="mt-[10px]">任务列表</div>

    <div v-if="store.permissionCode.includes('23.81.520.443')">
      <el-button type="success" @click="handleCopy(null)"> + 新建任务 </el-button>
      <ImportDialog
        ref="importDialogRef"
        :applyList="applyList"
        :importList="importList"
        :annoTaskList="annoTaskList"
        @fetchList="onSubmit"
      />
    </div>
  </div>
  <el-table v-loading="loading" :data="tableData" style="width: 100%">
    <el-table-column label="ID" width="60" prop="id" fixed="left" />

    <el-table-column label="任务名称" width="160" prop="taskName" fixed="left">
      <template #default="scope">
        <span class="id-text" @click="handleRouter(scope.row)">{{
          scope.row.taskName
        }}</span>
      </template>
    </el-table-column>

    <el-table-column label="执行状态" fixed="left">
      <template #default="scope">
        <div :style="{ color: colorObj[scope.row.taskStatus] }">
          <span>{{ statusObj[scope.row.taskStatus] }} </span>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="最近执行时间" width="160">
      <template #default="scope">
        {{
          scope.row.lastRunTime &&
          moment(scope.row.lastRunTime).format("YYYY-MM-DD HH:mm")
        }}
      </template>
    </el-table-column>

    <el-table-column label="应用" width="200">
      <template #default="scope">
        {{
          applyList?.length && applyList.find((e) => e.appId === scope.row.appId)?.appName
        }}
      </template>
    </el-table-column>
    <el-table-column label="服务" width="200">
      <template #default="scope">
        {{
          importList.length &&
          importList.find((e) => e.serviceId === scope.row.serviceId)?.serviceName
        }}
      </template>
    </el-table-column>

    <el-table-column label="评测方法" width="160">
      <template #default="scope">
        <el-tooltip class="box-item" effect="dark" content="" placement="top">
          <template #content>
            <div class="w-[300px]">
              利用模型提取对话中的信息，如客户意向品牌、销售话术规范等，用提取信息与人工标注结果作对比，计算纠错召回、精准率等指标
            </div>
          </template>
          <div class="flex items-center">
            信息提取评测方法 <el-icon class="ml-[5px]"><QuestionFilled /></el-icon>
          </div>
        </el-tooltip>
      </template>
    </el-table-column>

    <el-table-column label="创建人" prop="createByName" />

    <el-table-column label="创建时间" width="160">
      <template #default="scope">
        {{ moment(scope.row.createTime).format("YYYY-MM-DD HH:mm") }}
      </template>
    </el-table-column>

    <el-table-column label="评测样本" width="160">
      <template #default="scope">
        <div class="flex flex-wrap gap-2">
          <el-tag
            v-for="item in scope.row.tblConfValues.dataValueJson.anno_task_id &&
            Object.values(scope.row.tblConfValues.dataValueJson.anno_task_id)"
            :key="item"
            type="success"
            size="small"
            >{{ item }}</el-tag
          >
        </div>
      </template>
    </el-table-column>

    <el-table-column label="操作" width="210" fixed="right">
      <template #default="scope">
        <el-button
          @click="handleCopy(scope.row)"
          :disabled="[4].includes(scope.row.taskStatus)"
          class="mr-20px"
          type="text"
          >复制</el-button
        >
        <el-button
          @click="handleRouter(scope.row)"
          :disabled="[4].includes(scope.row.taskStatus)"
          class="mr-20px"
          type="text"
          >任务详情</el-button
        >
        <el-button
          :disabled="
            (store.userInfo as any).domainAccount !== scope.row.createBy
          "
          @click="handleTranslateStatus(scope.row)"
          type="text"
          >删除</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <div class="pagination">
    <el-pagination
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, prev, pager, next, jumper, sizes"
      :total="total"
      :background="'#4455FF'"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, ref, watch } from "vue";
import { useRouter } from "vue-router";
import moment from "moment";
import { ElMessage, ElMessageBox } from "element-plus";
import ImportDialog from "./importDialog.vue";
import { useUserStore } from "@/store/useUserStore";

const colorObj = {
  "10": "#1E90FF",
  "20": "#f1db11f8",
  "30": "#93158e",
  "40": "#00a700",
  "50": "#ff4a37",
};

const statusObj = {
  "20": "未执行",
  "30": "计算中",
  "40": "成功",
  "50": "失败",
};

const store = useUserStore();

const props = defineProps({
  listData: {
    type: Object,
  },

  applyList: {
    type: Object,
    default: () => {
      return [];
    },
  },

  importList: {
    type: Object,
    default: () => {
      return [];
    },
  },

  annoTaskList: {
    type: Object,
    default: () => {
      return [];
    },
  },

  loading: {
    type: Boolean,
  },
});

watch(
  () => props.listData,
  () => {
    if (props.listData?.list) {
      tableData.value = props.listData.list;
    }

    total.value = props.listData?.total;
    currentPage.value = props.listData?.pageNum;
    pageSize.value = props.listData?.pageSize;
  }
);

const emit = defineEmits(["getPage"]);
const router = useRouter();
const currentPage = ref(1);
const total = ref(0);
const pageSize = ref(10);
const tableData = ref([]);
const importDialogRef = ref(null);
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  emit("getPage", { page: currentPage.value, pageSize: pageSize.value });
};

function handleSizeChange(size) {
  currentPage.value = 1;
  pageSize.value = size;
  emit("getPage", { page: currentPage.value, pageSize: pageSize.value });
}

const handleCopy = (row) => {
  importDialogRef.value.open(row ? row.id : null);
};

const handleRouter = (row) => {
  router.push({
    path: "/messageEvaluation/detail",
    query: {
      id: row.taskId,
    },
  });
};

const handleTranslateStatus = async (row: any) => {
  try {
    await ElMessageBox.confirm("删除后不可恢复，是否确认？", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await $http.ajax({
      url: "/nbi/evaluate/task/deleteTaskById",
      method: "post",
      data: {
        id: row.id,
      },
    });
    if (res.code) {
      // throw new Error(res.msg)
      return ElMessage({
        type: "error",
        message: res.msg || "操作失败",
      });
    }
    ElMessage({
      type: "success",
      message: "操作成功",
    });
    emit("getPage", { page: currentPage.value, pageSize: pageSize.value });
  } catch (e: any) {
    // ElMessage({
    //   type: 'error',
    //   message: e.message || '操作失败',
    // })
  }
};

const onSubmit = async () => {
  emit("getPage", { page: currentPage.value, pageSize: pageSize.value });
};

const abandonUNAssignedSample = async (event, row) => {
  const res = await $http.ajax({
    url: "/nbi/anno/annoTask/abandonUNAssignedSample",
    method: "POST",
    data: {
      annoTaskId: row.id,
    },
  });
  if (!res.code) {
    emit("getPage", { page: currentPage.value, pageSize: pageSize.value });
  } else {
    ElMessage({
      message: "网络异常，请重试~",
      type: "warning",
    });
  }
};
</script>

<style lang="less" scoped>
.id-text {
  color: #4455ff;
  cursor: pointer;
}

.pagination {
  display: flex;
  justify-content: end;
  margin-top: 40px;
}

.total {
  margin-left: 30px;
  font-size: 12px;
  font-weight: 500;
  color: #686464;
  margin-bottom: 10px;
}
</style>
