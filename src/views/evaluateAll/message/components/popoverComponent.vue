<template>
  <el-popover placement="bottom" title="评测方法配置" :width="800" trigger="click">
    <template #reference>
      <div>
        方法配置<el-icon size="12px"><ArrowDownBold /></el-icon>
      </div>
    </template>
    <template #default>
      <div class="text-[12px] mb-[20px]">请选择需要统计的评分列（可多选）</div>
      <div class="popover-content">
        <el-table :data="tableData" height="300" style="width: 100%">
          <el-table-column>
            <template #header>
              <el-checkbox
                v-model="checkboxObj.id"
                @change="(e) => toggleAll(e, 'id')"
                label="问答ID"
              ></el-checkbox>
            </template>
            <template #default="scope"> {{ scope.row.id }} </template>
          </el-table-column>

          <el-table-column>
            <template #header>
              <el-checkbox
                v-model="checkboxObj.question"
                @change="(e) => toggleAll(e, 'question')"
                label="问题"
              ></el-checkbox>
            </template>
            <template #default="scope"> {{ scope.row.question }} </template>
          </el-table-column>

          <el-table-column>
            <template #header>
              <el-checkbox
                v-model="checkboxObj.model"
                @change="(e) => toggleAll(e, 'model')"
                label="模型"
              ></el-checkbox>
            </template>
            <template #default="scope"> {{ scope.row.model }} </template>
          </el-table-column>

          <el-table-column>
            <template #header>
              <el-checkbox
                v-model="checkboxObj.prompt"
                @change="(e) => toggleAll(e, 'prompt')"
                label="prompt"
              ></el-checkbox>
            </template>
            <template #default="scope"> {{ scope.row.prompt }} </template>
          </el-table-column>

          <el-table-column>
            <template #header>
              <el-checkbox
                v-model="checkboxObj.modelAnswer"
                @change="(e) => toggleAll(e, 'modelAnswer')"
                label="模型答案"
              ></el-checkbox>
            </template>
            <template #default="scope"> {{ scope.row.modelAnswer }} </template>
          </el-table-column>

          <el-table-column>
            <template #header>
              <el-checkbox
                v-model="checkboxObj.referenceAnswer1"
                @change="(e) => toggleAll(e, 'referenceAnswer1')"
                label="参考答案1"
              ></el-checkbox>
            </template>
            <template #default="scope"> {{ scope.row.referenceAnswer1 }} </template>
          </el-table-column>

          <el-table-column>
            <template #header>
              <el-checkbox
                v-model="checkboxObj.referenceAnswer2"
                @change="(e) => toggleAll(e, 'referenceAnswer2')"
                label="参考答案2"
              ></el-checkbox>
            </template>
            <template #default="scope"> {{ scope.row.referenceAnswer2 }} </template>
          </el-table-column>
        </el-table>
      </div>
    </template>
  </el-popover>
</template>

<script lang="ts" setup>
import { ref, defineEmits } from "vue";
import { ElPopover, ElIcon, ElTable, ElTableColumn, ElCheckbox } from "element-plus";
import { ArrowDownBold } from "@element-plus/icons-vue";

const emit = defineEmits(["all-selected"]);

const tableData = ref([
  {
    id: 1,
    question: "What is Vue?",
    model: "GPT-3",
    prompt: "Explain Vue",
    modelAnswer: "Vue is a progressive JavaScript framework...",
    referenceAnswer1: "Vue is an open-source model–view–viewmodel...",
    referenceAnswer2: "Vue is a framework for building user interfaces...",
  },
  {
    id: 2,
    question: "What is React?",
    model: "GPT-3",
    prompt: "Explain React",
    modelAnswer: "React is a JavaScript library for building user interfaces...",
    referenceAnswer1: "React is an open-source front-end JavaScript library...",
    referenceAnswer2: "React can be used as a base in the development of single-page...",
  },
  // Add more rows as needed
]);

const checkboxObj = ref({
  id: false,
  question: false,
  model: false,
  prompt: false,
  modelAnswer: false,
  referenceAnswer1: false,
  referenceAnswer2: false,
});

const toggleAll = (value: boolean, type: string) => {
  checkboxObj.value[type] = value;
  emit("all-selected", checkboxObj.value);
};
</script>

<style scoped>
.popover-content {
  max-height: 300px;
  overflow-y: auto;
}
</style>
