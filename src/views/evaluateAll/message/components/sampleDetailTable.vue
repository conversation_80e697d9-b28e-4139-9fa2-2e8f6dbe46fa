<template>
  <el-table :data="sampleList" style="width: 100%">
    <el-table-column prop="busiId" label="通话ID"> </el-table-column>

    <el-table-column prop="sentenceId" label="句子ID"> </el-table-column>

    <el-table-column prop="taskId" label="关联的任务id" />

    <el-table-column prop="taskBatchId" label="关联的任务批次id" />
    <el-table-column prop="annoTaskId" label="标注任务Id" />
    <el-table-column prop="role" label="角色" />
    <el-table-column prop="sentenceContent" label="句子内容" />
    <el-table-column prop="sentenceTime" label="句子时间" />
    <el-table-column prop="annoContents" label="标注内容" />
    <el-table-column prop="annoGroups" label="标注组" />
    <el-table-column prop="tagId" label="质检项id" />
    <el-table-column prop="tagName" label="质检项name" />
    <el-table-column prop="tagNameEn" label="质检项英文name" />
    <el-table-column prop="tagCode" label="质检项值" />
    <el-table-column prop="algTagId" label="算法质检项" />
    <el-table-column prop="algTagNameEn" label="算法质检项name" />
    <el-table-column prop="algTagCode" label="算法质检项值" />
    <el-table-column prop="annoVsLlmId" label="	质检项id是否一致" />
    <el-table-column prop="annoVsLlmCode" label="质检项名是否一致" />
    <el-table-column prop="annoVsLlmValue" label="质检项值是否一致" />
  </el-table>
  <div class="pagination">
    <el-pagination
      :current-page="pagination.page"
      :page-size="pagination.size"
      layout="total, prev, pager, next, jumper"
      :total="pagination.total"
      :background="'#4455FF'"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
const sampleList = ref([]);
const pagination = ref({
  page: 1,
  size: 5,
  total: 0,
});
const route: any = useRoute();
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const batchId = route.query && route.query.id;
onMounted(async () => {
  fetchList();
});

const props = defineProps({
  taskData: {
    type: Object,
  },
});

const handleDownload = async () => {
  const res = await $http.ajax({
    url: "/nbi/evaluate/taskBatch/downLoadDetails",
    // method: 'POST',
    params: {
      batchId,
      downLoadType: 0,
    },
    responseType: "blob",
  });

  if (res) {
    const blob = new Blob([res.data], {
      type: "application/vnd.ms-excel;charset=utf-8",
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = url;
    link.setAttribute("id", "downloadLink");
    link.setAttribute(
      "download",
      `${props.taskData?.taskName}_${props.taskData.id}.xlsx`
    );
    document.body.appendChild(link);
    link.click();
    const objLink = document.getElementById("downloadLink");
    objLink && document.body.removeChild(objLink);
    ElMessage({
      type: "success",
      message: "导出成功",
    });
  }
};

const handleCurrentChange = (val: number) => {
  pagination.value.page = val;
  fetchList();
};
async function fetchList() {
  const res = await $http.ajax({
    url: "/nbi/evaluate/evalDetail/nlp/page",
    params: {
      page: pagination.value.page,
      size: pagination.value.size,
      batchId,
    },
  });
  if (!res) return;

  pagination.value.total = res.total;
  sampleList.value = res.list;
}
</script>
<style lang="less" scoped>
.id-text {
  color: #4455ff;
}

.pagination {
  display: flex;
  justify-content: end;
  margin-top: 40px;
}

.total {
  margin-left: 30px;
  font-size: 12px;
  font-weight: 500;
  color: #686464;
  margin-bottom: 10px;
}
</style>
