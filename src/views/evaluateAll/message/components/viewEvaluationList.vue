<template>
  <div class="mt-10px mb-20px flex space-between flex-content-center flex-items-center">
    <span class="text-14px color-#606266">状态：</span>
    <el-select
      v-model="batchStatusList"
      multiple
      placeholder="请选择"
      style="width: 600px"
      @change="getBatchStatusList"
    >
      <!-- <el-option label="全部" :value="option.value" /> -->
      <el-option
        v-for="option in taskStatusOptions"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>
  </div>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column label="执行ID">
      <template #default="scope">
        <span v-if="![40].includes(scope.row.batchStatus)">{{ scope.row.id }}</span>
        <span v-else class="id-text" @click="handleRouter('view', scope.row)">{{
          scope.row.id
        }}</span>
      </template>
    </el-table-column>
    <el-table-column label="状态">
      <template #default="scope">
        <span :style="{ color: colorObj[scope.row.batchStatus] }">{{
          taskStatusOptions.find((e) => e.value === scope.row.batchStatus)?.label
        }}</span>
      </template>
    </el-table-column>
    <el-table-column label="整体效果">
      <template #default="scope">
        <span class="mr-10px">{{
          scope.row.resultDescJson && scope.row.resultDescJson.llmPrecisionRate
            ? `${Math.round(scope.row.resultDescJson.llmPrecisionRate * 10000) / 100}%`
            : "0%"
        }}</span>
        <span>{{
          scope.row.resultDescJson && scope.row.resultDescJson.llmRecallRate
            ? `${Math.round(scope.row.resultDescJson.llmRecallRate * 10000) / 100}%`
            : "0%"
        }}</span>
      </template>
    </el-table-column>
    <el-table-column label="评测推送版本">
      <template #default="scope">
        {{
          (scope.row.busiColumnsJson && scope.row.busiColumnsJson.markedValidBatch) || "-"
        }}
      </template>
    </el-table-column>
    <el-table-column label="提交执行时间">
      <template #default="scope">
        {{ moment(scope.row.submitTime).format("YYYY-MM-DD HH:mm") }}
      </template>
    </el-table-column>

    <el-table-column label="耗时">
      <template #default="scope">
        <span v-if="!scope.row.timeCostMs">-</span>
        <span v-else-if="scope.row.timeCostMs < 0">0秒</span>
        <span v-else>
          {{
            scope.row.timeCostMs / 1000 < 60
              ? `${scope.row.timeCostMs / 1000}秒`
              : `${Math.floor(scope.row.timeCostMs / 60000)}分${Math.round(
                  +((scope.row.timeCostMs % 60000) / 1000).toFixed(1)
                )}秒`
          }}
        </span>
      </template>
    </el-table-column>
    <el-table-column label="操作" fixed="right">
      <template #default="scope">
        <el-button
          @click="handleRouter('view', scope.row)"
          :disabled="![40].includes(scope.row.batchStatus)"
          class="mr-20px"
          type="text"
          >查看</el-button
        >

        <el-button
          @click="handleDownload(scope.row)"
          :disabled="![40].includes(scope.row.batchStatus)"
          class="mr-20px"
          type="text"
          >下载</el-button
        >

        <el-button
          @click="handleTranslateStatus(scope.$index, scope.row)"
          :disabled="![40].includes(scope.row.batchStatus)"
          type="text"
          >{{
            scope.row.busiColumnsJson && scope.row.busiColumnsJson.markedValidBatch
              ? "取消推送"
              : "推送蓝图"
          }}</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <div class="pagination">
    <el-pagination
      :current-page="pagination.page"
      :page-size="pagination.size"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, prev, pager, next, jumper, sizes"
      :total="pagination.total"
      :background="'#4455FF'"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, ref, watch } from "vue";
import { useRouter } from "vue-router";
import moment from "moment";
import { ElMessage, ElMessageBox } from "element-plus";

import { useUserStore } from "@/store/useUserStore";
const pagination = ref({
  total: 0,
  size: 10, // 每页显示的条目数
  page: 1, // 当前页码
});
enum colorObj {
  "#1E90FF",
  "20" = "#f1db11f8",
  "30" = "#93158e",
  "40" = "#00a700",
  "50" = "#ff4a37",
}

enum statusObj {
  "20" = "未执行",
  "30" = "计算中",
  "40" = "完成",
  "50" = "失败",
}

const taskStatusOptions = [
  { label: "未执行", value: 20 },
  { label: "计算中", value: 30 },
  { label: "完成", value: 40 },
  { label: "失败", value: 50 },
];
const batchStatusList = ref(null);
const router = useRouter();
const store = useUserStore();

//defineProps
const props = defineProps({
  listData: {
    type: Object,
    default: () => {
      return [];
    },
  },

  role: {
    type: Array,
    default: () => {
      return [];
    },
  },

  applyList: {
    type: Object,
    default: () => {
      return [];
    },
  },

  searchData: {
    type: Object,
    default: () => {
      return null;
    },
  },

  taskData: {
    type: Object,
  },

  evaluationSample: {
    type: String,
  },

  id: {
    type: String,
  },
});
function handleSizeChange(size) {
  pagination.value.page = 1;
  pagination.value.size = size;
  emit("fetchTaskData", {
    ...pagination.value,
  });
}

const handleCurrentChange = (val: number) => {
  pagination.value.page = val;
  emit("fetchTaskData", {
    ...pagination.value,
  });
};

async function getBatchStatusList(dropDownType: number[]) {
  batchStatusList.value = dropDownType;
  pagination.value.page = 1;
  emit("fetchTaskData", {
    batchStatusList: dropDownType ? dropDownType.join(",") : null,
    page: 1,
  });
}

//watch teacher
watch(
  () => props.listData,
  () => {
    tableData.value = props.listData as any;
    pagination.value.total = props.searchData.total;
  }
);

const emit = defineEmits(["getPage", "getEnums", "fetchTaskData"]);
const tableData = ref([]);
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const handleRouter = (type, row) => {
  router.push({
    path: "/messageEvaluation/execute",
    query: {
      id: row.id,
      taskId: props.id,
      status: type,
      evaluationSample: props.evaluationSample,
      executeName: row.service,
      taskName: props.taskData!.taskName,
    },
  });
};

const handleDownload = async (row) => {
  const res = await $http.ajax({
    url: "/nbi/evaluate/taskBatch/downLoadDetails",
    params: {
      batchId: row.id,
      downLoadType: 1,
    },
    responseType: "blob",
  });

  if (res) {
    const blob = new Blob([res.data], {
      type: "application/vnd.ms-excel;charset=utf-8",
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = url;
    link.setAttribute("id", "downloadLink");
    link.setAttribute("download", `${props.taskData?.taskName}_${row.id}.xlsx`);
    document.body.appendChild(link);
    link.click();
    const objLink = document.getElementById("downloadLink");
    objLink && document.body.removeChild(objLink);
    ElMessage({
      type: "success",
      message: "导出成功",
    });
  }
};

const handleTranslateStatus = (index: number, row: any) => {
  const str =
    row.busiColumnsJson && row.busiColumnsJson.markedValidBatch
      ? "取消推送蓝图"
      : "推送蓝图";
  ElMessageBox.confirm(`确定要${str}展示吗?`, str, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    $http
      .ajax({
        url: "/nbi/evaluate/taskBatch/markDataStatus",
        method: "POST",
        data: {
          batchId: row.id,
        },
      })
      .then((res) => {
        if (res.msg) {
          ElMessage({
            type: "error",
            message: res.msg || "操作失败",
          });
          return;
        }
        ElMessage({
          type: "success",
          message:
            row.busiColumnsJson && row.busiColumnsJson.markedValidBatch
              ? "取消推送成功"
              : "推送成功",
        });
        emit("fetchTaskData");
      })
      .catch((e) => {
        ElMessage({
          type: "info",
          message: e || "操作失败",
        });
      });
  });
};
</script>

<style lang="less" scoped>
.id-text {
  color: #4455ff;
  cursor: pointer;
}

.pagination {
  display: flex;
  justify-content: end;
  margin-top: 40px;
}

.total {
  margin-left: 30px;
  font-size: 12px;
  font-weight: 500;
  color: #686464;
  margin-bottom: 10px;
}
</style>
