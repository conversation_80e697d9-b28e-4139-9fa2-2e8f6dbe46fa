<template>
  <div class="page-content flex search-content flex-justify-between">
    <div class="mb-20px" v-if="taskData">
      <div class="text-14px">任务信息：</div>
      <div class="flex items-center mt-10px mb-30px">
        <div class="flex items-center color-#666">
          <span class="w-[80px] min-w-[80px] whitespace-nowrap">任务名称：</span>

          <el-input
            @keyup.enter="submitTaskName"
            @blur="submitTaskName"
            v-model="taskName"
            placeholder=""
          />
        </div>
        <div class="color-#666 ml-30px">
          所属应用：
          <span class="color-dark font-600">
            {{
              applyList?.length &&
              taskData!.appId &&
              applyList.find((e) => e.appId === taskData?.appId)
                ?.appName
            }}
          </span>
        </div>
        <div class="ml-30px color-#666">
          对应服务：
          <span class="color-dark font-600">
            {{
              importList?.length &&
              taskData!.serviceId &&
              importList.find((e) => e.serviceId === taskData?.serviceId)
                ?.serviceName
            }}
          </span>
        </div>
        <div class="ml-30px color-#666 mr-20px">
          创建时间：
          <span class="color-dark font-600">{{
            moment(taskData!.createTime).format('YYYY-MM-DD HH:mm')
          }}</span>
        </div>
      </div>

      <div class="text-14px">执行参数：</div>
      <div class="mt-10px">
        <!-- <div class="color-#666">
          服务接口：
          <span class="color-dark font-600">{{ taskData.taskStatusName }}</span>
          <span v-if="taskData.taskStatus" class="color-dark font-600"
            >（ {{ taskData.finishedTaskCount }} /
            {{ taskData.totalTaskCount - taskData.abandonedCount }} ）</span
          >
        </div> -->
        <div class="color-#666 mt-10px flex items-start">
          <div class="w-[80px] min-w-[80px] whitespace-nowrap color-#666 mt-[4px]">
            评测样本：
          </div>

          <div class="flex flex-wrap gap-2">
            <el-tag
              v-for="item in taskData.tblConfValues.dataValueJson.anno_task_id &&
              Object.values(taskData.tblConfValues.dataValueJson.anno_task_id)"
              :key="item"
              type="success"
              size="small"
              >{{ item }}</el-tag
            >
          </div>
        </div>

        <div class="color-#666 mt-10px">当前仅支持仅3个月以内发生的通话进行评测</div>
      </div>
    </div>

    <div class="flex flex-col flex-justify-end">
      <el-button type="success" @click="toTaskAllocation"> 任务执行</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from "vue";
import moment from "moment";
import { ElMessage } from "element-plus";

import { useEnumsStore } from "@/store/useEnumsStore";
import { useUserStore } from "@/store/useUserStore";
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const taskName = ref("");
const applyList = ref<{ appId: string; appName: string; id: string }[]>([]);
const importList = ref<{ serviceId: string; serviceName: string }[]>([]);
const annoTaskList = ref<{ value: string; key: string }[]>([]);

const enumsStore = useEnumsStore();
const store = useUserStore();

onMounted(() => {
  Promise.all([getAppList(), getServiceList(), enumsStore.fetchAnnoTaskList()])
    .then((values) => {
      applyList.value = values[0] as [];
      importList.value = values[1] as [];
      annoTaskList.value = values[2] as [];
      taskName.value = props.taskData && props.taskData.taskName;
    })
    .catch((err) => {
      console.log(err);
    });
});

const getServiceList = async () => {
  const res = await $http.ajax({
    url: "/nbi/aiplat/svc/serviceIdMapping",
    method: "GET",
  });
  if (!res.code) {
    return res;
  } else {
    console.log("error");
  }
};

const props = defineProps({
  taskData: {
    type: Object,
  },
  id: {
    type: String,
  },
  role: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

//defineEmits
const emit = defineEmits(["onSubmit", "getEnums", "getTaskDetail"]);

async function submitTaskName() {
  if (!props.taskData.id) return;
  const res = await $http.ajax({
    url: "/nbi/evaluate/task/createOrUpdate",
    method: "POST",
    data: {
      taskName: taskName.value,
      id: props.taskData.taskId,
      appId: props.taskData.appId,
      serviceId: props.taskData.serviceId,
      strategyId: 7,
      taskType: 1,
      taskDesc: null,
      busiColumnsJson: props.taskData.busiColumnsJson,
      tblConfValues: {
        dataValueJson: props.taskData.tblConfValues.dataValueJson,
        strategyId: 7,
      },
    },
  });
  if (typeof res.success === "boolean" && !res.success) {
    ElMessage({
      message: "修改失败~",
      type: "error",
    });
    return;
  }
  ElMessage({
    message: "修改成功~",
    type: "success",
  });
  emit("getTaskDetail");
}

async function toTaskAllocation() {
  if (!props.taskData.taskId) return;
  const res = await $http.ajax({
    url: "/nbi/evaluate/taskBatch/createNewBatch",
    method: "POST",
    data: {
      taskId: props.taskData.taskId,
    },
  });

  if (res.code) {
    ElMessage({
      message: res.msg || "任务执行失败~",
      type: "error",
    });
    return;
  }
  ElMessage({
    message: "任务执行成功~",
    type: "success",
  });

  emit("onSubmit");
}
async function getAppList() {
  const res = await $http.ajax({
    url: "/nbi/aiplat/app/appIdMapping",
    method: "GET",
  });
  return res;
}
</script>
<style lang="less" scoped>
.el-select {
  width: 100%;
}

.search-btns {
  display: flex;
  justify-content: right;

  .el-button {
    width: 85px;
  }
}
</style>
