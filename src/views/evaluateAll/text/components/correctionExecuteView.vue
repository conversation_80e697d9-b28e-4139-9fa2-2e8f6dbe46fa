<template>
  <div>
    <el-breadcrumb style="margin: 10px 0 20px" :separator-icon="ArrowRight">
      <el-breadcrumb-item :to="{ path: '/evaluation/text' }">评测列表</el-breadcrumb-item>
      <el-breadcrumb-item
        :to="{
          path: '/textEvaluation/detail',
          query: { id: route.query && route.query.taskId },
        }"
        >{{ (route.query && route.query.taskName) || '_' }}</el-breadcrumb-item
      >

      <el-breadcrumb-item>{{ taskData && taskData?.id }}</el-breadcrumb-item>
    </el-breadcrumb>

    <div v-if="taskData" class="page-content" style="position: relative">
      <div class="mb-60px" v-if="taskData">
        <div class="text-14px mt-16px">执行信息：</div>
        <div class="flex items-center flex-wrap gap-20px justify-start mt-10px mb-30px">
          <div class="color-#666">
            执行ID：
            <span class="color-dark font-600">{{ taskData?.id }}</span>
          </div>
          <!-- <div class="color-#666">
            服务接口：
            <span class="color-dark font-600">{{
              taskData?.taskIdDisplay
            }}</span>
          </div> -->

          <div class="color-#666">
            执行状态：
            <span class="color-dark font-600">{{
              taskStatusOptions.find((e) => e.value === taskData.batchStatus)?.label
            }}</span>
          </div>
          <div class="color-#666 mr-20px">
            执行时间：
            <span class="color-dark font-600">{{ moment(taskData!.submitTime).format('YYYY-MM-DD HH:mm') }}</span>
          </div>

          <div class="color-#666 mr-20px">
            耗时：
            <span v-if="!taskData.timeCostMs" class="color-dark font-600">{{ taskData.timeCostMs }}秒</span>
            <span class="color-dark font-600" v-else-if="taskData.timeCostMs < 0">0秒</span>
            <span v-else class="color-dark font-600">
              {{
                taskData.timeCostMs / 1000 < 60
                  ? `${taskData.timeCostMs / 1000}秒`
                  : `${Math.floor(taskData.timeCostMs / 60000)}分${Math.round(
                      +((taskData.timeCostMs % 60000) / 1000).toFixed(1)
                    )}秒`
              }}
            </span>
          </div>

          <div class="color-#666">
            评测方法：
            <span class="color-dark font-600">
              {{ taskData.parentBusiColumnsJson && descItemsFormat(taskData.parentBusiColumnsJson.evalMethod) }}
            </span>
          </div>
        </div>

        <div class="text-14px">推送蓝图：</div>
        <div class="mt-10px mb-30px">
          <div class="color-#666">
            <div v-if="taskData.busiColumnsJson && taskData.busiColumnsJson.markedValidBatch">
              <span>已推送，推送版本号：</span>
              <span class="color-dark font-600">{{
                taskData.busiColumnsJson && taskData.busiColumnsJson.markedValidBatch
              }}</span>
            </div>
            <span v-else>未推送</span>
          </div>
        </div>
      </div>

      <div class="button-box">
        <el-button
          v-if="taskData.busiColumnsJson && taskData.busiColumnsJson.markedValidBatch"
          type="warning"
          @click="toExecute"
        >
          取消推送</el-button
        >
        <el-button v-else type="warning" @click="toExecute"> 推送蓝图</el-button>
      </div>
    </div>
  </div>

  <div class="page-content mt-20px" style="position: relative">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="评估报告" name="1"></el-tab-pane>
      <el-tab-pane label="样本明细" name="2"></el-tab-pane>
    </el-tabs>
    <div>
      <div v-if="activeName == 1">
        <template v-if="store.permissionCode.includes('23.81.508.509.510.511.512')">
          <div class="text-12px color-#666 mt-10px mb-5px">整体指标：</div>
          <el-table :data="tableWholeData" stripe style="width: 100%">
            <el-table-column
              v-for="column in tableWholeData && tableWholeData[0] && Object.keys(tableWholeData[0])"
              :key="column"
              :prop="column"
              :label="column"
            >
            </el-table-column>
          </el-table>

          <div class="text-12px color-#666 mt-50px mb-5px">分布统计：</div>

          <div class="flex w-[100%] flex-wrap justify-start">
            <div v-for="item in chartList" :key="item" class="w-[50%]">
              <EchartComponent :records="item" />
            </div>
          </div>
        </template>
        <template v-else>
          <NoPermission />
        </template>
      </div>

      <div v-if="activeName == 2">
        <template v-if="store.permissionCode.includes('23.81.508.509.510.511.513')">
          <SampleDetailTable :taskData="taskData" />
        </template>
        <template v-else>
          <NoPermission />
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import moment from 'moment';
import { ElMessage, ElMessageBox } from 'element-plus';
import EchartComponent from './echartComponent.vue';
import NoPermission from '@/views/401.vue';

import SampleDetailTable from './sampleDetailTable';
import { ArrowRight } from '@element-plus/icons-vue';
import type { TabsPaneContext } from 'element-plus';

import { useEnumsStore } from '@/store/useEnumsStore';
import { useUserStore } from '@/store/useUserStore';
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const store = useUserStore();
const enumsStore = useEnumsStore();
const route: any = useRoute();
const taskData = ref<any>({});
const annoTaskList = ref<{ value: string; key: string }[]>([]);
const tableWholeData = ref([]);
const chartList = ref([]);

const activeName = ref('1');
const taskStatusOptions = [
  { label: '未开始', value: 10 },
  { label: '进行中', value: 20 },
  { label: '准备中', value: 30 },
  { label: '成功', value: 40 },
  { label: '失败', value: 50 },
  { label: '正在停止', value: 60 },
  { label: '已停止', value: 70 },
];

const descItemsFormat = (method) => {
  let desc = null;
  const descObj = { humanScored: '人工评测', similarity: 'Similarity方法' };

  if (method) {
    for (const key in descObj) {
      if (key === method) {
        desc = descObj[key];
      }
    }
  }
  return desc;
};
const toExecute = () => {
  const str =
    taskData.value.busiColumnsJson && taskData.value.busiColumnsJson.markedValidBatch ? '取消推送蓝图' : '推送蓝图';
  ElMessageBox.confirm(`确定要${str}展示吗?`, str, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    $http
      .ajax({
        url: '/nbi/evaluate/taskBatch/markDataStatus',
        method: 'POST',
        data: {
          batchId: taskData.value.id,
        },
      })
      .then((res) => {
        if (res.msg) {
          ElMessage({
            type: 'error',
            message: res.msg || '操作失败',
          });
          return;
        }

        ElMessage({
          type: 'success',
          message:
            taskData.value.busiColumnsJson && taskData.value.busiColumnsJson.markedValidBatch
              ? '取消推送成功'
              : '推送成功',
        });
        fetchDetail();
      })
      .catch((e) => {
        ElMessage({
          type: 'error',
          message: e || '操作失败',
        });
      });
  });
};

onMounted(async () => {
  await fetchDetail();
  await fetchData();
  const res = await enumsStore.fetchAnnoTaskList();
  annoTaskList.value = res as any;
});

async function fetchDetail() {
  const batchId = route.query && route.query.id;
  const res = await $http.ajax({
    url: '/nbi/evaluate/taskBatch/getOneDetailByBatchId',
    params: {
      batchId,
    },
  });

  taskData.value = res;
}
async function fetchData() {
  const batchId = route.query && route.query.id;
  Promise.all([
    await $http.ajax({
      url: '/nbi/evaluate/evalResult/freeAnnoText/mainIndicator',
      params: {
        batchId,
      },
    }),
    await $http.ajax({
      url: '/nbi/evaluate/evalResult/freeAnnoText/distribution',
      params: {
        batchId,
      },
    }),
  ]).then((values) => {
    tableWholeData.value = values[0];
    console.log(values[1], 4555);
    chartList.value = values[1];
  });
}

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event);
};
</script>
<style>
.button-box {
  position: absolute;
  right: 20px;
  top: 20px;
}
</style>
