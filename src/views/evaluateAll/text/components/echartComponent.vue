<template>
  <div class="w-[100%] pr-[20px]">
    <div
      class="border-[1px] mr-[20px] mb-[30px] border-solid border-[#DCDFE6]"
      ref="chart"
      style="width: 100%; height: 400px"
    ></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, defineProps } from "vue";
import * as echarts from "echarts";

const records = ref([
  { key: "0-0.2", value: 0.61 },
  { key: "0.2-0.4", value: 0.45 },
  { key: "0.4-0.6", value: 0.5 },
  { key: "0.6-0.8", value: 0.55 },
  { key: "0.8-1.0", value: 0.65 },
]);

const props = defineProps({
  records: {
    type: Object,
    default: () => null,
  },
});

const chart = ref<HTMLElement | null>(null);

onMounted(() => {
  const { finaleMap, legend, title, xtag, ytag } = props.records;
  if (props.records && chart.value) {
    const myChart = echarts.init(chart.value);

    const options = {
      title: {
        text: title,
        textStyle: {
          fontSize: 16,
          fontWeight: 500,
        },
      },
      grid: {
        left: "3%",
        right: "70px",
        bottom: "3%",
        top: "20%",
        containLabel: true,
      },
      tooltip: {},
      legend: {
        data: [legend],
        icon: "circle",
        top: 40,
        itemHeight: 10,
        textStyle: {
          fontSize: 12,
          fontWeight: 400,
          color: "#999999",
        },
      },
      xAxis: {
        type: "category",
        data: Object.keys(finaleMap),
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: "#999999",
        },
        name: xtag, // 替换为你的X轴名称
        nameTextStyle: {
          // x 轴名称样式
          color: "#999999",
          fontSize: 12,
          fontWeight: 400,
        },
      },
      yAxis: {
        type: "value",
        name: ytag, // y 轴名称
        nameTextStyle: {
          // y 轴名称样式
          color: "#999999",
          fontSize: 12,
          fontWeight: 400,
        },
        axisLine: { show: false },
        axisTick: { show: false },
        splitNumber: 5,
        axisLabel: {
          color: "#999999",
        },
        splitLine: {
          lineStyle: {
            color: "#E4E4E4",
          },
        },
      },
      series: [
        {
          name: legend,
          type: "bar",
          data: Object.values(finaleMap),
          itemStyle: {
            color: "rgba(55, 106, 255, 0.85)",
          },
          barWidth: 14,
        },
      ],
    };

    myChart.setOption(options);

    // 监听数据变化以更新图表
    watch(records, () => {
      myChart.setOption({
        xAxis: {
          data: records.value.map((record) => record.key),
        },
        series: [
          {
            data: records.value.map((record) => record.value),
          },
        ],
      });
    });
  }
});
</script>
