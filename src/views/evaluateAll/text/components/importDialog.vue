<template>
  <div v-if="dialogFormVisible" @click="popoverHide">
    <el-dialog
      show-close
      align-center
      @click="popoverHide"
      class="importDialog"
      @close="resetForm(ruleFormRef)"
      :close-on-click-modal="false"
      :append-to-body="true"
      v-model="dialogFormVisible"
      title="创建评测任务-文本生成评测"
    >
      <div class="h-[80vh] overflow-y-scroll noscroll">
        <div class="text-[15px] font-medium mb-[10px]">基础信息</div>
        <el-form
          :model="form"
          ref="ruleFormRef"
          label-suffix="："
          :rules="rules"
          @close="resetForm(ruleFormRef)"
          label-width="130"
        >
          <el-form-item label="任务名称" prop="taskName">
            <el-input placeholder="请输入" :style="{ width: '100%' }" v-model="form.taskName" />
          </el-form-item>

          <div class="text-[15px] font-medium mb-[10px] mt-[30px]">评测配置</div>
          <el-form-item label="评测应用" prop="appId">
            <el-select
              @change="appIdChange"
              :style="{ width: '100%' }"
              v-model="form.appId"
              placeholder="请选择"
              filterable
            >
              <el-option v-for="(item, index) in applyList" :key="index" :label="item.appName" :value="item.appId" />
            </el-select>
          </el-form-item>

          <el-form-item label="评测服务" prop="serviceId">
            <el-select
              @change="serviceChange"
              :style="{ width: '100%' }"
              v-model="form.serviceId"
              placeholder="请选择"
              filterable
            >
              <el-option
                v-for="(item, index) in importList"
                :key="index"
                :label="item.serviceName"
                :value="item.serviceId"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="样本来源" prop="resource">
            <el-radio-group v-model="form.resource">
              <el-radio value="biaozhu">标注中心</el-radio>
              <el-radio disabled value="question">问题集</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="评测样本选择" prop="sampleId">
            <el-select
              :style="{ width: '100%' }"
              v-model="form.sampleId"
              placeholder="请选择"
              @change="sampleIdChange"
              multiple
            >
              <el-option
                v-for="(item, index) in annoTaskList"
                :key="index"
                :label="`${item.value}（${item.key}）`"
                :value="item.key"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="评测场景" prop="occasion">
            <el-radio-group v-model="form.occasion">
              <el-radio value="line">线上服务</el-radio>
            </el-radio-group>
          </el-form-item>

          <div class="text-[15px] font-medium mb-[10px] mt-[30px]">评测方法(单选)</div>
          <el-form-item label="人工评测" prop="method">
            <el-checkbox
              :model-value="Boolean(form.method === 'humanScored')"
              @change="(e) => changeCheckbox(e, 'humanScored')"
              class="checkbox-style"
            >
              <el-card
                style="width: 207px"
                :class="['h-[107px]', 'relative', Boolean(form.method === 'humanScored') ? 'check-active' : '']"
                shadow="always"
              >
                <div class="text-[14px] color-[#666666] mb-[10px] ml-[16px]">人工打分</div>
                <div class="text-[12px] color-[#666666] whitespace-break-spaces leading-[18px]">
                  基于人工标注的评分，计算统计评分。
                </div>
              </el-card>
            </el-checkbox>
          </el-form-item>

          <el-form-item label="相似度规则" prop="">
            <el-popover
              offset="-32"
              placement="top"
              @show="popoverShow"
              title="评测方法配置"
              :width="800"
              trigger="click"
            >
              <template #default>
                <PopoverComponent @all-selected="allSelected" :methodList="methodList" :checkboxObj="checkboxObj" />
              </template>
              <template #reference>
                <div class="relative">
                  <div class="text-[12px] z-66 cursor-pointer color-[#376AFF] right-[23px] bottom-[0px] absolute">
                    <div>
                      方法配置<el-icon size="12px"><ArrowDownBold /></el-icon>
                    </div>
                  </div>

                  <el-checkbox
                    :model-value="Boolean(form.method === 'similarity')"
                    @change="(e) => changeCheckbox(e, 'similarity')"
                    class="checkbox-style"
                    value="similarity"
                    name="similarity"
                  >
                    <el-card
                      style="width: 207px"
                      :class="['h-[107px]', 'relative', Boolean(form.method === 'similarity') ? 'check-active' : '']"
                      shadow="always"
                    >
                      <div class="text-[14px] color-[#666666] mb-[10px] ml-[16px]">Similarity方法</div>
                      <div class="text-[12px] color-[#666666] whitespace-break-spaces leading-[18px]">
                        将模型生成文本和参考文本对比，计算如下指标：相似度。
                      </div>
                    </el-card>
                  </el-checkbox>
                </div>
              </template>
            </el-popover>
            <el-checkbox disabled class="checkbox-style" value="Rouge" name="Rouge">
              <el-card class="bg-[#F6F6F7] h-[107px] relative w-[207px]" shadow="always">
                <div class="text-[12px] color-[#FF822A] right-[8px] top-[5px] absolute">暂未开放</div>

                <div class="text-[14px] color-[#666666] mb-[10px] ml-[16px]">Rouge规则打分</div>
                <div class="text-[12px] color-[#666666] whitespace-break-spaces leading-[18px]">
                  将模型生成结果和参考文本按n-gram拆分后，计算如下指标：召回率。
                </div>
              </el-card>
            </el-checkbox>

            <el-checkbox disabled class="checkbox-style" value="RAGAs" name="RAGAs">
              <el-card style="width: 207px" class="bg-[#F6F6F7] h-[107px] relative" shadow="always">
                <div class="text-[12px] color-[#FF822A] right-[8px] top-[5px] absolute">暂未开放</div>
                <div class="text-[14px] color-[#666666] mb-[10px] ml-[16px]">RAGAs评估</div>
                <div class="text-[12px] color-[#666666] whitespace-break-spaces leading-[18px]">
                  将正确回答、RAG生成的答案、知识内容分别对比，计算如下指标：忠实度、答案相关性、上下文精度、上下文召回率、上下文相关性。
                </div>
              </el-card>
            </el-checkbox>
          </el-form-item>

          <el-form-item label="模型判定" prop="">
            <el-checkbox disabled class="checkbox-style" value="Online activities" name="type">
              <el-card style="width: 207px" class="bg-[#F6F6F7] h-[107px] relative" shadow="always">
                <div class="text-[12px] color-[#FF822A] right-[8px] top-[5px] absolute">暂未开放</div>
                <div class="text-[14px] color-[#666666] mb-[10px] ml-[16px]">裁判模型判定</div>
                <div class="text-[12px] color-[#666666] whitespace-break-spaces leading-[18px]">
                  使用能力更强的大模型作为裁判员，对被评估模型的生成结果进行自动化打分。
                </div>
              </el-card>
            </el-checkbox>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitForm(ruleFormRef)"> 提 交 </el-button>
          <el-button @click="resetForm(ruleFormRef)">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, reactive, ref, defineExpose, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import PopoverComponent from './popoverComponent.vue';
import type { FormInstance, FormRules } from 'element-plus';
import { useEnumsStore } from '@/store/useEnumsStore';
import { useUserStore } from '@/store/useUserStore';
import { isEqual } from 'lodash-es';
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const dialogFormVisible = ref(false);
const serviceList = ref<{ value: string; key: string; appendix: string }[]>([]);
const annoTaskList = ref<{ value: string; key: string }[]>([]);
const methodList = ref([]);
const ruleFormRef = ref(null);
const checkboxObj = ref({});
const loading = ref(true);
const enumsStore = useEnumsStore();
const userStore = useUserStore();
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
};
const form = reactive({
  appId: null,
  assessmentScenes: null,
  taskName: null,
  taskType: 2,
  serviceId: null,
  sampleId: [],
  resource: 'biaozhu',
  occasion: 'line',
  method: 'humanScored',
});
const rules = reactive<FormRules>({
  appId: [{ required: true, message: '请选择所属应用' }],
  taskName: [{ required: true, message: '请输入任务名称' }],
  sampleId: [{ required: true, message: '请选择评测样本' }],
  serviceId: [{ required: true, message: '请选择对应服务' }],
  occasion: [{ required: true, message: '请选择评测场景' }],
  resource: [{ required: true, message: '请选择样本来源' }],
});
const props = defineProps({
  applyList: {
    type: Object,
  },
  importBatch: {
    type: Object,
  },

  importList: {
    type: Object,
  },
});
const emit = defineEmits(['fetchList']);
const popoverShow = async () => {
  getPopoverTableData();
};
function changeCheckbox(e, type) {
  if (type === 'humanScored') {
    checkboxObj.value = {};
  }
  methodList.value = [];
  if (!e) {
    form.method = null;
  }
  form.method = type;
}

function showRouge(type) {
  if (type === 'Rouge规则打分') {
  }
}
const appIdChange = (value) => {
  form.appId = value;
  form.sampleId = [];
  annoTaskList.value = [];
  methodList.value = [];
  checkboxObj.value = {};
  methodList.value = [];
  form.appId && form.serviceId && getAnnoTaskList();
};

const serviceChange = (value) => {
  form.serviceId = value;
  form.sampleId = [];
  annoTaskList.value = [];
  methodList.value = [];
  checkboxObj.value = {};
  methodList.value = [];
  form.appId && form.serviceId && getAnnoTaskList();
};

const sampleIdChange = (value) => {
  methodList.value = [];
  checkboxObj.value = {};
  methodList.value = [];
};

const allSelected = ({ type, value }) => {
  checkboxObj.value[type] = value;
};

async function getAnnoTaskList() {
  if (!form.serviceId) {
    return;
  }
  if (!form.serviceId) return;
  const id = props.applyList?.find((item) => item.appId === form.appId)?.appId;

  const serviceId = props.importList?.find((item) => item.serviceId === form.serviceId)?.serviceId;
  const res = await enumsStore.fetchAnnoTaskList(id, serviceId, '3');
  annoTaskList.value = res as any;
}
const getPopoverTableData = async () => {
  try {
    if (form.method !== 'similarity') {
      checkboxObj.value = {};
      // ElMessage.warning("请先选择similarity方法");
      return;
    }
    if (!form.sampleId.length) {
      // checkboxObj.value = {};
      // ElMessage.warning("请先选择评测样本");
      return;
    }
    loading.value = true;
    const new_anno_task_id = {};
    form.sampleId.forEach((e) => {
      new_anno_task_id[e] = annoTaskList.value.find((item) => item.key == e)?.value;
    });
    const res = await $http.ajax({
      url: '/nbi/evaluate/task/getEvalMethodDetailByTask',
      method: 'post',
      data: {
        strategyId: 8,
        taskType: 2,
        appId: form.appId,
        serviceId: form.serviceId,
        tblConfValues: {
          dataValueJson: {
            anno_task_id: {
              ...new_anno_task_id,
            },
          },
          strategyId: 8,
        },
      },
    });
    loading.value = false;
    if (!res || !res.sampleData || !res.sampleData.length) {
      throw new Error('没有相关数据');
    }

    methodList.value = res.sampleData;

    if (isEqual(Object.keys(checkboxObj.value), Object.keys(res.sampleData[0]))) {
      return;
    }

    for (const key in res.sampleData[0]) {
      checkboxObj.value[key] = false;
    }
  } catch (error) {
    loading.value = false;
    checkboxObj.value = {};
    methodList.value = [];
  }
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    await formEl.validate();
    if (
      form.method === 'similarity' &&
      !Object.entries(checkboxObj.value)
        .filter((item) => item[1] === true)
        .map((e) => e[0]).length
    ) {
      ElMessage.error('请先选择方法配置');
      return;
    }
    const new_method_data =
      form.method !== 'similarity'
        ? { evalMethod: form.method }
        : {
            evalMethod: form.method,
            evalMethodParam: [
              ...Object.entries(checkboxObj.value)
                .filter((item) => item[1] === true)
                .map((e) => e[0]),
            ],
          };
    const new_anno_task_id = {};
    form.sampleId.forEach((e) => {
      new_anno_task_id[e] = annoTaskList.value.find((item) => item.key == e)?.value;
    });
    const res = await $http.ajax({
      url: '/nbi/evaluate/task/createOrUpdate',
      method: 'POST',
      data: {
        taskName: form.taskName,
        appId: form.appId,
        serviceId: form.serviceId,
        strategyId: 8,
        taskType: 2,
        busiColumnsJson: {},
        tblConfValues: {
          dataValueJson: {
            anno_task_id: {
              ...new_anno_task_id,
            },
          },
          runDetailConfigJson: {
            params: {
              ...new_method_data,
            },
          },
          strategyId: 8,
        },
      },
    });
    if (res.code === -1) {
      ElMessage({
        message: res.msg || '网络异常，请重试~',
        type: 'warning',
      });
    } else {
      ElMessage({
        message: '操作成功~',
        type: 'success',
      });
      dialogFormVisible.value = false;
      setTimeout(() => {
        emit('fetchList');
      }, 1000);
    }
  } catch (error) {}
};

async function open(id) {
  form.appId = null;
  form.serviceId = null;
  form.taskName = null;
  form.sampleId = [];
  form.method = 'humanScored';
  checkboxObj.value = {};
  methodList.value = [];
  if (id) {
    const res = await $http.ajax({
      url: '/nbi/evaluate/task/getOneDetailByTaskId',
      method: 'get',
      params: {
        taskId: id,
      },
    });
    if (res) {
      form.appId = res.appId;
      form.serviceId = res.serviceId;
      form.appId && form.serviceId && (await getAnnoTaskList());
      form.taskName = res.taskName;
      form.taskType = res.taskType;

      form.sampleId = Object.keys(res.tblConfValues.dataValueJson.anno_task_id).map((key) => +key);

      const evalMethodParam = res.tblConfValues.runDetailConfigJson.params;
      form.method = evalMethodParam.evalMethod;
      if ('similarity' === evalMethodParam.evalMethod) {
        await getPopoverTableData();

        console.log(evalMethodParam.evalMethodParam, 112343);
        setTimeout(() => {
          evalMethodParam.evalMethodParam &&
            evalMethodParam.evalMethodParam.forEach((item) => {
              checkboxObj.value[item] = true;
            });
          console.log(evalMethodParam.evalMethodParam, 11, checkboxObj.value, 1111);
        }, 1000);
      }
    }
  }
  dialogFormVisible.value = true;
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  dialogFormVisible.value = false;
};

defineExpose({
  open,
});
</script>
<style lang="less">
.importDialog {
  width: 860px;
  .el-button--text {
    margin-right: 15px;
  }

  .el-select {
    width: 100%;
  }

  .dialog-footer button:first-child {
    margin-right: 10px;
  }

  .el-input__wrapper {
    width: 100%;
  }
}

.el-checkbox {
  margin-right: 15px !important;
}

.el-card__body {
  padding: 3px 3px !important;
}

.check-active {
  border: 1px solid #376aff;
}
.checkbox-style {
  height: 107px;
  position: relative;

  .el-checkbox__input {
    position: absolute;
    top: 1px;
    left: 8px;
    .el-checkbox__inner {
      width: 18px;
      height: 18px;

      &::after {
        left: 6px;
        top: 2px;
      }
    }
  }
}

.el-date-table__row {
  .ignore,
  .disabled {
    background: #ffffff;
  }
}
</style>
