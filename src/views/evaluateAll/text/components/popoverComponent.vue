<template>
  <div>
    <div>
      <div class="text-[12px] mb-[20px]">请选择需要对比的列（可选2列）</div>
      <div class="popover-content">
        <div>
          <el-table v-loading="props.loading" :data="methodList" height="400" style="width: 100%" class="">
            <template v-if="methodList && methodList.length" v-for="(value, key) in methodList[0]" :key="key">
              <el-table-column :prop="key" :label="key" :width="180">
                <template #header>
                  <div>
                    <el-checkbox
                      v-model="checkBoxList[key]"
                      :true-value="true"
                      :checked="false"
                      :false-value="false"
                      @change="(e) => toggleAll(e, key)"
                    >
                      <div>{{ key }}</div>
                    </el-checkbox>
                  </div>
                </template>
                <template #default="scope">
                  <el-tooltip :content="value" placement="top">
                    <div class="ellipsis">{{ value }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
            </template>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, defineEmits, defineExpose, onMounted } from 'vue';
import { ElPopover, ElIcon, ElTable, ElTableColumn, ElCheckbox } from 'element-plus';
import { ArrowDownBold } from '@element-plus/icons-vue';

const checkBoxList = ref({});
const emit = defineEmits(['all-selected', 'getTableData']);
const props = defineProps({
  methodList: {
    type: Object,
    default: () => [],
  },

  checkboxObj: {
    type: Object,
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
const popoverVisible = ref(false);

watch(props, (n, o) => {
  checkBoxList.value = props.checkboxObj;
});

const toggleAll = (value: boolean, type: string) => {
  if (Object.values(checkBoxList.value).filter((key) => key).length >= 3) {
    ElMessage.warning('最多可选两列');
    checkBoxList.value = { ...checkBoxList.value, [type]: false };
    emit('all-selected', { type, value: false });
    return;
  }
  checkBoxList.value[type] = value;
  emit('all-selected', { type, value });
};
</script>

<style scoped>
.popover-content {
  max-height: 600px;
  height: 400px;
  max-width: 800px;
}
</style>
