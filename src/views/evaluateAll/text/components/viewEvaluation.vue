<template>
  <div v-if="taskData">
    <el-breadcrumb style="margin: 10px 0 20px" :separator-icon="ArrowRight">
      <el-breadcrumb-item :to="{ path: '/evaluation/text' }">
        评测列表
      </el-breadcrumb-item>
      <el-breadcrumb-item>{{ taskData?.taskName }}</el-breadcrumb-item>
    </el-breadcrumb>

    <viewEvaluationSearch
      :taskData="taskData"
      :role="store.getUserRoleList"
      :id="route.query && route.query.id"
      @onSubmit="fetchData"
      @getEnums="getEnums"
      @getTaskDetail="getTaskDetail"
      @getEvaluationSample="getEvaluationSample"
    />

    <div class="page-content">
      <viewEvaluationList
        ref="viewTaskList"
        :taskData="taskData"
        :listData="listData"
        :searchData="searchData"
        :role="store.getUserRoleList"
        :annotationList="annotationList"
        :id="route.query && route.query.id"
        :evaluationSample="evaluationSample"
        @fetchTaskData="fetchTaskData"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";

import viewEvaluationSearch from "../components/viewEvaluationSearch.vue";
import viewEvaluationList from "./viewEvaluationList.vue";

import { ArrowRight } from "@element-plus/icons-vue";

import { useUserStore } from "@/store/useUserStore";
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http;
const listData = ref<any>();
const taskData = ref<any>();
const route: any = useRoute();
const hasList = ref(true);
const evaluationSample = ref<string>();
let viewTaskList = ref();
const searchData = ref({
  batchStatusList: null,
  page: 1,
  size: 10,
  total: 0,
});
const timer = ref(null);
const store = useUserStore();
const annotationList = ref<{ value: string; key: string }[]>([]);

onMounted(async () => {
  await getTaskDetail();
  await fetchData();

  timer.value = setInterval(() => {
    fetchData();
  }, 5000);
});

onUnmounted(() => {
  clearInterval(timer.value);
  timer.value = null;
});

function getEnums(pageData) {
  annotationList.value = pageData.annotationList;
}
function fetchTaskData(data) {
  searchData.value = { ...searchData.value, ...data };
  fetchData();
}
function getEvaluationSample(data) {
  evaluationSample.value = data;
}
async function fetchData() {
  const taskId = taskData.value.taskId;

  if (!taskId) return;
  const res = await $http.ajax({
    url: "/nbi/evaluate/taskBatch/page",
    method: "GET",
    params: {
      page: searchData.value.page,
      size: searchData.value.size,
      batchStatusList: searchData.value.batchStatusList,
      taskId,
    },
  });
  if (typeof res.success === "boolean" && !res.success) {
    ElMessage({
      message: "网络异常，请重试~",
      type: "warning",
    });
    return;
  }
  listData.value = res.list;
  searchData.value.total = res.total;
  return res;
}

async function getTaskDetail() {
  const annoTaskId = route.query && route.query.id;

  if (!annoTaskId) return;

  const res = await $http.ajax({
    url: "/nbi/evaluate/task/getOneDetailByTaskId",
    method: "GET",
    params: {
      taskId: annoTaskId,
    },
  });

  if (res.code) {
    ElMessage({
      message: "网络异常，请重试~",
      type: "warning",
    });
    return;
  }
  taskData.value = res;
  return res;
}
</script>
