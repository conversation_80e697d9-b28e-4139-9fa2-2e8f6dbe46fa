<template>
  <div></div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { useUserStore } from '@/store/useUserStore'

const router = useRouter()
const route: any = useRoute()
const store = useUserStore()

watch(
  () => {
    return store.token
  },
  (newVal, oldVal) => {
    init()
  }
)

async function init() {
  if (store.token && route.path === '/') {
    router.replace('/sample/importManage')
  }
}

onMounted(() => {
  init()
})
</script>
