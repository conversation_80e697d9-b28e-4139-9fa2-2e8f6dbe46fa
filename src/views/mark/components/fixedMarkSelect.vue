<template>
  <div class="max-h-200px overflow-y-auto">
    <div
      v-for="(item, index) in props.qualityList"
      :key="item.id"
      class="not-first:mt-3"
    >
      <div class="flex items-center">
        <div>{{ item.tagName }}</div>
        <el-popover
          placement="left"
          :effect="'dark'"
          :width="200"
          :show-after="200"
          trigger="hover"
        >
          <template #reference>
            <el-icon :size="15" class="ml-3"><QuestionFilled /></el-icon>
          </template>
          <template #default>
            <div class="text-sm">{{ item.tagDesc }}</div>
          </template>
        </el-popover>
      </div>
      <template v-if="[0, 6, 7, 8, 9, 10, 11, 12, 13].includes(item.itemType)">
        <template v-if="commonStore.INTERFACE.includes(item.itemType)">
          <template v-if="storeSelected[index] && options[item.itemId]">
            <el-select
              v-model="storeSelected[index].tagCode"
              :disabled="!labelable"
              class="mt-1"
              placeholder="请选择"
              size="default"
              @change="handleChange($event, index)"
              filterable
              remote
              remote-show-suffix
              :remote-method="($event) => remoteMethod($event, item)"
              :loading="loading"
            >
              <el-option
                v-for="option in options[item.itemId]"
                :key="option.value"
                :label="option.label"
                :value="`${option.value}`"
              >
              </el-option>
            </el-select>
          </template>
        </template>
        <template v-else>
          <template v-if="storeSelected[index] && options[item.itemId]">
            <el-select
              v-model="storeSelected[index].tagCode"
              :disabled="!labelable"
              class="mt-1"
              placeholder="请选择"
              size="default"
              @change="handleChange($event, index)"
            >
              <el-option
                v-for="option in options[item.itemId]"
                :key="option.value"
                :label="option.label"
                :value="`${option.value}`"
              >
              </el-option>
            </el-select>
          </template>
        </template>
      </template>
      <template v-else>
        <template v-if="storeSelected[index]">
          <el-input
            v-model="storeSelected[index].tagCode"
            :disabled="!labelable"
            class="mt-1"
            placeholder="请输入"
            size="default"
            @change="handleChange($event, index)"
          ></el-input>
        </template>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, toRaw } from 'vue'

import { getItemDropDownForAnno } from '@/api/tag'

import { TagsListResType } from '@/common/interface'

import { QuestionFilled } from '@element-plus/icons-vue'

import { useMarkOperation } from '@/hooks/useMarkOperation'

import { useCommonStore } from '@/store/useCommonStore'
import useFixedMarkStore from '@/store/useFixedMarkStore'
import useSelectOptions from '@/store/useSelectOptions'

const emit = defineEmits<{
  (e: 'update', data: object): void
}>()

const props = defineProps<{
  row: object
  rowIndex: number
  selectedValue?: []
  qualityList: TagsListResType[]
}>()

const commonStore = useCommonStore()

const labelable = useMarkOperation()
const optionsCacheStore = useSelectOptions()

const userAnnoDetailsStore = useFixedMarkStore()

const storeSelected = ref({})

const handleChange = (value: string, index: number) => {
  storeSelected.value[index].tagCode = value

  emit('update', {
    rowIndex: props.rowIndex,
    index,
    data: toRaw(storeSelected.value),
  })
}

const loading = ref(false)
const options = ref({})

const remoteMethod = async (str, item) => {
  let query = str.trim()
  if (query) {
    loading.value = true
    try {
      const res = await getItemDropDownForAnno(item.itemId, query)
      options.value[item.itemId] = res
    } catch (e) {
      options.value[item.itemId] = []
    } finally {
      loading.value = false
    }
  }
}

onMounted(async () => {
  const temp = userAnnoDetailsStore.getSelectedDataByRowIndex(props.rowIndex)
  if (Object.keys(temp).length) {
    storeSelected.value = temp
  } else {
    storeSelected.value = props.qualityList.map((item) => {
      return {
        tagId: '' + item.id,
        tagName: item.tagName,
        position: '' + props.rowIndex + '_' + item.id,
        positionType: 11,
        content: '',
        tagCode: '',
        uid: '',
        className: '',
        frontProperties: '',
      }
    })
    userAnnoDetailsStore.updateStore(
      String(props.rowIndex),
      Object.values(storeSelected.value)
    )
  }
  props.qualityList.map(async (item) => {
    if ([0, 6, 7, 8, 9, 10, 11, 12, 13].includes(item.itemType)) {
      options.value[item.itemId] = await optionsCacheStore.getOptionsByItemId(
        item.itemId
      )
    }
  })
})
</script>
