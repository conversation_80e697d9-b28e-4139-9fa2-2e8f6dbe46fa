<template>
  <div :id="props.id">{{ props.text }}</div>
</template>
<script setup>
import { defineExpose, defineProps, onBeforeUnmount, onMounted, ref } from 'vue'

import { useMarkOperation } from '@/hooks/useMarkOperation'

import useUnderlineMarkStore from '@/store/useUnderlineMarkStore'

const emit = defineEmits(['add'])

const props = defineProps({ text: String, id: String })

const underlineMarkStore = useUnderlineMarkStore()

const highlighter = ref()

const labelable = useMarkOperation()

const deleteHighlightedNode = (nodeId) => {
  highlighter.value.remove(nodeId)
}

onMounted(() => {
  const rowIndex = props.id.split('-')[0]
  const columnIndex = props.id.split('-')[1]

  let Highlighter = window.Highlighter

  highlighter.value = new Highlighter({
    $root: document.getElementById(props.id),
    rootDocument: window.__MICRO_APP_ENVIRONMENT__
      ? window.rawDocument
      : window.document,
    style: {
      className: 'highlight-mengshou-wrap',
    },
    verbose: true,
    window: window.__MICRO_APP_ENVIRONMENT__ ? window.rawWindow : window,
  })
  highlighter.value.run()

  if (labelable.value) {
    highlighter.value.on(Highlighter.event.CREATE, (data) => {
      const { sources, type } = data

      if (type !== 'from-store') {
        emit('add', { rowIndex, columnIndex, data: sources[0] })
      }
    })
    highlighter.value.on(Highlighter.event.CLICK, (id) => {
      console.log('id: ', id)
      // highlighter.addClass('highlight', id)
      // const doms = highlighter.getDoms([id])
      // console.log('doms: ', doms)
    })
  } else {
    highlighter.value.stop()
  }

  const existData = underlineMarkStore.getSquareData(rowIndex, columnIndex)
  if (existData) {
    for (let item of existData) {
      // highlighter.value.setOption({
      //   $root: document.getElementById(props.id),
      //   rootDocument: window.__MICRO_APP_ENVIRONMENT__
      //     ? window.rawWindow.document
      //     : window.document,
      //   style: {
      //     className: 'highlight-mengshou-wrap',
      //   },
      //   window: window.__MICRO_APP_ENVIRONMENT__ ? window.rawWindow : window,
      // })
      highlighter.value.fromStore(
        item.startMeta,
        item.endMeta,
        item.text,
        item.id
      )
    }
  }
})

onBeforeUnmount(() => {
  highlighter.value.dispose()
})

defineExpose({
  deleteHighlightedNode,
})
</script>
