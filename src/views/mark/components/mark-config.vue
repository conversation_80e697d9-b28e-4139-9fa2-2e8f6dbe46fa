<template>
  <div class="mark-config-head">
    <div
      v-if="type === 'list'"
      class="mark-config-head-index"
      :title="propsItem.uid"
    >
      {{ propsItem.index }}
    </div>
    <div class="mark-config-head-middle">
      <div class="mark-config-head-label">原文：</div>
      <div class="mark-config-head-text">
        {{ propsItem.text }}
      </div>
    </div>
    <div v-if="type === 'list'" class="mark-config-head-del">
      <el-button
        @click.stop.prevent="deleteByUid(propsItem.uid)"
        :disabled="markable"
        size="small"
        type="primary"
        circle
      >
        <el-icon><IEpDelete /></el-icon>
      </el-button>
    </div>
  </div>
  <div class="mark-config-props">
    <div class="mark-config-props-label">质检项：</div>
    <el-select
      :disabled="markable"
      v-model="propsItem.configKey"
      class="mark-config-props-select"
      placeholder="请选择"
      size="default"
      :filterable="true"
      @change="configItemChange($event, propsItem, 'key')"
    >
      <el-option
        v-for="item in qualityList"
        :key="item.id"
        :disabled="item.status === 1"
        :label="item.tagName"
        :value="`${item.id}`"
      >
        <el-popover
          placement="left"
          title=""
          effect="dark"
          :width="200"
          :show-after="200"
          trigger="hover"
        >
          <template #reference>
            <div>
              {{ item.tagName }}
              {{ item.status === 1 ? `(无效)` : '' }}
            </div>
          </template>
          <template #default>
            质检项说明：
            {{ item.tagDesc }} <br />
            典型案例：
            {{ item.typicalSample }}
          </template>
        </el-popover>
      </el-option>
    </el-select>
  </div>
  <div class="mark-config-props">
    <div class="mark-config-props-label">对应值：</div>
    <el-input
      :disabled="markable"
      class="!w-100%"
      v-if="data.itemType === 1"
      v-model="propsItem.configValue"
      @change="configItemChange($event, propsItem, 'value')"
      placeholder="请输入"
    />
    <el-input-number
      :disabled="markable"
      class="!w-100%"
      :controls="false"
      v-else-if="data.itemType === 2"
      v-model="propsItem.configValue"
      @change="configItemChange($event, propsItem, 'value')"
      placeholder="请输入数字"
    />
    <el-select
      :disabled="markable"
      v-else-if="commonStore.INTERFACE.includes(data.itemType)"
      v-model="propsItem.configValue"
      class="mark-config-props-select"
      placeholder="请输入关键词搜索"
      size="default"
      filterable
      remote
      remote-show-suffix
      :persistent="data.isPersistent"
      :remote-method="remoteMethod"
      :loading="data.loading"
      @change="configItemChange($event, propsItem, 'value')"
    >
      <el-option
        v-for="item in data.remoteList"
        :key="item.key"
        :label="item.value"
        :value="`${item.key}`"
      />
    </el-select>
    <el-select
      :disabled="markable"
      v-else
      v-model="propsItem.configValue"
      class="mark-config-props-select"
      placeholder="请选择"
      size="default"
      :persistent="data.isPersistent"
      :filterable="true"
      @change="configItemChange($event, propsItem, 'value')"
    >
      <el-option
        v-for="item in data.qualityItemList"
        :key="item.key"
        :label="item.value"
        :value="`${item.key}`"
      />
    </el-select>
  </div>
  <div v-if="type === 'list'" class="flex justify-end">
    <el-popover
      v-if="Route.query.status !== 'approve' && item.auditCommentInfo?.comment"
      trigger="click"
    >
      <template #reference>
        <el-button type="text">查看批注</el-button>
      </template>
      <div>
        <div>{{ item.auditCommentInfo?.comment }}</div>
        <div>批注人：{{ item.auditCommentInfo?.commentUserName }}</div>
      </div>
    </el-popover>
    <div v-if="Route.query.status === 'approve'">
      <el-button type="text" @click="handleOpenCommentDialog">批注</el-button>
      <el-popconfirm
        :title="`确认${
          propsItem.auditCommentInfo?.isTypicalSample ? '删除' : '添加'
        }样例？`"
        @confirm="handleTypicalSample"
      >
        <template #reference>
          <el-button type="text">
            {{
              propsItem.auditCommentInfo?.isTypicalSample
                ? '已添加'
                : '添加为样例'
            }}
          </el-button>
        </template>
      </el-popconfirm>
    </div>
  </div>
  <el-dialog v-model="addCommentDialog" width="400" :show-close="false">
    <el-input
      v-model="comment"
      type="textarea"
      :rows="4"
      placeholder="请输入批注"
    ></el-input>
    <template #footer>
      <div>
        <el-button type="primary" @click="handleComment">确认</el-button>
        <el-button @click="handleCommentClose">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  computed,
  inject,
  nextTick,
  onMounted,
  reactive,
  ref,
  toRefs,
  watch,
} from 'vue'
import { useRoute } from 'vue-router'

import { getItemDropDownForAnno } from '@/api/tag'

import { sleep } from '@/common/helper'
import { SelectListItemType, TagsListResType } from '@/common/interface'

import type { markConfigItemType } from '@/hooks/JsMark/common/interface'
import { setLocal } from '@/hooks/JsMark/common/localStorage'
import useAnnotation from '@/hooks/JsMark/useAnnotation'
import useMark from '@/hooks/JsMark/useMark'

import { useCommonStore } from '@/store/useCommonStore'
import { useConfigStore } from '@/store/useConfigStore'
import { useUserStore } from '@/store/useUserStore'

const userStore = useUserStore()
const Route = useRoute()
const props = withDefaults(
  defineProps<{
    item: markConfigItemType
    index: number
    type: 'menu' | 'list'
    markable: boolean
  }>(),
  {
    index: 0,
    type: 'list',
    markable: false,
  }
)

const emits = defineEmits(['update'])

// console.log('init: ', props.item.auditCommentInfo)
const addCommentDialog = ref(false)
const comment = ref(props.item.auditCommentInfo?.comment)
// eslint-disable-next-line vue/no-dupe-keys
const { item: propsItem, markable } = toRefs(props)

const { settings, config } = inject('mark-config') as {
  settings: {
    activeUid: string
  }
  config: {
    value: string
    list: markConfigItemType[]
    data: string
    invalids: Set<string>
  }
}

const { addAntRecords, deleteAnnotation } = useAnnotation()
const { search } = useMark()
let store = useConfigStore()
const commonStore = useCommonStore()

const data = reactive({
  qualityList: [] as TagsListResType[],
  qualityItemList: [] as SelectListItemType[],
  remoteList: [] as SelectListItemType[],
  isPersistent: true,
  itemType: 0,
  itemId: 0 as string | number,
  loading: false,
})

let qualityList = computed(() => store.getQualityList)
let qualityMap = computed(() => store.getQualityMap)

watch(
  () => propsItem.value.configKey,
  async (val, prevVal) => {
    console.log(val)
    if (!commonStore.INTERFACE.includes(data.itemType)) {
      getItemList()
    }
  }
)

async function getItemList() {
  let key = propsItem.value.configKey
  if (key) {
    let { itemType, itemId } = qualityMap.value?.[key] as TagsListResType
    data.itemType = itemType
    data.itemId = itemId
    if ([1, 2].includes(data.itemType)) return
    if (commonStore.INTERFACE.includes(data.itemType)) {
      console.log(propsItem.value)
      let userInput = propsItem.value.tagName || propsItem.value.configValue
      await remoteMethod(userInput as string)
    } else {
      data.qualityItemList = await store.getQualityItemList(itemId)
    }
    console.log(propsItem.value)
    let { configKey, configValue, tagName } = propsItem.value
    let k = `${configKey}-${configValue}`
    console.log(k, tagName)
    store.setItemCodeMap(`${configKey}-${configValue}`, tagName as string)
    // console.log(data.qualityItemList)
    // console.log(store.itemCodeMap)
  }
}

function editConfig(uid, data) {
  addAntRecords({
    uid,
    desc: data,
  })
  setLocal({ uid, desc: data })
}

const configItemChange = (val, item: markConfigItemType, type) => {
  let { uid, configKey, configValue } = item
  // console.log(uid, type)
  console.log(configKey, configValue)
  editConfig(uid, {
    configKey,
    configValue,
  })
  if (`${configKey}`.trim() && `${configValue}`.trim()) {
    config.invalids.delete(uid)
  } else {
    if (!config.invalids.has(uid)) {
      config.invalids.add(uid)
    }
  }
  if (type === 'key') {
    propsItem.value.configValue = ''

    if (val) {
      let { itemType, itemId } = qualityMap.value?.[val] as TagsListResType
      data.itemType = itemType
      data.itemId = itemId
      console.log(itemType)
    }
  }
  if (type === 'value') {
    if ([1, 2].includes(data.itemType)) {
      propsItem.value.tagName = propsItem.value.configValue
    } else {
      let arr: SelectListItemType[] = []
      if (commonStore.INTERFACE.includes(data.itemType)) {
        arr = [...data.remoteList]
      } else {
        // data.qualityItemList = []
        arr = [...data.qualityItemList]
      }
      let item = arr.find((item) => item.key === propsItem.value.configValue)
      propsItem.value.tagName = item?.value || ''
    }
    let { configKey, configValue, tagName } = propsItem.value
    console.log(propsItem.value)
    store.setItemCodeMap(`${configKey}-${configValue}`, tagName as string)
  }
  // data.isPersistent && (data.isPersistent = false)
}

async function remoteMethod(str: string) {
  let query = str.trim()
  if (query) {
    data.loading = true
    try {
      const res = await getItemDropDownForAnno(data.itemId, query)
      data.remoteList = res || []
    } catch (e) {
      console.log(e)
      data.remoteList = []
    } finally {
      data.loading = false
    }
  } else {
    data.remoteList = []
  }
}

function deleteByUid(uid: string) {
  deleteAnnotation(uid)
  let i = 0
  config.list = config.list.reduce((prev, item, index) => {
    if (item.uid !== uid) {
      prev.push({
        ...item,
        index: ++i,
      })
    }
    return prev
  }, [] as markConfigItemType[])
}

async function handleTypicalSample() {
  propsItem.value.auditCommentInfo.isTypicalSample =
    !propsItem.value.auditCommentInfo.isTypicalSample

  emits('update', {
    index: props.index,
    auditCommentInfo: propsItem.value.auditCommentInfo,
  })
}

async function handleComment() {
  addCommentDialog.value = false
  propsItem.value.auditCommentInfo.comment = comment.value
  propsItem.value.auditCommentInfo.commentUserName = userStore.getUserName
  emits('update', {
    index: props.index,
    auditCommentInfo: propsItem.value.auditCommentInfo,
  })
}

function handleCommentClose() {
  addCommentDialog.value = false
  comment.value = propsItem.value.auditCommentInfo?.comment
}

function handleOpenCommentDialog() {
  addCommentDialog.value = true
}

onMounted(async () => {
  nextTick(async () => {
    await sleep(300)
    getItemList()
  })
})
</script>
