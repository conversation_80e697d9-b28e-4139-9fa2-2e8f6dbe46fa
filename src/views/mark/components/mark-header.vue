<template>
  <div class="bg-#fff bg-white page-header p-10px pl-20px mb-10px text-14px">
    <MarkNav :nav-list="navList" />
    <div class="flex mt-20px">
      <div class="color-#666">
        样本ID：
        <span class="color-dark font-600">{{ data.sampleBusiId }}</span>
      </div>
      <div class="ml-20px color-#666">
        样本类型：
        <span class="color-dark font-600">{{
          SampleTypeEnum[data.sampleType]
        }}</span>
      </div>
      <div class="ml-20px color-#666">
        标注状态：
        <span class="color-dark font-600">{{ data.annoStatusName }}</span>
      </div>
      <div class="ml-20px color-#666">
        标注人：
        <span class="color-dark font-600">{{ data.annotatorName }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

import MarkNav from '@/views/mark/components/mark-nav.vue'

import { SampleTypeEnum } from '@/common/constants'

const Route = useRoute()

const props = withDefaults(
  defineProps<{
    data: {
      sampleBusiId: string
      sampleType: number
      annoStatusName: string
      annotatorName: string
    }
  }>(),
  {}
)

let navList = computed(() => {
  let { page, taskName, taskId } = Route.query || {}
  let paths: {
    path: string
    text: string
  }[] = []
  if (page === 'viewTask') {
    paths = [
      {
        path: '/task/taskManage',
        text: '任务列表',
      },
      {
        path: `/task/viewTask?id=${taskId}`,
        text: taskName as string,
      },
      {
        path: '',
        text: '样本标注页',
      },
    ]
  }
  if (page === 'approve') {
    paths = [
      {
        path: '/sample-approval-list',
        text: '样本审批',
      },
      {
        path: '',
        text: '样本审批页',
      },
    ]
  }
  return paths
})
</script>
