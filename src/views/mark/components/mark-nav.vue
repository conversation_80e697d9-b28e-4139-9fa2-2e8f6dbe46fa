<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item
      v-for="item in props.navList"
      :key="item.text"
      :to="item.path || ''"
      >{{ item.text }}</el-breadcrumb-item
    >
  </el-breadcrumb>
</template>

<script setup lang="ts">
interface NavType {
  path: string
  text: string
}
const props = withDefaults(
  defineProps<{
    navList: NavType[]
  }>(),
  {}
)
</script>
