<template>
  <div
    class="flex absolute bg-white z-10 p-10px w-100% bottom-0 left-0 border-t-1px border-solid border-#ddd flex-space-between"
  >
    <div>
      <el-button
        v-if="
          routeInfo.status === 'update' &&
          routeInfo.annoStatus !== '3' &&
          routeInfo.annoStatus !== '-2'
        "
        type="danger"
        @click="abandonAnAnnoApi"
        >废弃</el-button
      >
    </div>
    <div>
      <template
        v-if="routeInfo.status === 'approve' && routeInfo.viewMode === 'audit'"
      >
        <el-button type="primary" size="default" @click="saveAnAnnoApi('')">
          保存
        </el-button>
        <el-button type="primary" size="default" @click="passAnAnnoApi">
          通过
        </el-button>
        <el-button type="danger" size="default" @click="rejectAnAnnoApi">
          驳回
        </el-button>
      </template>
      <template
        v-if="routeInfo.status === 'update' && routeInfo.viewMode === 'anno'"
      >
        <el-button
          v-if="markable"
          type="success"
          size="default"
          @click="saveAnAnnoApi('')"
        >
          保存
        </el-button>
        <el-button
          v-if="routeInfo.annoStatus === '2'"
          type="primary"
          @click="continueAnnoHandler"
          >继续标注</el-button
        >
        <el-button v-if="markable" type="primary" @click="submitAuditApi"
          >提交审核</el-button
        >
      </template>
      <el-button type="warning" @click="getPreviousDetailWithConfirm">
        上一条
      </el-button>
      <el-button type="warning" @click="getNextDetailWithConfirm">
        下一条
      </el-button>
      <el-button type="info" @click="back">返回</el-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'

import {
  abandonAnAnno,
  continueAnno,
  saveAnAnno,
  submitAudit,
} from '@/api/mark'
import { passAnAnno, rejectAnAnno } from '@/api/tag'

import { useMarkOperation } from '@/hooks/useMarkOperation'

import useFixedMarkStore from '@/store/useFixedMarkStore'
import useUnderlineMarkStore from '@/store/useUnderlineMarkStore'

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator(value) {
      return ['fixed', 'underline'].includes(value)
    },
  },
})

const router = useRouter()
const route = useRoute()
const routeInfo = computed(() => route.query)

let operationStore
if (props.type === 'fixed') {
  operationStore = useFixedMarkStore()
} else {
  operationStore = useUnderlineMarkStore()
}

const emits = defineEmits(['previous', 'next'])

const skipSave = computed(() => {
  return (
    routeInfo.value.status === 'view' ||
    (routeInfo.value.status === 'update' && routeInfo.value.annoStatus === '-2')
  )
})

const markable = useMarkOperation()

const abandonAnAnnoApi = () => {
  ElMessageBox.confirm('确定要废弃这条样本吗？', '提示', {
    type: 'warning',
    message: '确定要废弃这条样本吗？',
  })
    .then(async () => {
      try {
        let res = await abandonAnAnno(routeInfo.value.id)
        if (res === 'OK') {
          await router
            .replace({
              path: route.path,
              query: {
                id: routeInfo.value.id,
                taskId: route.query.taskId,
                status: route.query.status,
                viewMode: route.query.viewMode,
                page: 'viewTask',
                taskName: route.query.taskName,
                annoStatus: '-2',
                condition: routeInfo.value.condition,
              },
            })
            .then(() => {
              // router.go(0)
              refresh()
            })
        } else {
          ElMessage.warning(res.msg)
        }
      } catch (e) {
        console.log(e)
      }
    })
    .catch(() => {
      console.log('取消')
    })
}

const saveAnAnnoApi = async (msg = '保存成功！') => {
  if (operationStore.validateData()) {
    const loading = ElLoading.service({
      lock: true,
      text: '保存中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    try {
      console.log('saveAnAnnoApi', routeInfo.value)
      if (
        routeInfo.value.viewMode === 'anno' &&
        routeInfo.value.annoStatus === '2'
      )
        return true
      let res = await saveAnAnno({
        userAnnoId: routeInfo.value.id,
        userAnnoDetails: operationStore.getFormatData(),
        msg,
      })
      if (res === 'OK') {
        return true
      } else {
        ElMessage.warning(res.msg)
        return false
      }
    } catch (e) {
      console.log(e)
      return false
    } finally {
      loading.close()
    }
  } else {
    ElMessage.warning('请检查标注项')
    return false
  }
}

const continueAnnoHandler = async () => {
  try {
    let res = await continueAnno(routeInfo.value.id)
    if (res === 'OK') {
      // 成功
      await router
        .replace({
          path: route.path,
          query: {
            id: routeInfo.value.id,
            taskId: route.query.taskId,
            status: 'update',
            viewMode: 'anno',
            page: 'viewTask',
            taskName: route.query.taskName,
            annoStatus: '1',
            condition: routeInfo.value.condition,
          },
        })
        .then(() => {
          refresh()
        })
    } else {
      ElMessage.warning(res.msg)
    }
  } catch (e) {
    console.log(e)
  }
}

const submitAuditApi = async () => {
  try {
    if (await saveAnAnnoApi('no')) {
      const res = await submitAudit(routeInfo.value.id)
      if (res === 'OK') {
        await router
          .replace({
            path: route.path,
            query: {
              id: routeInfo.value.id,
              taskId: route.query.taskId,
              status: 'update',
              viewMode: 'anno',
              page: 'viewTask',
              taskName: route.query.taskName,
              annoStatus: '2',
              condition: routeInfo.value.condition,
            },
          })
          .then(() => {
            // router.go(0)
            refresh()
          })
      } else {
        ElMessage.warning(res.msg)
      }
    }
  } catch (e) {
    console.log(e)
  }
}

const passAnAnnoApi = async () => {
  try {
    if (await saveAnAnnoApi('')) {
      let res = await passAnAnno([routeInfo.value.id])
      if (res === 'OK') {
        await router
          .replace({
            path: route.path,
            query: {
              id: routeInfo.value.id,
              taskId: route.query.taskId,
              status: 'approve',
              viewMode: 'audit',
              page: 'approve',
              taskName: route.query.taskName,
              annoStatus: '3',
              condition: routeInfo.value.condition,
            },
          })
          .then(() => {
            refresh()
          })
      } else {
        ElMessage.warning(res.msg)
      }
    }
  } catch (e) {
    console.log(e)
  }
}

const rejectAnAnnoApi = async () => {
  try {
    if (await saveAnAnnoApi()) {
      let res = await rejectAnAnno([routeInfo.value.id])
      if (res === 'OK') {
        await router
          .replace({
            path: route.path,
            query: {
              id: routeInfo.value.id,
              taskId: route.query.taskId,
              status: 'approve',
              viewMode: 'audit',
              page: 'approve',
              taskName: route.query.taskName,
              annoStatus: '4',
              condition: routeInfo.value.condition,
            },
          })
          .then(() => {
            // router.go(0)
            refresh()
          })
      } else {
        ElMessage.warning(res.msg)
      }
    }
  } catch (e) {
    console.log(e)
  }
}

const back = async () => {
  if (skipSave.value) {
    operationStore.clearData()
    router.go(-1)
  } else {
    if (await saveAnAnnoApi()) {
      operationStore.clearData()
      router.go(-1)
    }
  }
}

const getPreviousDetailWithConfirm = async () => {
  if (skipSave.value) {
    emits('previous', {
      page: routeInfo.value.page,
      viewMode: routeInfo.value.viewMode,
      status: routeInfo.value.status,
    })
  } else {
    if (await saveAnAnnoApi()) {
      emits('previous', {
        page: routeInfo.value.page,
        viewMode: routeInfo.value.viewMode,
        status: routeInfo.value.status,
      })
    }
  }
}
const getNextDetailWithConfirm = async () => {
  if (skipSave.value) {
    emits('next', {
      page: routeInfo.value.page,
      viewMode: routeInfo.value.viewMode,
      status: routeInfo.value.status,
    })
  } else {
    if (await saveAnAnnoApi()) {
      emits('next', {
        page: routeInfo.value.page,
        viewMode: routeInfo.value.viewMode,
        status: routeInfo.value.status,
      })
    }
  }
}

const refresh = () => {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    window.rawWindow.location.reload()
  } else {
    window.location.reload()
  }
}
</script>
