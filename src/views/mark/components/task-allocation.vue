<template>
  <el-dialog
    class="taskAllocation"
    v-model="dialogData.show"
    title="人员分配"
    width="640"
    :before-close="handleClose"
  >
    <div class="pl-20% pr-20%">
      <div class="text-16px font-bold mb-15px">任务概况</div>
      <div v-if="annoTaskAssignData" class="text-14px mb-10px color-#333">
        任务剩余样本量：
        <span class="font-bold"
          >{{ annoTaskAssignData.unassignedTaskSampleCount }}条</span
        >
      </div>
      <div class="text-14px color-#333">人员标注进度：</div>
      <el-table
        :data="userAnnoTaskStaticList"
        border
        show-summary
        style="width: 100%; margin-top: 20px"
      >
        <el-table-column prop="userName" label="姓名" />
        <el-table-column prop="unFinishedSampleCount" label="待完成" />
        <el-table-column prop="finishedSampleCount" label="已完成" />
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-popconfirm
              title="回收待标注、标注中样本，是否确认回收？"
              @confirm="handleRecycle(row)"
            >
              <template #reference>
                <el-button
                  link
                  type="primary"
                  :disabled="row.unFinishedSampleCount === 0"
                  >回收
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div class="text-16px font-bold mb-15px mt-15px">样本分配</div>
      <el-form ref="ruleFormRef" :rules="rules" :model="form">
        <el-form-item label="标注人:" prop="userDomains">
          <el-select
            v-model="form.userDomains"
            placeholder="请选择"
            class="w-100%"
            multiple
            filterable
          >
            <el-option
              v-for="(item, index) in annoTaskIdList"
              :key="index"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分配量:" prop="sampleCount">
          <el-input v-model="form.sampleCount" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submit"> 提交 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getCurrentInstance, onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'

import type { FormInstance, FormRules } from 'element-plus'
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http
let dialogData = reactive({
  show: false,
})
let domainOrNameInVague = ref(null)
let annoTaskIdList = ref<{ value: string; key: string }[]>([])
const annoTaskAssignData = ref()
const userAnnoTaskStaticList = ref([])
const ruleFormRef = ref<FormInstance>()

let form = reactive({
  annoTaskId: '',
  sampleCount: '',
  userDomains: [],
})

const validateSampleCount = (rule: any, value: any, callback: any) => {
  console.log(+value, 55)
  if (!+value) {
    callback(new Error('分配量必填,且必须大于0'))
  }

  if (!RegExp('^[0-9]*$').test(value)) {
    callback(new Error('分配量必须大于0且小于等于剩余样本量'))
  }

  if (+value > +annoTaskAssignData.value.unassignedTaskSampleCount) {
    callback(new Error('分配量必须大于0且小于等于剩余样本量'))
  }
  callback()
}

const rules = reactive<FormRules>({
  userDomains: [{ required: true, message: '标注人必填' }],
  sampleCount: [
    { required: true, message: '分配量必填' },
    { validator: validateSampleCount },
  ],
})

defineExpose({ dialogData, getAnnoTaskAssignData })

onMounted(() => {
  getAnnoSysUsersList()
})

const props = defineProps({
  taskData: {
    type: Object,
  },
})

const emit = defineEmits<{
  (e: 'change', status: boolean): void
  // (e: 'update', value: string): void
}>()

function handleClose() {
  ruleFormRef.value!.resetFields()
  dialogData.show = false
}

async function submit() {
  try {
    await ruleFormRef.value!.validate()
    const res = await $http.ajax({
      url: '/nbi/anno/annoTask/annoTaskAssign',
      method: 'POST',
      data: {
        ...form,
        annoTaskId: props.taskData?.id,
      },
    })
    if (!res.code) {
      // ElMessage({
      //   message: '操作成功~',
      //   type: 'success',
      // })
      handleClose()
      emit('change', true)
    } else {
      ElMessage({
        message: res.msg || '网络异常，请重试~',
        type: 'warning',
      })
    }
  } catch (e) {
    console.log(e)
  }
}

async function getAnnoSysUsersList() {
  const res = await $http.ajax({
    url: '/nbi/anno/annoTask/getAnnoSysUsers',
    method: 'GET',
    params: { domainOrNameInVague: domainOrNameInVague.value },
  })

  annoTaskIdList.value = res
  return res
}

async function getAnnoTaskAssignData() {
  const res = await $http.ajax({
    url: '/nbi/anno/annoTask/getAnnoTaskAssign',
    method: 'GET',
    params: { annoTaskId: props.taskData!.id },
  })

  annoTaskAssignData.value = res
  userAnnoTaskStaticList.value = annoTaskAssignData.value.userAnnoTaskStaticList
  return res
}

async function handleRecycle(row) {
  const res = await $http.ajax({
    url: '/nbi/anno/annoTask/recollectTaskAssign',
    method: 'POST',
    data: {
      annoTaskId: props.taskData!.id,
      userDomain: row.userDomain,
    },
  })
  if (res === 'OK') {
    getAnnoTaskAssignData()
  } else {
    ElMessage({
      message: res.msg,
      type: 'warning',
    })
  }
}

interface Product {
  userName: string
  unFinishedSampleCount: number
  finishedSampleCount: number
  unassignedTaskSampleCount: number
}
</script>
<style lang="less" scoped>
.taskAllocation {
  .el-select__tags {
    height: 30px;
    overflow-x: hidden;
    overflow-y: scroll;
  }
}
</style>
