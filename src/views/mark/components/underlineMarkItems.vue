<template>
  <!--  <template v-if="props.tagShowMethod === 0">-->
  <div class="overflow-y-auto">
    <div
      v-for="(item, index) in highlightedItems"
      :key="item.id"
      class="mt-2 is-first:mt-0"
    >
      <div class="mark-config-head">
        <div class="mark-config-head-index">{{ index + 1 }}</div>
        <div class="mark-config-head-middle">
          <div class="mark-config-head-label">原文：</div>
          <div
            class="w-100px overflow-hidden text-14px font-bold text-ellipsis whitespace-nowrap"
          >
            {{ item.text }}
          </div>
        </div>
        <div class="">
          <el-button
            @click.stop.prevent="
              handleDelete(item.columnIndex, item.id, item.oldIndex)
            "
            :disabled="!labelable"
            size="small"
            type="primary"
            circle
          >
            <el-icon><IEpDelete /></el-icon>
          </el-button>
        </div>
      </div>
      <div class="flex mt-15px items-center">
        <div class="mark-config-props-label">质检项：</div>
        <el-select
          class="mark-config-props-select"
          placeholder="请选择"
          size="default"
          v-model="item.tagId"
          @change="handleChange($event, item)"
          :disabled="!labelable"
        >
          <el-option
            v-for="option in props.qualityList"
            :key="option.itemId"
            :label="option.tagName"
            :value="option.itemId"
          >
            <el-popover
              placement="left"
              title=""
              :effect="'dark'"
              :width="200"
              :show-after="200"
              trigger="hover"
            >
              <template #reference>
                <div>
                  {{ option.tagName }}
                  {{ option.status === 1 ? '(无效)' : '' }}
                </div>
              </template>
              <template #default>
                质检项说明: {{ option.tagDesc }} <br />
                典型案例：{{ option.typicalSample }}
              </template>
            </el-popover>
          </el-option>
        </el-select>
      </div>
      <div class="flex mt-15px items-center">
        <div class="mark-config-props-label">对应值：</div>
        <template
          v-if="[0, 6, 7, 8, 9, 10, 11, 12, 13].includes(item.itemType)"
        >
          <template v-if="commonStore.INTERFACE.includes(item.itemType)">
            <el-select
              class="mark-config-props-select"
              placeholder="请选择"
              size="default"
              v-model="item.tagCode"
              :disabled="!labelable"
              filterable
              remote
              remote-show-suffix
              :remote-method="($event) => remoteMethod($event, item)"
              :loading="loading"
            >
              <el-option
                v-for="option in options[item.tagId]"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </el-select>
          </template>
          <template v-else>
            <el-select
              class="mark-config-props-select"
              placeholder="请选择"
              size="default"
              v-model="item.tagCode"
              :disabled="!labelable"
              filterable
            >
              <el-option
                v-for="option in options[item.tagId]"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </el-select>
          </template>
        </template>
        <template v-else>
          <el-input
            v-model="item.tagCode"
            :disabled="!labelable"
            class="mt-1"
            placeholder="请输入"
            size="default"
          ></el-input>
        </template>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, onMounted, ref } from 'vue'

import { getItemDropDownForAnno } from '@/api/tag'

import { useMarkOperation } from '@/hooks/useMarkOperation'

import { useCommonStore } from '@/store/useCommonStore'
import useSelectOptions from '@/store/useSelectOptions'
import useUnderlineMarkStore from '@/store/useUnderlineMarkStore'

const underlineStore = useUnderlineMarkStore()
const commonStore = useCommonStore()

const props = defineProps({
  row: Object,
  rowIndex: Number,
  tagShowMethod: Number, // 0:下拉选择标注项 1:弹出全部标注项
  selectedValue: {
    type: Array,
    required: false,
  },
  qualityList: Array,
})

const emit = defineEmits(['delete'])

const labelable = useMarkOperation()

const optionsCacheStore = useSelectOptions()

const loading = ref(false)
const options = ref({})

const highlightedItems = computed(() => {
  return underlineStore.getRowData(props.rowIndex)
})

const handleDelete = (columnIndex, nodeId, index) => {
  emit('delete', { rowIndex: props.rowIndex, columnIndex, nodeId, index })
}

const handleChange = (value, item) => {
  const target = props.qualityList.find((i) => i.itemId === value)
  if (target) {
    item.itemType = target.itemType
    item.tagName = target.tagName
    item.tagId = target.itemId
    item.positionType = 10
    item.tagCode = ''
  }
}

const remoteMethod = async (str, item) => {
  let query = str.trim()
  if (query) {
    loading.value = true
    try {
      const res = await getItemDropDownForAnno(item.tagId, query)
      options.value[item.tagId] = res
    } catch (e) {
      options.value[item.tagId] = []
    } finally {
      loading.value = false
    }
  }
}

onMounted(() => {
  props.qualityList.map(async (item) => {
    if ([0, 6, 7, 8, 9, 10, 11, 12, 13].includes(item.itemType)) {
      options.value[item.itemId] = await optionsCacheStore.getOptionsByItemId(
        item.itemId
      )
    }
  })
})
</script>
