<template>
  <div class="relative">
    <template v-if="loading">
      <MarkHeader :data="data" />
      <div class="pb-60px">
        <el-table :data="tableData" border :height="'calc(100vh - 260px)'">
          <el-table-column
            v-for="(item, index) in columns"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          >
            <template #default="{ row }">
              <div class="max-h-400px overflow-y-auto">
                <VueMarkdown :markdown="formatMarkdownText(row[item.prop])" />
              </div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="标注项" width="200">
            <template #default="{ row, $index }">
              <div class="overflow-y-auto max-h-400px">
                <FixedMarkSelect
                  :row="row"
                  :rowIndex="$index"
                  :qualityList="qualityList"
                  @update="handleUpdate"
                >
                </FixedMarkSelect>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <MarkOperations
        type="fixed"
        @previous="handlePrevious"
        @next="handleNext"
      ></MarkOperations>
    </template>
  </div>
</template>
<script setup>
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

import FixedMarkSelect from '@/views/mark/components/fixedMarkSelect.vue'
import MarkHeader from '@/views/mark/components/mark-header.vue'
import MarkOperations from '@/views/mark/components/markOperations.vue'

import { getOneAnnoById } from '@/api/mark'
import { getItemDropDownForAnno, getTagListForAnno } from '@/api/tag'

import { VueMarkdown } from '@crazydos/vue-markdown'

import useFixedMarkStore from '@/store/useFixedMarkStore'
import useSelectOptions from '@/store/useSelectOptions'

const router = useRouter()
const route = useRoute()

const routeInfo = computed(() => route.query)

const userAnnoDetailsStore = useFixedMarkStore()
const optionsCacheStore = useSelectOptions()

const data = ref({
  annoStatus: -1,
  userAnnoId: '',
  sampleBusiId: '11111111',
  sampleType: 1,
  annoStatusName: '标注中',
  annotatorName: '测试人',
  annoMethod: -1,
  viewMode: 'anno',
  annoTaskId: null,
})

const tableData = ref([])
const columns = ref([])
const qualityList = ref([])

const loading = ref(false)

async function getOneAnnoData(id, idOffset = 0) {
  try {
    let {
      annoTaskId,
      userAnnoId,
      annoStatus,
      sampleBusiId,
      annotatorName,
      annoStatusName,
      annoMethod,
      sampleContent,
      sampleType,
      code,
      msg,
      userAnnoDetails,
    } = await getOneAnnoById({
      userAnnoId: id,
      idOffset: idOffset,
      viewMode: routeInfo.value.viewMode ?? data.value.viewMode,
      ...JSON.parse(decodeURIComponent(routeInfo.value.condition)),
    })
    if (code === -1) {
      ElMessage.warning(msg)
      return
    }

    if (sampleType === 1003) {
      let dialogue = JSON.parse(sampleContent) || []
      tableData.value = dialogue.dataList

      if (userAnnoDetails.length) {
        userAnnoDetailsStore.setToMap(userAnnoDetails)
      } else {
        userAnnoDetailsStore.initRows(dialogue.dataList)
      }

      const keys = Object.keys(dialogue.columnMap)
      for (const key of keys) {
        columns.value.push({
          prop: key,
          label: key,
          width: dialogue.columnNameWidthMap[key],
        })
      }
    }
    data.value.annoStatus = annoStatus
    data.value.userAnnoId = userAnnoId
    data.value.sampleType = sampleType
    data.value.sampleBusiId = sampleBusiId
    data.value.annoStatusName = annoStatusName
    data.value.annotatorName = annotatorName + ''
    data.value.annoMethod = annoMethod
    data.value.annoTaskId = annoTaskId
  } catch (e) {
    console.error(e)
  }
}

const getAnnotationTerms = async (taskId) => {
  try {
    const res = await getTagListForAnno(taskId)
    qualityList.value = res || []
  } catch (e) {
    console.error(e)
  }
}
const getOptions = async () => {
  for (let index = 0; index < qualityList.value.length; index++) {
    const item = qualityList.value[index]
    if ([0, 6, 7, 8, 9, 10, 11, 12, 13].includes(item.itemType)) {
      const res = await getItemDropDownForAnno(item.itemId)
      optionsCacheStore.setOptionsByItemId(item.itemId, res)
    }
  }
}
const handleUpdate = ({ rowIndex, data }) => {
  userAnnoDetailsStore.updateStore(rowIndex, data)
}

// 格式化markdown文本，将\\n替换为markdown换行
const formatMarkdownText = (text) => {
  if (!text || typeof text !== 'string') {
    return text
  }
  // 尝试多种方式处理换行：
  // 1. 先尝试markdown硬换行（行末两个空格+换行符）
  // 2. 如果不行，可以尝试HTML的<br>标签
  // 3. 或者双换行符创建段落

  // 使用HTML br标签，这是最可靠的方法
  return text.replace(/\\\\n/g, '<br>')
}

const handlePrevious = async ({ page, viewMode, status }) => {
  try {
    await getOneAnnoData(route.query.id, -1)
    let path = '/mark'
    if (data.value.sampleType === 1003) {
      if (data.value.annoMethod === 1) {
        path = '/fixed-mark'
      } else {
        path = '/underline-mark'
      }
    }
    await router.replace({
      path,
      query: {
        id: data.value.userAnnoId,
        taskId: data.value.annoTaskId ?? route.query.taskId,
        status,
        viewMode,
        page,
        taskName: route.query.taskName,
        annoStatus: data.value.annoStatus,
        condition: routeInfo.value.condition,
      },
    })
  } catch (e) {
    console.error(e)
  }
}

const handleNext = async ({ page, viewMode, status }) => {
  try {
    await getOneAnnoData(route.query.id, 1)
    let path = '/mark'
    if (data.value.sampleType === 1003) {
      if (data.value.annoMethod === 1) {
        path = '/fixed-mark'
      } else {
        path = '/underline-mark'
      }
    }
    const taskId = data.value.annoTaskId ?? route.query.taskId
    await router.replace({
      path,
      query: {
        id: data.value.userAnnoId,
        taskId: taskId,
        status,
        viewMode,
        page,
        taskName: route.query.taskName,
        annoStatus: data.value.annoStatus,
        condition: routeInfo.value.condition,
      },
    })
  } catch (e) {
    console.error(e)
  }
}

onMounted(async () => {
  loading.value = false
  let { id, taskId } = route.query || {}
  await getOneAnnoData(id)
  await getAnnotationTerms(taskId)
  await getOptions()
  loading.value = true
})

onBeforeUnmount(() => {
  userAnnoDetailsStore.clearData()
})

watch(
  () => route.query,
  async () => {
    loading.value = false
    const { taskId } = route.query || {}

    // await getOneAnnoData(id)
    await getAnnotationTerms(taskId)
    await getOptions()
    loading.value = true
  },
  {
    deep: true,
  }
)
</script>
