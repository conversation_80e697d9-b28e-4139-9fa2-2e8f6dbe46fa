<template>
  <MarkHeader :data="data" />

  <div class="mark-page mark-page-qa">
    <div class="mark-container">
      <div ref="markContentRef" class="mark-content">
        <div ref="markBoxRef" class="relative mark-edit mb-200px pt-20px">
          <div
            v-for="item in data.dialogue"
            :key="item.id"
            class="dialogue-list border-b-1px b-style-solid b-color-#eee"
          >
            <div
              class="dialogue-list-left flex flex-col flex-1"
              :class="isDisabled"
            >
              <div
                v-for="(ls, index) in item.ls"
                :key="index"
                class="dialogue-list-chat flex flex-1 mb-15px"
              >
                <div
                  class="dialogue-role disabled"
                  :class="[`role-class-${ls.r_t}`]"
                >
                  {{ ls.r }}：
                  <!--                  模型(引导问)：-->
                </div>
                <div class="dialogue-content">
                  <pre class="dialogue-msg !b-0">{{ ls.s }}</pre>
                </div>
              </div>

              <div class="dialogue-time-outer">
                <div
                  class="dialogue-time disabled inline text-12px !bg-#f6f6f6"
                >
                  {{ item.t }}
                </div>
              </div>
            </div>

            <div class="dialogue-ps pb-20px">
              <div class="">
                <div class="dialogue-ps-title disabled inline text-#666">
                  补充信息：
                </div>
              </div>
              <textarea
                disabled
                readonly
                class="dialogue-ps-content disabled"
                name="dialogue-ps"
                rows="10"
                >{{ item.addi }}
              </textarea>
              <!--              <div class="dialogue-ps-wrap">-->
              <!--                <div class="dialogue-ps-content disabled text-12px">-->
              <!--                  【知识】-->
              <!--                  1、车系：秦PLUS新能源品牌：比亚迪·新能源级别：轿车销量：39904优点：性价比挺高,动力十足,能耗很满意,整体空间表现出色,外观非常好看2、车系：汉品牌：比亚迪·新能源级别：轿车销量：21798优点：后排空间大,外观非常好看,乘坐感受很好,动力十足,整体空间表现出色3、车系：凯美瑞品牌：广汽丰田级别：轿车销量：21677优点：外观非常好看,动力十足,整体空间表现出色,操控很出色,能耗很满意4、车系：AION-->
              <!--                  S品牌：埃安级别：轿车销量：21546优点：外观非常好看,整体空间表现出色,座椅舒适,动力十足,乘坐感受很好5、车系：帕萨特品牌：上汽大众级别：轿车销量：17695优点：能耗很满意,操控很出色,整体空间表现出色,动力十足,外观非常好看-->
              <!--                  【问题 prompt】-->
              <!--                  现在的日期是2023-11-08，你是易车公司创造出来的人工智能助手小易，是一个汽车相关领域的智能助手，请注意是易车，不是易车网。你只会回答汽车相关或汽车领域的问题。已知信息:```1、车系：秦PLUS新能源品牌：比亚迪·新能源级别：轿车销量：39904优点：性价比挺高,动力十足,能耗很满意,整体空间表现出色,外观非常好看2、车系：汉品牌：比亚迪·新能源级别：轿车销量：21798优点：后排空间大,外观非常好看,乘坐感受很好,动力十足,整体空间表现出色3、车系：凯美瑞品牌：广汽丰田级别：轿车销量：21677优点：外观非常好看,动力十足,整体空间表现出色,操控很出色,能耗很满意4、车系：AION-->
              <!--                  S品牌：埃安级别：轿车销量：21546优点：外观非常好看,整体空间表现出色,座椅舒适,动力十足,乘坐感受很好5、车系：帕萨特品牌：上汽大众级别：轿车销量：17695优点：能耗很满意,操控很出色,整体空间表现出色,动力十足,外观非常好看```&#45;&#45;&#45;&#45;&#45;&#45;当前对话:用户:-->
              <!--                  你好助手:-->
              <!--                  你好！很高兴为您提供帮助。请问有什么关于汽车方面的问题我可以帮您解答吗？用户:20-25万值得购买的轿车Answer：-->
              <!--                </div>-->
              <!--              </div>-->
            </div>
          </div>
        </div>

        <div
          ref="contextMenuRef"
          class="context-menu-wrapper"
          :class="{ show: menuData.show }"
          :style="menuData.fixed"
        >
          <MarkConfig
            :item="config.item"
            :config="config"
            :settings="settings"
            type="menu"
          />
        </div>
      </div>
      <div ref="configRef" class="mark-config-wrapper">
        <div class="mark-config-title">质检标注</div>
        <div class="mark-config-list">
          <el-card
            v-for="(item, index) in config.list"
            :key="item.uid"
            class="mark-config-card"
            shadow="hover"
            :class="[
              { active: item.uid === settings.activeUid },
              `config-uid-${item.uid}`,
              { invalid: config.invalids.has(item.uid) },
            ]"
            @click="selectCurrent(item.uid)"
          >
            <MarkConfig
              :markable="!markable"
              :item="item"
              :index="index"
              :config="config"
              :settings="settings"
              @update="handleUpdate"
            />
          </el-card>
        </div>
        <template v-if="0">
          <el-button
            v-if="!isView && markable"
            type="success"
            size="default"
            @click="saveAnAnnoApi"
            >保存</el-button
          >
          <el-button type="primary" size="default" @click="getMarkData"
            >查看数据</el-button
          >
        </template>
      </div>
    </div>

    <div
      class="absolute flex bg-white z-10 p-10px w-100% bottom-0 left-0 border-t-1px border-solid border-#ddd flex-space-between"
    >
      <div>
        <el-button
          v-if="data.pageFrom === 'update' && data.annoStatus !== 3"
          type="danger"
          @click="abandonAnAnnoApi"
          >废弃</el-button
        >
      </div>
      <div>
        <template v-if="data.pageFrom === 'approve'">
          <el-button type="primary" size="default" @click="saveAnAnnoApi('')">
            保存
          </el-button>
          <el-button type="primary" size="default" @click="passAnAnnoApi">
            通过
          </el-button>
          <el-button type="danger" size="default" @click="rejectAnAnnoApi">
            驳回
          </el-button>
        </template>
        <template v-if="data.pageFrom === 'update'">
          <el-button
            v-if="markable"
            type="success"
            size="default"
            @click="saveAnAnnoApi('')"
            >保存</el-button
          >
          <el-button
            v-if="data.annoStatus === 2"
            type="primary"
            @click="continueAnnoHandler"
            >继续标注</el-button
          >
          <el-button
            v-if="markable"
            type="primary"
            @click="submitAuditApiWithConfirm"
            >提交审核</el-button
          >
        </template>
        <el-button type="warning" @click="getPreviousDetailWithConfirm">
          上一条
        </el-button>
        <el-button type="warning" @click="getNextDetailWithConfirm">
          下一条
        </el-button>
        <el-button type="info" @click="back">返回</el-button>
      </div>
    </div>
  </div>

  <el-drawer
    v-model="config.open"
    :lock-scroll="true"
    size="50%"
    :direction="'rtl'"
  >
    <template #header>
      <h4>标注数据展示</h4>
    </template>
    <template #default>
      <el-input
        v-model="config.data"
        :autosize="{ minRows: 10, maxRows: 20 }"
        type="textarea"
        class="text-16px"
        placeholder="标注数据"
      />
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import {
  computed,
  nextTick,
  onBeforeUnmount,
  onMounted,
  provide,
  reactive,
  ref,
} from 'vue'
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'

import MarkConfig from './components/mark-config.vue'
import MarkHeader from '@/views/mark/components/mark-header.vue'

import {
  abandonAnAnno,
  continueAnno,
  getOneAnnoById,
  saveAnAnno,
  submitAudit,
} from '@/api/mark'
import { passAnAnno, rejectAnAnno } from '@/api/tag'

import { ColorList, SampleTypeToPath } from '@/common/constants'
import { getLastData, getMenuPosition, isTrue, sleep } from '@/common/helper'
import {
  AxiosErrorResType,
  DialogueType,
  markReqItemType,
} from '@/common/interface'

import { Guid } from '@/plugins/JsMark/util'

import type { markConfigItemType } from '@/hooks/JsMark/common/interface'
import {
  clearLocalData,
  getLocal,
  resetLocalData,
} from '@/hooks/JsMark/common/localStorage.js'
import useAnnotation from '@/hooks/JsMark/useAnnotation'
import useMark from '@/hooks/JsMark/useMark'

import { useConfigStore } from '@/store/useConfigStore'

const store = useConfigStore()
const Router = useRouter()
const Route = useRoute()

const {
  renderStore,
  destroy,
  initMark,
  clearMarkAll,
  markSelected,
  markSelector,
} = useMark()

const {
  addAntRecords,
  addEditRecord,
  setAntBorderColor,
  scrollToCurrentConfig,
  addSelectedClassByUid,
  deleteAnnotation,
  setMarkClassByUid,
} = useAnnotation()

const cssStyle = ColorList.map((item) => item.value)
const contextMenuRef = ref<HTMLElement>()
const markBoxRef = ref<HTMLElement>()
const configRef = ref<HTMLElement>()
const markContentRef = ref<HTMLElement>()

const config = reactive({
  value: '',
  list: [] as markConfigItemType[],
  data: '',
  open: false,
  item: {} as markConfigItemType,
  invalids: new Set<string>(),
})

const settings = reactive({
  activeClassName: cssStyle[0],
  activeUid: '',
  textDesc: '',
  open: false,
  saveStatus: false,
})

const data = reactive({
  dialogue: [] as DialogueType[],
  searchTotal: 0,
  searchIndex: 0,
  annoTaskName: '',
  sampleBusiId: '',
  annoStatusName: '',
  annoStatus: 0,
  userAnnoId: '',
  appId: '',
  serviceId: '',
  annotatorName: '',
  pageFrom: '',
  viewMode: 'view',
  sampleType: 0,
  tagShowMethod: 0,
  condition: {},
})

const menuData = reactive({
  show: false,
  value: [],
  fixed: {
    left: '',
    top: '',
  },
})

provide('mark-config', {
  settings,
  config,
})

let isView = computed(() => {
  let { status } = Route.query || {}
  return ['view', 'approve'].includes(status as string)
})

let isDisabled = computed(() => {
  return {
    'box-disabled': !markable.value,
  }
})

let markable = computed(() => {
  return (
    (data.pageFrom === 'update' && data.annoStatus === 1) ||
    data.pageFrom === 'approve'
  )
})

async function passAnAnnoApi() {
  try {
    await saveAnAnnoApi('')
    if (settings.saveStatus) {
      let res = await passAnAnno([data.userAnnoId])
      if (res === 'OK') {
        await sleep(200)
        if (window.__MICRO_APP_ENVIRONMENT__) {
          window.rawWindow.location.reload()
        } else {
          Router.go(0)
        }
        // data.annoStatusName = '已完成'
      } else {
        ElMessage.warning((res as AxiosErrorResType).msg)
      }
    }
  } catch (e) {
    console.log(e)
  }
}

async function rejectAnAnnoApi() {
  try {
    await saveAnAnnoApi()
    if (settings.saveStatus) {
      let res = await rejectAnAnno([data.userAnnoId])
      if (res === 'OK') {
        await sleep(200)
        if (window.__MICRO_APP_ENVIRONMENT__) {
          window.rawWindow.location.reload()
        } else {
          Router.go(0)
        }
        // data.annoStatusName = '已驳回'
      } else {
        ElMessage.warning((res as AxiosErrorResType).msg)
      }
    }
  } catch (e) {
    console.log(e)
  }
}

async function getOneAnnoData(id, idOffset = 0) {
  try {
    let {
      userAnnoId,
      appId,
      serviceId,
      annoTaskName,
      sampleBusiId,
      annotatorName,
      annoStatusName,
      annoStatus,
      sampleContent,
      userAnnoDetails,
      sampleType,
      tagShowMethod,
      code,
      msg,
    } = await getOneAnnoById({
      userAnnoId: id,
      idOffset: idOffset,
      viewMode: data.viewMode,
      ...data.condition,
    })
    if (code === -1) {
      ElMessage.warning(msg)
      return
    }
    data.userAnnoId = userAnnoId + ''
    if (idOffset === 1 || idOffset === -1) {
      // ?id=1955&taskId=37&status=update&page=viewTask&taskName=测试
      let { taskId, status, page, taskName } = Route.query || {}
      Router.replace({
        path: SampleTypeToPath[sampleType],
        query: {
          ...Route.query,
          id: userAnnoId,
        },
      })
      reset()
      await sleep(200)
      if (window.__MICRO_APP_ENVIRONMENT__) {
        window.rawWindow.location.reload()
      } else {
        Router.go(0)
      }
      return
    }
    let dialogue = JSON.parse(sampleContent) || []
    data.dialogue = dialogue
    console.log(dialogue)
    data.sampleType = +sampleType
    data.tagShowMethod = tagShowMethod
    data.annoTaskName = annoTaskName
    data.sampleBusiId = sampleBusiId
    data.annoStatusName = annoStatusName
    data.annoStatus = annoStatus
    data.appId = appId + ''
    data.serviceId = serviceId + ''
    data.annotatorName = annotatorName + ''
    let list = getLastData(userAnnoDetails || [])
    console.log(list)
    resetLocalData(list)
  } catch (e) {
    console.log(e)
  }
}

function getPreviousDetailWithConfirm() {
  let list = getSaveList()
  if (list.length === 0 && data.viewMode.includes('anno')) {
    ElMessageBox.confirm('暂未标注，是否确认切换？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      await getPreviousDetail()
    })
  } else {
    getPreviousDetail()
  }
}
function getNextDetailWithConfirm() {
  let list = getSaveList()
  if (list.length === 0 && data.viewMode.includes('anno')) {
    ElMessageBox.confirm('暂未标注，是否确认切换？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      await getNextDetail()
    })
  } else {
    getNextDetail()
  }
}

// 上一条
async function getPreviousDetail() {
  try {
    if (markable.value) {
      await saveAnAnnoApi()
    } else {
      settings.saveStatus = true
    }
    if (settings.saveStatus) {
      await getOneAnnoData(data.userAnnoId, -1)
    }
  } catch (e) {
    console.log(e)
  }
}

// 下一条
async function getNextDetail() {
  try {
    if (markable.value) {
      await saveAnAnnoApi()
    } else {
      settings.saveStatus = true
    }
    if (settings.saveStatus) {
      await getOneAnnoData(data.userAnnoId, 1)
    }
  } catch (e) {
    console.log(e)
  }
}

const addEventHandler = (type: 'add' | 'remove') => {
  markBoxRef?.value?.[`${type}EventListener`]('contextmenu', (e: Event) => {
    //取消默认的浏览器自带右键
    e.preventDefault()
    // showContextmenu(e as MouseEvent)
  })
  // contextMenuRef?.value?.[`${type}EventListener`]('click', menuItemClick)
  document[`${type}EventListener`]('click', (e: Event) => {
    e.stopPropagation()
    menuData.show = false
  })
  window[`${type}EventListener`]('keydown', (e: Event) => {
    // console.log(event)
    shortcutsHandler(e as KeyboardEvent)
  })
  contextMenuRef?.value?.[`${type}EventListener`]('click', (e: Event) => {
    e.stopPropagation()
  })
}

function shortcutsHandler(e: KeyboardEvent) {
  if (
    config.list.length &&
    (e.metaKey || e.ctrlKey) &&
    e.altKey &&
    [46, 8].includes(e.keyCode)
  ) {
    // ctrl + alt + delete 清除所有标注
    reset()
  }
  if (!config.list.length || !settings.activeUid) return
  if (
    (e.metaKey || e.ctrlKey) &&
    [46, 8].includes(e.keyCode) &&
    settings.activeUid
  ) {
    // delete 删除选中的标注
    deleteByUid(settings.activeUid)
  }
  if (/^F[1-4]/.test(e.key)) {
    // F1 - F4 设置选中的颜色
    let className = `annotator-hl-${e.key.slice(1)}`
    // console.log(className)
    setMarkClassByUid(settings.activeUid, className)
  }
}
function showContextmenu(e: MouseEvent) {
  e.stopPropagation()
  let target = e.target as HTMLElement
  if (target.classList.contains(markSelected)) {
    // 已经标注
    let uid = target.dataset['selector'] || ''
    settings.activeUid = uid
    config.item =
      config.list.find((item) => item.uid === uid) || ({} as markConfigItemType)
    let { offsetTop, offsetLeft, offsetWidth, parentNode } = target
    let ps = getMenuPosition(markContentRef?.value as HTMLElement, target)
    // let { y, height, left } = target.getBoundingClientRect()
    // let h = window.innerHeight - y - height
    let l = ps === 'left' ? offsetLeft + 20 : offsetLeft + 20 - 130
    menuData.fixed = {
      left: `${l}px`,
      top: `${offsetTop + 35}px`,
    }
    menuData.value = []
    menuData.show = true
  }
}

function deleteByUid(uid: string) {
  deleteAnnotation(uid)
  config.list = getLocal().map((item, index) => ({
    ...item,
    ...item.desc,
    index: index + 1,
  }))
  settings.activeUid = ''
}

function selectCurrent(uid: string) {
  // if (settings.activeUid === uid) return
  settings.activeUid = uid
  addSelectedClassByUid(uid)
  let ele = document.querySelector<HTMLElement>(
    `span[${markSelector}="${uid}"]`
  )
  ele?.scrollIntoView({
    behavior: 'smooth',
  })
}

function getMarkData() {
  console.log(config.list)
  config.data = JSON.stringify(
    config.list.map(
      ({ id, className, offset, uid, configKey, configValue, text }) => ({
        id,
        uid,
        text,
        offset,
        configKey,
        configValue,
        className,
      })
    ),
    null,
    2
  )
  config.open = true
}

function reset() {
  clearMarkAll()
  clearLocalData()
  config.list = []
}

async function abandonAnAnnoApi() {
  ElMessageBox.confirm('确定要废弃这条样本吗？', '提示', {
    // title: '提示',
    // 'show-cancel-button': true,
    type: 'warning',
    message: '确定要废弃这条样本吗？',
  })
    .then(async () => {
      try {
        let res = await abandonAnAnno(data.userAnnoId)
        if (res === 'OK') {
          await sleep(200)
          if (window.__MICRO_APP_ENVIRONMENT__) {
            window.rawWindow.location.reload()
          } else {
            Router.go(0)
          }
        } else {
          ElMessage.warning((res as AxiosErrorResType).msg)
        }
      } catch (e) {
        console.log(e)
      }
    })
    .catch(() => {
      console.log('取消')
    })
}

async function continueAnnoHandler() {
  try {
    let res = await continueAnno(data.userAnnoId)
    if (res === 'OK') {
      // 成功
      await getOneAnnoData(data.userAnnoId)
      // router.go(0)
    }
  } catch (e) {
    console.log(e)
  }
}

function submitAuditApiWithConfirm() {
  const list = getSaveList()
  if (list.length === 0) {
    ElMessageBox.confirm('暂未标注，是否确认提交？', '提示', {
      type: 'warning',
      message: '确定要提交审核吗？',
    })
      .then(async () => {
        try {
          await submitAuditApi()
        } catch (e) {
          console.log(e)
        }
      })
      .catch(() => {
        console.log('取消')
      })
  } else {
    submitAuditApi()
  }
}
async function submitAuditApi() {
  try {
    await saveAnAnnoApi('no')
    console.log(data.userAnnoId)
    if (settings.saveStatus) {
      let res = await submitAudit(data.userAnnoId)
      if (res === 'OK') {
        await getOneAnnoData(data.userAnnoId)
        // router.go(0)
      }
    }
  } catch (e) {
    console.log(e)
  }
}

async function saveAnAnnoApi(msg = '保存成功！') {
  if (!markable.value) {
    settings.saveStatus = true
    console.log('markable:', markable.value)
    return
  }
  const loading = ElLoading.service({
    lock: true,
    text: '保存中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  try {
    let list = getSaveList()
    console.table(list)
    let lastList = list.reduce((prev, item, index) => {
      let { tagCode, tagId } = item
      let key = `${tagId}-${tagCode}`
      let tagName = store.getItemCodeMap(key) as string
      console.log(key, tagName)
      prev.push({
        ...item,
        tagName,
      })
      return prev
    }, [] as markReqItemType[])
    console.table(lastList)
    console.log(config.invalids)
    if (lastList.length && config.invalids.size > 0) {
      ElMessage.warning('质检项和对应值不能为空')
      settings.saveStatus = false
      return
    }
    let res = await saveAnAnno({
      userAnnoId: data.userAnnoId,
      userAnnoDetails: lastList,
      msg,
    })
    if (res === 'OK') {
      settings.saveStatus = true
      config.invalids.clear()
    } else {
      ElMessage.warning((res as AxiosErrorResType).msg)
      settings.saveStatus = false
    }
  } catch (e) {
    console.log(e)
    settings.saveStatus = false
  } finally {
    loading.close()
  }
}

function getSaveList(): markReqItemType[] {
  config.invalids.clear()
  let list: markReqItemType[] = []
  for (let {
    configValue,
    configKey,
    text,
    offset,
    uid,
    className,
    tagName,
    auditCommentInfo,
  } of config.list) {
    if (isTrue(configKey) && isTrue(configValue)) {
      // console.log(uid)
    } else {
      config.invalids.add(uid)
    }
    console.log(tagName)
    list.push({
      auditCommentInfo: JSON.stringify(auditCommentInfo || {}),
      position: offset as number,
      content: text || '',
      tagId: configKey || '',
      tagCode: configValue || '',
      tagName: tagName?.toString() || '',
      uid: uid,
      className: className || 'annotator-hl-1',
    })
  }
  return list
}

async function back() {
  // await saveAnAnnoApi()
  // if (settings.saveStatus) {
  //   Router.go(-1)
  // }
  Router.go(-1)
}

const init = () => {
  initMark(markBoxRef.value, settings.activeClassName, {
    onSelected,
    onClick: markOnClick,
  })
  console.log(getLocal())
  renderStore(getLocal())
}

function onSelected({
  id,
  uid,
  offsetTop,
  offset,
  desc,
  hasStoreRender,
  text,
  className,
  auditCommentInfo,
}) {
  // console.log({ uid, offset, offsetTop, desc, hasStoreRender, text, className })
  settings.textDesc = ''
  let index = config.list.length + 1
  let item = {
    id,
    auditCommentInfo: auditCommentInfo || {},
    uid,
    offset,
    offsetTop,
    desc: {
      ...desc,
      index,
    },
    text,
    className,
    index,
    configKey: '',
    configValue: '',
  }

  config.list.push({ ...item, ...item.desc })

  if (hasStoreRender) {
    // 渲染
    addAntRecords({
      uid,
      desc,
      offsetTop,
    })
  } else {
    // 选择
    addEditRecord(uid, offsetTop)
    settings.activeUid = uid
    addSelectedClassByUid(uid)
    nextTick(() => {
      scrollToCurrentConfig(uid)
    })

    if (data.tagShowMethod) {
      selectAll({ text, offset, uid })
    }
  }

  nextTick(() => {
    setAntBorderColor(uid)
  })
}

async function selectAll({ text, offset, uid }) {
  let originList = store.qualityList.map(({ id, itemId }) => {
    return {
      position: offset,
      content: text,
      tagId: id + '',
      tagCode: '',
      tagName: '',
      uid: Guid(),
      className: 'annotator-hl-1',
      auditCommentInfo: '{}',
      id: '',
    }
  })
  let list = getLastData(originList)
  let oldList = getLocal()
  resetLocalData([...oldList, ...list])
  renderStore(list)
  await sleep(500)
  nextTick(() => {
    if (store.qualityList.length) {
      deleteByUid(uid)
    }
    console.log(getLocal())
  })
}

function markOnClick({ uid }) {
  addSelectedClassByUid(uid)
  scrollToCurrentConfig(uid)
  if (settings.activeUid === uid) return
  settings.activeUid = uid
  const marked = document.querySelector(`[data-uid="${uid}"]`)
  settings.textDesc = marked?.textContent || ''
  addAntRecords({
    uid,
    isEdit: true,
  })
}

onBeforeRouteLeave(async (to, from) => {
  try {
    await saveAnAnnoApi()
    // await sleep(5000)
  } catch (e) {
    console.log(e)
    return false
  }
  // 取消导航并停留在同一页面上
  return settings.saveStatus
})

async function beforeunload(e: BeforeUnloadEvent) {
  e.preventDefault()
  let msg = '确定要离开此页面吗？'
  e.returnValue = msg
  await saveAnAnnoApi()
  return msg
}

function handleUpdate({ index, auditCommentInfo }) {
  config.list[index].auditCommentInfo = auditCommentInfo
}

onMounted(async () => {
  let { status, viewMode, id, condition } = Route.query || {}
  console.log(condition)
  let p = JSON.parse(decodeURIComponent(condition as string))
  console.log(p)
  data.condition = p
  data.pageFrom = status as string
  data.viewMode = viewMode as string
  data.userAnnoId = (id || '471') as string
  const loading = ElLoading.service({
    lock: true,
    text: '数据加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  await getOneAnnoData(data.userAnnoId)
  loading.close()
  const { taskId } = Route.query
  store.setQualityList(taskId)
  nextTick(() => {
    init()
    addEventHandler('add')
  })
})

onBeforeUnmount(() => {
  console.log('onBeforeUnmount')
  destroy()
  clearLocalData()
  addEventHandler('remove')
})
</script>
