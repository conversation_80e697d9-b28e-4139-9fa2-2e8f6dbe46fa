<template>
  <div class="mark-task-page bg-white">
    <div class="bg-#fff page-header p-10px pl-20px text-14px">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">任务列表</el-breadcrumb-item>
        <el-breadcrumb-item>小易纠错标注</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex mt-20px items-center">
        <div class="color-#666">
          任务ID：
          <span class="color-dark font-600">10010</span>
        </div>
        <div class="ml-20px color-#666">
          所属应用：
          <span class="color-dark font-600">智能小易</span>
        </div>
        <div class="ml-20px color-#666">
          对应服务：
          <span class="color-dark font-600">纠错</span>
        </div>
        <div class="ml-20px color-#666">
          任务进度：
          <span class="color-dark font-600">待分配人员</span>
        </div>
        <div class="ml-20px color-#666">
          我的待完成量：
          <span class="color-dark font-600">12</span>
        </div>
        <div class="ml-20px color-#666 mr-20px">
          期望完成时间：
          <span class="color-dark font-600">2023-06-12</span>
        </div>
        <el-button type="primary" @click="toTaskAllocation">样本分配</el-button>
      </div>
    </div>
    <div class="mark-task-body p-20px">
      <el-form :inline="true" :model="formSearch" class="demo-form-inline">
        <el-form-item label="信息搜索">
          <el-input v-model="formSearch.user" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="标注状态">
          <el-select v-model="formSearch.region" placeholder="全部状态">
            <el-option label="Zone one" value="shanghai" />
            <el-option label="Zone two" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary">搜索</el-button>
          <el-button>重置</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="date" label="样本id" width="180" />
        <el-table-column prop="name" label="状态" width="180" />
        <el-table-column prop="name" label="标注人" width="180" />
        <el-table-column prop="name" label="提交审批时间" />
        <el-table-column prop="name" label="操作" width="200">
          <template #default>
            <el-button link type="primary">查看</el-button>
            <el-button link type="primary">标注</el-button>
            <el-button link type="primary">放弃</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex flex-end mt-20px">
        <el-pagination
          background
          v-model:current-page="pagData.page"
          v-model:page-size="pagData.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, jumper, sizes"
          :total="pagData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
  <TaskAllocation @change="onTaskAllocationChange" ref="taskAllocationRef" />
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'

import TaskAllocation from '@/views/mark/components/task-allocation.vue'

let taskAllocationRef = ref()

let data = reactive({
  sampleBusiId: '',
  annoStatusName: '',
})

let formSearch = reactive({
  user: '',
  region: '',
})

let tableData = reactive([])

let pagData = reactive({
  total: 100,
  page: 1,
  size: 10,
})

function toTaskAllocation() {
  // console.log(taskAllocationRef.value)
  taskAllocationRef.value.dialogData.show = true
}

function onTaskAllocationChange(status) {
  console.log(status)
}

function handleSizeChange(size) {
  console.log(size)
}

function handleCurrentChange(page) {
  console.log(page)
}
</script>
