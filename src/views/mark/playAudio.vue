<template>
  <div id="aplayer"></div>
</template>

<script setup>
import { onMounted } from 'vue'

import 'aplayer/dist/APlayer.min.css'
import APlayer from 'aplayer'

const audio = [
  {
    name: 'China-Y', //歌名
    artist: '徐梦圆', //歌手
    url: 'https://www.ytmp3.cn/down/59298.mp3down/39868.mp3', //音频文件地址
    cover:
      'http://imge.kugou.com/stdmusic/150/20170815/20170815070007812976.jpg', //音乐封面地址
    lrc: '', // 歌词
  },
]

onMounted(() => {
  addMyAudio()
})

const addMyAudio = () => {
  const ap = new APlayer({
    container: document.getElementById('aplayer'),
    audio: audio,
  })
}
</script>
