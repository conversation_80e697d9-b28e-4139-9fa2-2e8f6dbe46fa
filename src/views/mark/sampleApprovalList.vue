<template>
  <div class="mark-task-page bg-white">
    <div class="p-20px pb-0">
      <!-- <PageTabs :data="MarkTaskTabs" value="22" /> -->
      <NotAuth v-if="useAuth()" />
    </div>
    <template v-if="!useAuth()">
      <div class="px-20px">
        <el-radio-group @change="typeChange"
                        v-model="formSearch.purposeId">
          <el-radio-button :label="2">评测集审批</el-radio-button>
          <el-radio-button :label="1">训练集审批</el-radio-button>
        </el-radio-group>
        <el-divider />
      </div>

      <div class="p-20px pt-0">
        <el-form :inline="true"
                 :model="formSearch">
          <el-form-item label="样本ID:">
            <el-input clearable
                      v-model="formSearch.sampleBusiId"
                      @keyup.enter="query"
                      placeholder="输入样本ID搜索" />
          </el-form-item>
          <el-form-item label="所属任务:">
            <el-input clearable
                      v-model="formSearch.taskIdNameInVague"
                      @keyup.enter="query"
                      placeholder="输入任务ID或任务名称" />
          </el-form-item>
          <el-form-item label="标注进度:">
            <el-select v-model="formSearch.annoStatus"
                       clearable
                       filterable
                       placeholder="全部状态"
                       class="w-42">
              <el-option v-for="item in data.annoStatusList"
                         :key="item.key"
                         :label="item.value"
                         :value="item.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="标注人:">
            <el-select v-model="formSearch.annoUserDomain"
                       clearable
                       filterable
                       placeholder="全部"
                       class="w-42">
              <el-option v-for="item in data.annoUserList"
                         :key="item.key"
                         :label="item.value"
                         :value="item.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="提交审批时间:">
            <el-date-picker v-model="formSearch.submitTimeBeginEnd"
                            clearable
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期" />
          </el-form-item>
          <el-form-item label="所属应用:">
            <el-select v-model="formSearch.appId"
                       clearable
                       placeholder="全部"
                       class="w-42">
              <el-option v-for="item in applicationOptions"
                         :key="item.key"
                         :label="item.value"
                         :value="item.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="对应服务:">
            <el-select v-model="formSearch.serviceId"
                       clearable
                       placeholder="全部"
                       class="w-42">
              <el-option v-for="item in servicesOptions"
                         :key="item.key"
                         :label="item.value"
                         :value="item.key" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary"
                       @click="query">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="a">
          <el-button type="primary"
                     :disabled="data.ids.length <= 0"
                     @click="passAnAnnoApiWithConfirm">批量通过</el-button>
          <el-button type="danger"
                     :disabled="data.ids.length <= 0"
                     @click="rejectAnAnnoApiWithConfirm">批量驳回</el-button>
        </div>
        <div class="flex items-center text-14px mb-10px border-solid mt-10px border-1px border-#BAE7FF p-5px pl-10px bg-#E6F7FF">
          <el-icon class="mr-10px"
                   size="20"
                   color="#409eff">
            <IEpInfoFilled />
          </el-icon>
          已选择
          <span class="pl-10px pr-10px color-#409eff">{{
            data.ids.length
            }}</span>
          项
        </div>
        <el-table :data="data.tableData"
                  @selection-change="handleSelectionChange"
                  style="width: 100%">
          <el-table-column type="selection"
                           :selectable="checkDisable"
                           width="55" />
          <el-table-column prop="sampleBusiId"
                           label="样本ID"
                           width="120" />
          <el-table-column prop="taskIdDisplay"
                           label="所属任务ID"
                           width="120" />
          <el-table-column prop="applicationName"
                           label="所属应用" />
          <el-table-column prop="serviceName"
                           label="对应服务" />
          <el-table-column prop="annoTaskName"
                           label="所属任务" />
          <el-table-column prop="annoStatus"
                           label="样本状态"
                           width="100">
            <template #default="scope">
              {{ data.statusMap[scope.row.annoStatus] }}
            </template>
          </el-table-column>
          <el-table-column prop="userName"
                           label="标注人"
                           width="100" />
          <el-table-column prop="submitAuditTime"
                           label="提交审批时间" />
          <el-table-column prop="auditorName"
                           label="审批人">
            <template #default="{ row }">
              {{ row.auditorName || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="auditTime"
                           label="审批时间">
            <template #default="{ row }">
              {{ row.auditTime || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="id"
                           label="操作"
                           width="160">
            <template #default="scope">
              <el-button :disabled="+scope.row.annoStatus !== 2"
                         @click="toMarkPage(scope.row, 'approve')"
                         link
                         type="primary">审批</el-button>
              <el-button @click="toMarkPage(scope.row, 'view')"
                         link
                         type="primary">查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex flex-end mt-20px">
          <el-pagination background
                         v-model:current-page="pag.page"
                         v-model:page-size="pag.size"
                         :page-sizes="[10, 20, 50, 100]"
                         layout="total, prev, pager, next, jumper, sizes"
                         :total="pag.total"
                         @size-change="handleSizeChange"
                         @current-change="handleCurrentChange" />
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { h, onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import dayjs from "dayjs";
import { ElMessageBox } from "element-plus";

import NotAuth from "@/views/401.vue";

import { getAllAnnoUsers, getDropDownList } from "@/api/common";
import { getAuditingPage, passAnAnno, rejectAnAnno } from "@/api/tag";

import type {
  AuditingResponseType,
  SelectListItemType,
} from "@/common/interface";

import type { Dayjs } from "dayjs";

import { useAuth } from "@/hooks/useAuth";

const Router = useRouter();

const pag = reactive({
  page: 1,
  size: 10,
  total: 0,
});

const formSearch = reactive({
  sampleBusiId: "",
  annoStatus: 2 as number | string,
  taskIdNameInVague: "",
  annoUserDomain: "",
  purposeId: 2,
  submitTimeBeginEnd: [dayjs(), dayjs()] as Dayjs[],
  serviceId: "",
  appId: "",
});

const data: {
  tableData: AuditingResponseType[];
  appTypeList: SelectListItemType[];
  annoStatusList: SelectListItemType[];
  annoUserList: SelectListItemType[];
  ids: number | string[];
  statusMap: {
    [k: number]: string;
  };
  condition: string;
} = reactive({
  tableData: [],
  appTypeList: [],
  annoStatusList: [],
  annoUserList: [],
  ids: [],
  statusMap: {},
  condition: "",
});

const applicationOptions = ref<SelectListItemType[]>([]);
const servicesOptions = ref<SelectListItemType[]>([]);

function toMarkPage(row: AuditingResponseType, status: "approve" | "view") {
  let path = "/mark";

  const { sampleType, annoMethod } = row;
  if (sampleType === 1003) {
    if (annoMethod === 1) {
      path = "/fixed-mark";
    } else {
      path = "/underline-mark";
    }
  }
  Router.push({
    path,
    query: {
      id: row.id,
      status,
      taskId: row.annoTaskId,
      viewMode: status === "view" ? "audit_view" : "audit",
      page: "approve",
      condition: encodeURIComponent(data.condition),
    },
  });
}

function checkDisable(row) {
  return +row.annoStatus === 2 ? 1 : 0;
}

function handleSelectionChange(rows) {
  console.log(rows);
  data.ids = rows.map((item) => item.id) || [];
}

function query() {
  pag.page = 1;
  getAuditingPageApi();
}

function handleSizeChange(size) {
  console.log(size);
  pag.size = size;
  getAuditingPageApi();
}

function handleCurrentChange(page) {
  console.log(page);
  pag.page = page;
  getAuditingPageApi();
}

function typeChange(type) {
  formSearch.purposeId = type;
  pag.page = 1;
  pag.size = 10;
  pag.total = 0;
  if (
    localStorage.getItem(
      type === 2 ? "searchEvaluateMarkData" : "searchTrainMarkData"
    )
  ) {
    const search = JSON.parse(
      localStorage.getItem(
        type === 2 ? "searchEvaluateMarkData" : "searchTrainMarkData"
      ) as string
    );
    if (typeof search === "object") {
      console.log(search, 45456);
      formSearch.taskIdNameInVague = search.taskIdNameInVague;
      formSearch.sampleBusiId = search.sampleBusiId;
      formSearch.annoStatus = search.annoStatus;
      formSearch.annoUserDomain = search.annoUserDomain;
      formSearch.submitTimeBeginEnd = search.submitTimeBeginEnd;
    }
  }
  getAuditingPageApi();
}

function reset() {
  pag.page = 1;
  pag.size = 10;
  pag.total = 0;
  formSearch.taskIdNameInVague = "";
  formSearch.sampleBusiId = "";
  formSearch.annoStatus = 2;
  formSearch.annoUserDomain = "";
  formSearch.submitTimeBeginEnd = [dayjs(), dayjs()];
  getAuditingPageApi();
}

function passAnAnnoApiWithConfirm() {
  ElMessageBox.confirm(
    () => {
      return h("div", [
        h("div", { class: "text-xm" }, [
          "是否确认",
          h("strong", "批量通过"),
          "已选样本？",
        ]),
        h(
          "div",
          h("div", { class: "text-12px mt-2" }, "该操作不可逆，请谨慎操作！")
        ),
      ]);
    },
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    passAnAnnoApi();
  });
}
async function passAnAnnoApi() {
  try {
    const res = await passAnAnno(data.ids);
    if (res === "OK") {
      getAuditingPageApi();
    }
  } catch (e) {
    console.log(e);
  }
}

function rejectAnAnnoApiWithConfirm() {
  ElMessageBox.confirm(
    () => {
      return h("div", [
        h("div", { class: "text-xm" }, [
          "是否确认",
          h("strong", "批量驳回"),
          "已选样本？",
        ]),
        h(
          "div",
          h("div", { class: "text-12px mt-2" }, "该操作不可逆，请谨慎操作！")
        ),
      ]);
    },
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    rejectAnAnnoApi();
  });
}
async function rejectAnAnnoApi() {
  try {
    const res = await rejectAnAnno(data.ids);
    if (res === "OK") {
      getAuditingPageApi();
    }
  } catch (e) {
    console.log(e);
  }
}

async function getAllAnnoUsersApi() {
  try {
    const res = await getAllAnnoUsers();
    data.annoUserList = res;
  } catch (e) {
    console.log(e);
  }
}

async function getDropDownListApi(dropDownType) {
  try {
    const res = await getDropDownList(dropDownType);
    if (dropDownType === 1) {
      data.appTypeList = res;
    } else {
      data.annoStatusList = res;
      data.statusMap = res.reduce((prev, item) => {
        prev[item.key] = item.value;
        return prev;
      }, {});
    }
  } catch (e) {
    console.log(e);
  }
}

async function getAuditingPageApi() {
  localStorage.setItem(
    formSearch.purposeId === 2
      ? "searchEvaluateMarkData"
      : "searchTrainMarkData",
    JSON.stringify(formSearch)
  );
  const dateStr = formSearch.submitTimeBeginEnd
    ? formSearch.submitTimeBeginEnd.map((date) =>
        dayjs(date).format("YYYY-MM-DD")
      )
    : "";
  const params = {
    ...formSearch,
    submitTimeBeginEnd: dateStr + "",
  };
  data.condition = JSON.stringify(params);
  try {
    const { pageSize, currentPage, total, elements } = await getAuditingPage({
      ...pag,
      ...params,
    });
    console.log({ pageSize, currentPage, total, elements });
    pag.total = total;
    data.tableData = elements;
  } catch (e) {
    console.log(e);
  }
}

async function getApplications() {
  const res = await getDropDownList(1);
  applicationOptions.value = res;
}
async function getServices() {
  const res = await getDropDownList(4);
  servicesOptions.value = res;
}

onMounted(async () => {
  if (localStorage.getItem("searchEvaluateMarkData")) {
    const search = JSON.parse(
      localStorage.getItem("searchEvaluateMarkData") as string
    );
    if (typeof search === "object") {
      formSearch.taskIdNameInVague = search.taskIdNameInVague;
      formSearch.sampleBusiId = search.sampleBusiId;
      formSearch.annoStatus = search.annoStatus;
      formSearch.annoUserDomain = search.annoUserDomain;
      formSearch.submitTimeBeginEnd = search.submitTimeBeginEnd;
    }
  }
  getAuditingPageApi();
  // getDropDownListApi(1)
  getDropDownListApi(6);
  getAllAnnoUsersApi();

  getApplications();
  getServices();
});
</script>

<style scoped>
.w-42 {
  width: 200px !important;
}
</style>
