<template>
  <div class="relative mark-underline">
    <template v-if="loading">
      <MarkHeader :data="data"></MarkHeader>
      <div class="pb-60px">
        <el-table
          :data="tableData"
          border
          :height="'calc(100vh - 220px)'"
          :cell-class-name="handleFixedColumn"
          :cell-style="
            () => {
              return { 'vertical-align': 'top' }
            }
          "
          :fit="true"
        >
          <el-table-column
            v-for="(item, index) in columns"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          >
            <template #default="{ row, $index }">
              <div class="overflow-y-auto max-h-400px">
                <HighlightItem
                  :ref="(el) => (highlightRefs[$index + '-' + index] = el)"
                  :id="$index + '-' + index"
                  :text="row[item.prop]"
                  @add="handleAdd"
                ></HighlightItem>
              </div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="标注项" :width="280">
            <template #default="{ $index }">
              <div class="overflow-y-auto max-h-400px">
                <UnderlineMarkItems
                  :tag-show-method="data.tagShowMethod"
                  :row-index="$index"
                  :quality-list="qualityList"
                  @delete="handleDelete"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <MarkOperations
        type="underline"
        @previous="handlePrevious"
        @next="handleNext"
      ></MarkOperations>
    </template>
  </div>
</template>
<script setup>
import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

import HighlightItem from './components/highlightItem.vue'
import MarkHeader from '@/views/mark/components/mark-header.vue'
import MarkOperations from '@/views/mark/components/markOperations.vue'
import UnderlineMarkItems from '@/views/mark/components/underlineMarkItems.vue'

import { getOneAnnoById } from '@/api/mark'
import { getItemDropDownForAnno, getTagListForAnno } from '@/api/tag'

import useSelectOptions from '@/store/useSelectOptions'
import useUnderlineMarkStore from '@/store/useUnderlineMarkStore'

const router = useRouter()
const route = useRoute()

const routeInfo = computed(() => route.query)

const optionsCacheStore = useSelectOptions()

const data = ref({
  annoStatus: -1,
  userAnnoId: '',
  sampleBusiId: '11111111',
  sampleType: 1,
  annoStatusName: '标注中',
  annotatorName: '测试人',
  annoMethod: -1,
  viewMode: 'anno',
  tagShowMethod: 0, // 0:下拉选择标注项 1:弹出全部标注项
})

const tableData = ref([])
const columns = ref([])
const qualityList = ref([])

const loading = ref(false)

const highlightRefs = ref({})

const underlineStore = useUnderlineMarkStore()

async function getOneAnnoData(id, idOffset = 0) {
  try {
    let {
      userAnnoId,
      annoStatus,
      sampleBusiId,
      annotatorName,
      annoStatusName,
      annoMethod,
      sampleContent,
      sampleType,
      code,
      msg,
      userAnnoDetails,
      tagShowMethod,
    } = await getOneAnnoById({
      userAnnoId: id,
      idOffset: idOffset,
      viewMode: routeInfo.value.viewMode ?? data.value.viewMode,
    })
    if (code === -1) {
      ElMessage.warning(msg)
      return false
    }

    if (sampleType === 1003) {
      let dialogue = JSON.parse(sampleContent) || []
      tableData.value = dialogue.dataList

      if (userAnnoDetails.length) {
        underlineStore.recoverData(userAnnoDetails)
      } else {
        // underlineStore.initRows(dialogue.dataList)
      }

      const keys = Object.keys(dialogue.columnMap)
      for (const key of keys) {
        columns.value.push({
          prop: key,
          label: key,
          width: dialogue.columnNameWidthMap[key],
        })
      }
    }
    data.value.annoStatus = annoStatus
    data.value.userAnnoId = userAnnoId
    data.value.sampleType = sampleType
    data.value.sampleBusiId = sampleBusiId
    data.value.annoStatusName = annoStatusName
    data.value.annotatorName = annotatorName + ''
    data.value.annoMethod = annoMethod
    data.value.tagShowMethod = tagShowMethod
    return true
  } catch (e) {
    console.error(e)
  }
}

const refresh = () => {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    window.rawWindow.location.reload()
  } else {
    window.location.reload()
  }
}

const handlePrevious = async ({ page, viewMode, status }) => {
  try {
    if (await getOneAnnoData(route.query.id, -1)) {
      underlineStore.clearData()
      let path = '/mark'
      if (data.value.sampleType === 1003) {
        if (data.value.annoMethod === 1) {
          path = '/fixed-mark'
        } else {
          path = '/underline-mark'
        }
      }
      await router
        .replace({
          path,
          query: {
            id: data.value.userAnnoId,
            taskId: route.query.taskId,
            status,
            viewMode,
            page,
            taskName: route.query.taskName,
            annoStatus: data.value.annoStatus,
            condition: routeInfo.value.condition,
          },
        })
        .then(() => {
          refresh()
        })
    }
  } catch (e) {
    console.error(e)
  }
}

const handleNext = async ({ page, viewMode, status }) => {
  try {
    if (await getOneAnnoData(route.query.id, 1)) {
      underlineStore.clearData()
      let path = '/mark'
      if (data.value.sampleType === 1003) {
        if (data.value.annoMethod === 1) {
          path = '/fixed-mark'
        } else {
          path = '/underline-mark'
        }
      }
      await router
        .replace({
          path,
          query: {
            id: data.value.userAnnoId,
            taskId: route.query.taskId,
            status,
            viewMode,
            page,
            taskName: route.query.taskName,
            annoStatus: data.value.annoStatus,
            condition: routeInfo.value.condition,
          },
        })
        .then(() => {
          refresh()
        })
    }
  } catch (e) {
    console.error(e)
  }
}

const getAnnotationTerms = async (taskId) => {
  try {
    const res = await getTagListForAnno(taskId)
    qualityList.value = res || []
  } catch (e) {
    console.error(e)
  }
}

const getOptions = async () => {
  for (let index = 0; index < qualityList.value.length; index++) {
    const item = qualityList.value[index]
    if ([0, 6, 7, 8, 9, 10, 11, 12, 13].includes(item.itemType)) {
      const res = await getItemDropDownForAnno(item.itemId)
      optionsCacheStore.setOptionsByItemId(item.itemId, res)
    }
  }
}

const handleAdd = ({ rowIndex, columnIndex, data: highlightItem }) => {
  if (data.value.tagShowMethod === 1) {
    for (let i = 0; i < qualityList.value.length; i++) {
      underlineStore.initRows(rowIndex, columnIndex, [highlightItem])
    }
  } else {
    underlineStore.initRows(rowIndex, columnIndex, [highlightItem])
  }
}

const handleDelete = ({ rowIndex, columnIndex, nodeId, index }) => {
  const tagShowMethod = data.value.tagShowMethod

  underlineStore.deleteOneItemByHighlightId(nodeId, index)
  // 一个划词，多个标注时，标注都删除时，删除该划词
  if (tagShowMethod === 1) {
    if (!underlineStore.getHighlightLeftItemsLength(nodeId)) {
      highlightRefs.value[`${rowIndex}-${columnIndex}`].deleteHighlightedNode(
        nodeId
      )
    }
  } else {
    highlightRefs.value[`${rowIndex}-${columnIndex}`].deleteHighlightedNode(
      nodeId
    )
  }
}

const handleFixedColumn = ({ columnIndex }) => {
  if (columnIndex === columns.value.length) {
    return 'fixed-column'
  }
}

onMounted(async () => {
  loading.value = false
  let { id } = route.query || {}

  await getOneAnnoData(id)
  const { taskId } = route.query
  await getAnnotationTerms(taskId)
  await getOptions()
  loading.value = true
})

onBeforeUnmount(() => {
  underlineStore.clearData()
})
</script>

<style>
.highlight {
  background: red !important;
}
.green {
  background: green !important;
}
.fixed-column {
  border-left: 2px solid #ccc;
}
</style>
