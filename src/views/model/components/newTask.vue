<template>
  <el-dialog
    class="taskAllocation"
    v-model="data.show"
    :title="data.title"
    width="640"
    :before-close="handleClose"
  >
    <div class="px-20px">
      <el-form ref="ruleFormRef" :rules="rules" :model="form" label-width="100">
        <el-form-item label="任务名称:" prop="taskName">
          <el-input
            v-model="form.taskName"
            clearable
            placeholder="请输入任务名称"
          />
        </el-form-item>
        <el-form-item label="样本来源:" prop="sampleSource">
          <el-select
            class="w-100%"
            v-model="form.sampleSource"
            :disabled="!!data.id"
            clearable
            placeholder="请选择"
            @change="onChange('sampleSource')"
          >
            <el-option
              v-for="item in SampleSourceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属应用:" prop="appId">
          <el-select
            class="w-100%"
            v-model="form.appId"
            :disabled="!!data.id"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in ApplicationOptions"
              :key="item.value"
              :label="item.label"
              :value="`${item.value}`"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="评测样本:" prop="evaluationSample">
          <el-select
            class="w-100%"
            v-model="form.evaluationSample"
            :disabled="!!data.id"
            clearable
            placeholder="请选择"
            @change="onChange('evaluationSample')"
          >
            <el-option
              v-for="item in data.evaluationSampleList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="样本数据:" prop="sampleData">
          <el-select
            class="w-100%"
            v-model="form.sampleData"
            :disabled="!!data.id"
            clearable
            multiple
            :multiple-limit="5"
            placeholder="请选择"
            @change="onChange('sampleData')"
          >
            <el-option
              v-for="item in data.sampleDataList"
              :key="item.id"
              :label="`${item.id} (${item.name})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="评测类型:" prop="evaluationTypeId">
          <el-select
            class="w-100%"
            v-model="form.evaluationTypeId"
            :disabled="!!data.id"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in data.evaluationTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="对比类型:" prop="comparisonType">
          <el-radio-group v-model="form.comparisonType" :disabled="!!data.id">
            <el-radio
              v-for="item in comparisonTypeOptions"
              :key="item.value"
              :label="item.value"
              :disabled="getComparisonTypeStatus(item)"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="任务描述:" prop="taskDescription">
          <el-input
            type="textarea"
            rows="4"
            v-model="form.taskDescription"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submit"> 提交 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, onMounted, reactive, ref, toRaw } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, FormInstance, FormRules } from 'element-plus'

import {
  createTask,
  getEvaluationSampleList,
  getSampleDataList,
  updateTaskName,
} from '@/api/session'

import type { IOptionsType, SampleListResType } from '@/common/interface'
import {
  ApplicationEnum,
  ApplicationOptions,
  comparisonTypeOptions,
  EvaluationTypeEnum,
  SampleSourceOptions,
} from '../config/consts'

import { cloneDeep, intersection } from 'lodash-es'

const Route = useRoute()
const Router = useRouter()

let formData = {
  taskName: '',
  sampleSource: '',
  evaluationSample: '',
  sampleData: [] as number[],
  application: '',
  appId: '',
  evaluationTypeId: '',
  comparisonType: 1,
  taskDescription: '',
  username: '',
}

const data = reactive({
  show: false,
  title: '新建任务',
  id: '',
  rows: {
    evaluationTypeId: '',
  },
  evaluationSampleList: [] as SampleListResType[],
  sampleDataList: [] as SampleListResType[],
  evaluationTypeList: [] as IOptionsType[],
})

const form = ref({
  ...cloneDeep(formData),
})
const ruleFormRef = ref<FormInstance>()

const emit = defineEmits<{
  (e: 'change', status: boolean): void
}>()

const rules = reactive<FormRules>({
  taskName: [{ required: true, message: '必填' }],
  sampleSource: [{ required: true, message: '必填' }],
  evaluationSample: [{ required: true, message: '必填' }],
  sampleData: [{ required: true, message: '必填' }],
  appId: [{ required: true, message: '必填' }],
  evaluationTypeId: [{ required: true, message: '必填' }],
  comparisonType: [{ required: true, message: '必填' }],
})

defineExpose({ showDialog })

function getComparisonTypeStatus({ value }) {
  return (
    (value === 1 && form.value.sampleData.length > 1) ||
    (value === 3 && form.value.sampleData.length < 2)
  )
}

async function showDialog(obj) {
  let newObj = toRaw(obj)
  console.log(newObj)
  let { id, sampleSource, evaluationTypeId, sampleData } = newObj
  if (id || sampleSource) {
    data.rows = cloneDeep(newObj)
    data.title = id ? '编辑任务' : '新建任务'
    data.id = id || ''
    form.value = cloneDeep(newObj)
    let sampleDataArr = sampleData.split('|').map((id) => +id)
    form.value.sampleData = sampleDataArr
    form.value.comparisonType = sampleDataArr.length > 1 ? 3 : 1
    await getEvaluationSampleListApi()
    await getSampleDataListApi()
    onChange('sampleData')
    form.value.evaluationTypeId =
      (evaluationTypeId && evaluationTypeId + '') || ''
  } else {
    data.title = '新建任务'
    data.id = ''
    form.value = cloneDeep(formData)
    form.value.sampleData = []
    form.value.comparisonType = 1
  }
  data.show = true
  nextTick(() => {
    ruleFormRef.value?.clearValidate()
  })
}

async function handleClose() {
  console.log('close')
  data.evaluationTypeList = []
  form.value = cloneDeep(formData)
  ruleFormRef.value!.resetFields()
  // await sleep(200)
  data.show = false
  Router.replace({
    path: '/session-evaluation',
  })
}

async function submit() {
  try {
    await ruleFormRef.value!.validate()
    let res: any = null
    if (data.id) {
      // 编辑
      res = await updateTaskNameHandler()
    } else {
      res = await createTaskHandler()
    }
    console.log(res)
    if (res) {
      ElMessage({
        message: '操作成功',
        type: 'success',
      })
      handleClose()
      emit('change', true)
    } else {
      ElMessage({
        message: res.msg || '服务器内部错误',
        type: 'warning',
      })
    }
  } catch (e) {
    console.log(e)
  }
}

function getSampleDataName() {
  let str = ''
  data.sampleDataList.forEach(({ id, name }) => {
    if (form.value.sampleData.includes(id)) {
      str += `${name},`
    }
  })
  return str.slice(0, -1)
}

async function createTaskHandler() {
  try {
    const res = await createTask({
      ...form.value,
      application: ApplicationEnum[form.value.appId],
      evaluationSampleName: data.evaluationSampleList.find(
        ({ id }) => id === +form.value.evaluationSample
      )?.name,
      sampleData: form.value.sampleData.join('|'),
      sampleDataName: getSampleDataName(),
      username: '',
    })
    return res
  } catch (e) {
    console.log(e)
  }
}

async function updateTaskNameHandler() {
  try {
    let res = await updateTaskName({
      id: data.id,
      taskName: form.value.taskName,
    })
    return res
  } catch (e) {
    console.log(e)
  }
}

function onChange(type) {
  if (!form.value.sampleSource) return
  if (type === 'sampleSource') {
    form.value.evaluationSample = ''
    form.value.evaluationTypeId = ''
    form.value.sampleData = []
    data.evaluationTypeList = []
    getEvaluationSampleListApi()
  }

  if (type === 'evaluationSample') {
    form.value.evaluationTypeId = ''
    form.value.sampleData = []
    data.evaluationTypeList = []
    getSampleDataListApi()
  }

  if (type === 'sampleData') {
    console.log(form.value.sampleData)
    form.value.evaluationTypeId = ''
    if (form.value.sampleData.length === 0) {
      data.evaluationTypeList = []
    } else if (form.value.sampleData.length === 1) {
      data.evaluationTypeList =
        data.sampleDataList
          .find(({ id }) => +id === +form.value.sampleData)
          ?.evaluationType?.split(',')
          .map((id) => {
            return {
              value: id,
              label: EvaluationTypeEnum[id],
            }
          }) || []
    } else {
      let arr = data.sampleDataList.filter(({ id }) =>
        form.value.sampleData.includes(id)
      )
      console.log(arr)
      let intersectionRes = intersection(
        ...arr.map(({ evaluationType }) => {
          return evaluationType?.split(',') || []
        })
      )
      console.log(intersectionRes)
      if (intersectionRes.includes('3')) {
        data.evaluationTypeList = [
          {
            value: '3',
            label: EvaluationTypeEnum['3'],
          },
        ]
      } else {
        data.evaluationTypeList = []
        form.value.evaluationTypeId = ''

        let t = setTimeout(() => {
          clearTimeout(t)
          form.value.evaluationTypeId = data.rows.evaluationTypeId
          // ruleFormRef.value?.clearValidate(['evaluationTypeId'])
        }, 200)
      }
    }

    form.value.comparisonType = form.value.sampleData.length > 1 ? 3 : 1
  }
  console.log('evaluationTypeList', data.evaluationTypeList)
}

async function getEvaluationSampleListApi() {
  try {
    let res = await getEvaluationSampleList({
      sampleSource: form.value.sampleSource,
    })
    console.log(res)
    data.evaluationSampleList = res || []
  } catch (e) {
    console.log(e)
  }
}

async function getSampleDataListApi() {
  try {
    let res = await getSampleDataList({
      sampleSource: form.value.sampleSource,
      id: form.value.evaluationSample,
    })
    console.log(res)
    data.sampleDataList = res || []
  } catch (e) {
    console.log(e)
  }
}

onMounted(() => {
  console.log('onMounted')
})
</script>
