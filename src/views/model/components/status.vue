<script lang="ts">
import { FunctionalComponent, h } from 'vue'

import { TaskStatusOptions } from '@/views/model/config/consts'

type FComponentProps = {
  status: number
}

const Status: FunctionalComponent<FComponentProps> = (props, context) => {
  let { status } = props
  let { label, color } = TaskStatusOptions.find(
    (item) => item.value === status
  ) || { label: '未初始化', value: 1, color: '#ccc' }

  return h(
    `div`,
    {
      style: {
        display: 'flex',
        alignItems: 'center',
      },
    },
    [
      h(`span`, {
        style: {
          width: '8px',
          height: '8px',
          borderRadius: '50%',
          backgroundColor: color,
          marginRight: '5px',
        },
      }),
      label,
    ]
  )
}

Status.props = {
  status: {
    type: Number,
    default: 1,
  },
}
export default Status
</script>
