export enum SampleSourceEnum {
  '问题库-任务模块' = 1,
  '标注',
}

export enum ApplicationEnum {
  '问题库' = 1,
  'AIC',
  '智能助手',
  'IM',
}

export enum ConsistencyListEnum {
  '不一致' = 0,
  '一致',
  '包含',
}

export enum ComparisonTypeEnum {
  '模型对比样本' = 1,
  '模型对比标注',
}

export enum TaskStatusEnum {
  '排队中' = 1,
  '进行中',
  '成功',
  '失败',
}

export enum QuestionEnum {
  '公共资源' = 1,
  'FAQ资源',
  '我的资源',
}

export enum EvaluationTypeEnum {
  'AIC意图评测' = 1,
  '实体评测',
  '模型答案评测',
  'IM意图评测',
}

export const SampleSourceOptions = [
  { label: '问题库-任务模块', value: 1 },
  // { label: '标注', value: 2 },
]

export const ApplicationOptions = [
  { label: '问题库', value: 1 },
  { label: 'AIC', value: 2 },
  { label: '智能助手', value: 3 },
  { label: 'IM', value: 4 },
]

export const EvaluationTypeOptions = [
  { label: 'AIC意图评测', value: 1 },
  { label: '实体评测', value: 2 },
  { label: '模型答案评测', value: 3 },
  { label: 'IM意图评测', value: 4 },
]

export const ConsistencyListOptions = [
  { label: '不一致', value: 0 },
  { label: '一致', value: 1 },
  { label: '包含', value: 2 },
  { label: '空', value: 9 },
]

export const ComparisonTypeOptions = [
  { label: '模型对比样本', value: 1 },
  // { label: '模型对比标注', value: 2 },
]

export const EvaluationTypeList = [
  { label: '意图清单', value: '意图' },
  { label: '实体清单', value: '实体' },
]

export const TaskStatusOptions = [
  { label: '排队中', value: 1, color: '#999' },
  { label: '进行中', value: 2, color: '#1890ff' },
  { label: '成功', value: 3, color: '#52c41a' },
  { label: '失败', value: 4, color: '#f5222d' },
]

export const PushRangeOptions = [
  { label: '全量', value: 1 },
  { label: '不一致', value: 2 },
]

export const PushTypeOptions = [
  { label: '更新原问题集', value: 1 },
  { label: '其他问题集', value: 2 },
]

export const QuestionOptions = [
  { label: '公共资源', value: 1 },
  { label: 'FAQ资源', value: 2 },
  { label: '我的资源', value: 3 },
]

// 对比类型 1：模型对比样本 2:模型对比标注3:模型对比模型
export const comparisonTypeOptions = [
  { label: '模型对比样本', value: 1 },
  // { label: '模型对比标注', value: 2 },
  { label: '多模型对比', value: 3 },
]

export const baseColumns = [
  {
    value: 'evaluationTaskId',
    label: '评测任务ID',
    selected: false,
    width: '100',
    type: '1',
  },
  {
    value: 'modelTaskId',
    label: '模型任务ID',
    selected: false,
    width: '100',
    type: '1',
  },
  {
    value: 'questionContentId',
    label: '问题ID',
    selected: true,
    width: '80',
    type: '1',
  },
  {
    value: 'questionContent',
    label: '问题内容',
    selected: true,
    width: '200',
    type: '1',
  },
  // {
  //   value: 'standardBatchId',
  //   label: '样本批次版本ID',
  //   selected: false,
  //   type: '1',
  // },
  {
    value: 'referenceAnswer',
    label: '样本参考答案',
    selected: true,
    width: '100%',
    type: '1',
  },
]

export const resultColumns = [
  {
    value: 'bestModelName',
    label: '优质答案',
    selected: true,
    width: '100%',
    type: '3',
  },
  {
    value: 'reason',
    label: '评测原因',
    selected: true,
    width: '200',
    type: '3',
  },
]
