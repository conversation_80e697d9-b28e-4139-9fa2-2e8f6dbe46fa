<template>
  <div class="session-page bg-white p-20px pt-10px main-h pd-20">
    <!-- <el-tabs
      v-model="data.activeName"
      @tab-click="tabsClick"
      :before-leave="beforeLeave"
    >
      <el-tab-pane label="模型任务" name="1"><br /></el-tab-pane>
      <el-tab-pane label="评测任务" name="2"><br /></el-tab-pane>
    </el-tabs> -->

    <NotAuth v-if="useAuth()" />

    <template v-else>
      <h1>任务列表</h1>

      <el-form
        ref="ruleFormRef"
        :inline="true"
        :model="form"
        class="mt-20px"
        label-width="90"
      >
        <el-form-item label="所属应用:" prop="appId">
          <el-select
            class="w-210px"
            v-model="form.appId"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in ApplicationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="评测类型:" prop="evaluationTypeId">
          <el-select
            class="w-210px"
            v-model="form.evaluationTypeId"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in EvaluationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="任务名称:" prop="taskName">
          <el-input
            clearable
            class="w-210px"
            v-model="form.taskName"
            @keyup.enter="query"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="状态:" prop="taskStatus">
          <el-select
            class="w-210px"
            v-model="form.taskStatus"
            clearable
            placeholder="全部"
          >
            <el-option
              v-for="item in TaskStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建人:" prop="username">
          <el-input
            clearable
            class="w-210px"
            v-model="form.username"
            @keyup.enter="query"
            placeholder="请输入用户中文名或域账号"
          />
        </el-form-item>
        <el-form-item label="创建时间:" prop="appId">
          <el-date-picker
            v-model="form.createAts"
            clearable
            class="w-210px"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 210px"
          />
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" @click="query">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="b-solid b-t-1px b-t-#dcdfe6"></div>
      <div class="my-20px">
        <el-button @click="edit({})" type="primary" class="mr-10px"
          >新建任务</el-button
        >
      </div>
      <el-table :data="data.tableData" style="width: 100%">
        <el-table-column prop="id" label="评测任务ID" width="120">
          <template #default="scope">
            <el-button
              :disabled="scope.row.taskStatus !== 3"
              link
              type="primary"
              @click="view(scope.row)"
            >
              {{ scope.row.id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="taskName" label="任务名称" />
        <el-table-column prop="application" label="所属应用" />
        <el-table-column prop="evaluationTypeId" label="评测类型">
          <template #default="scope">
            <div>{{ EvaluationTypeEnum[scope.row.evaluationTypeId] }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="taskStatus" label="状态" width="100">
          <template #default="scope">
            <Status :status="scope.row.taskStatus" />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="170" />
        <el-table-column prop="username" label="创建用户" />
        <el-table-column fixed="right" prop="id" label="操作" width="180">
          <template #default="scope">
            <el-button
              :disabled="scope.row.taskStatus !== 3"
              link
              type="primary"
              @click="view(scope.row)"
              >查看</el-button
            >
            <el-button link type="primary" @click="edit(scope.row)"
              >编辑</el-button
            >
            <el-button link type="danger" @click="del(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="mt-20px flex flex-end">
        <el-pagination
          background
          v-model:current-page="pag.page"
          v-model:page-size="pag.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, jumper, sizes"
          :total="pag.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </template>
  </div>

  <NewTask ref="newTaskRef" @change="taskChange" />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, toRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'

import Status from './components/status.vue'
import NotAuth from '@/views/401.vue'
import NewTask from '@/views/model/components/newTask.vue'

import { delTask, getTaskList } from '@/api/session'

import type { TaskPagListResType } from '@/common/interface'
import {
  ApplicationOptions,
  EvaluationTypeEnum,
  EvaluationTypeOptions,
  TaskStatusOptions,
} from '@/views/model/config/consts'

import { useAuth } from '@/hooks/useAuth'

import { useUserStore } from '@/store/useUserStore'

// import microApp from '@micro-zoe/micro-app'

const Route = useRoute()
const Router = useRouter()

const store = useUserStore()

const newTaskRef = ref()
const ruleFormRef = ref<FormInstance>()

const data = reactive({
  form: {
    appId: '',
    evaluationTypeId: '',
    taskStatus: '',
    taskName: '',
    username: '',
    createAts: [],
  },
  tableData: [] as TaskPagListResType[],
  activeName: '2',
})

const form = toRef(data, 'form')

let pag = reactive({
  page: 1,
  size: 10,
  total: 0,
})

function beforeLeave(paneName) {
  return !(paneName === '1' && store.isMicroApp)
}

function tabsClick({ paneName }) {
  window.$SDK_ALL.sendPageEvent({
    event_id: 'lantu_click', // 业务字段
    data: {
      button: 'task', // 业务字段
      card: paneName === '1' ? 'task-list' : 'session-evaluation',
    },
  })
  data.activeName = paneName
  if (!store.isMicroApp) return
  if (paneName === '1') {
    try {
      // 获取主应用路由
      const baseRouter = window.microApp.router.getBaseAppRouter()
      baseRouter.push({
        path: '/aso-question/task-list',
      })
    } catch (e) {
      console.log(e)
    }
  }
}

function taskChange() {
  console.log('taskChange')
  getPageList()
}

function reset() {
  console.log('reset')
  ruleFormRef.value?.resetFields()
  form.value.createAts = []
  query()
}

function query() {
  console.log('query')
  pag.page = 1
  getPageList()
}

function view({ id }) {
  Router.push({
    path: '/session-task',
    query: {
      id: id,
    },
  })
}

function edit(row) {
  newTaskRef.value.showDialog(row)
}

function del(row) {
  console.log(row)
  ElMessageBox.confirm('确认删除任务？', '', {
    type: 'warning',
    distinguishCancelAndClose: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(() => {
      delTaskApi(row)
    })
    .catch((action) => {
      console.log(action)
    })
}

async function delTaskApi({ id }) {
  try {
    let { code, msg } = await delTask({ id })
    console.log(code, msg)
    if (code === 0) {
      ElMessage.success('删除成功')
      getPageList()
    } else {
      ElMessage.warning(`删除失败：${msg}`)
    }
  } catch (e) {
    console.log(e)
  }
}

async function getPageList() {
  try {
    let { total, records } = await getTaskList({
      current: pag.page,
      size: pag.size,
      ...data.form,
      createAts: undefined,
      startDate: data.form?.createAts?.length
        ? dayjs(data.form?.createAts[0]).format('YYYY-MM-DD')
        : '',
      endDate: data.form?.createAts?.length
        ? dayjs(data.form?.createAts[1]).format('YYYY-MM-DD')
        : '',
    })
    console.log({ total, records })
    pag.total = total
    data.tableData = records || []
  } catch (e) {
    console.log(e)
  }
}

function handleSizeChange(size) {
  console.log(size)
  pag.size = size
  getPageList()
}

function handleCurrentChange(page) {
  console.log(page)
  pag.page = page
  getPageList()
}

function send() {
  window.localStorage.setItem('message', Date.now() + '')

  // 发送消息加入以下代码
  const channel = new BroadcastChannel('test')
  channel.postMessage('这是一个测试的消息')

  // const targetWindow = window.open(
  //   'http://localhost:1024/annotation/session-task',
  //   '_parent'
  // ) as Window
  // targetWindow.postMessage({ type: 'ready', value: '你好~' }, '*')
}

onMounted(() => {
  getPageList()
  console.log('jinglia', data.activeName)
  window.$SDK_ALL.sendPage({
    event_id: 'lantu_view', // 业务字段
    data: {
      page: 'task', // 业务字段
      card: data.activeName === '1' ? 'task-list' : 'session-evaluation',
    },
  })

  window.$SDK_ALL.sendPageEvent({
    event_id: 'lantu_click', // 业务字段
    data: {
      button: 'task', // 业务字段
      card: data.activeName === '1' ? 'task-list' : 'session-evaluation',
    },
  })
  let { sampleSource, taskId, batchId } = Route.query
  console.log(sampleSource)
  if (sampleSource) {
    edit({
      sampleSource: +sampleSource,
      evaluationSample: +(taskId as string),
      sampleData:
        (typeof batchId === 'string' && batchId?.replace(/,/g, '|')) || '',
    })
  }
})
</script>
