<template>
  <div class="bg-white p-20px main-h">
    <MarkNav :nav-list="navList" />

    <!--    <h1 class="mt-20px">执行详情</h1>-->

    <div class="flex items-center flex-wrap pt-30px">
      <div class="flex items-center pr-30px mb-20px w-270px">
        评测任务ID：
        <div class="truncate w-120px">{{ detail.id }}</div>
      </div>
      <div class="flex items-center pr-30px mb-20px w-320px">
        评测任务名称：
        <el-tooltip
          class="item"
          effect="dark"
          :content="detail.taskName"
          placement="top-start"
          :show-arrow="false"
        >
          <div class="truncate w-160px">
            {{ detail.taskName }}
          </div>
        </el-tooltip>
      </div>
      <div class="flex items-center pr-30px w-320px mb-20px">
        所属应用：
        <div class="w-150px truncate">{{ detail.application }}</div>
      </div>
      <div class="flex items-center pr-30px w-300px mb-20px">
        评测类型：
        <div class="w-150px truncate">
          {{ EvaluationTypeEnum[detail.evaluationTypeId] }}
        </div>
      </div>
      <div class="flex items-center pr-30px w-270px mb-20px">
        状态：
        <Status :status="detail.taskStatus" />
      </div>
      <div class="flex items-center pr-30px w-320px mb-20px">
        样本来源：
        <div class="w-150px truncate">
          {{ SampleSourceEnum[detail.sampleSource] }}
        </div>
      </div>
      <div class="flex items-center pr-30px w-320px mb-20px">
        评测样本：
        <el-tooltip
          class="item"
          effect="dark"
          :content="`${detail.evaluationSample}(${detail.evaluationSampleName})`"
          placement="top-start"
          :show-arrow="false"
        >
          <div class="truncate w-200px">
            {{ `${detail.evaluationSample}(${detail.evaluationSampleName})` }}
          </div>
        </el-tooltip>
      </div>
      <div class="flex items-center pr-30px w-270px mb-20px">
        样本数据：
        <el-tooltip
          class="item"
          effect="dark"
          :content="getSampleDataName"
          placement="top"
          :show-arrow="false"
        >
          <div class="w-150px truncate">
            {{ getSampleDataName }}
          </div>
        </el-tooltip>
      </div>
    </div>

    <div class="b-solid b-t-1px b-t-#dcdfe6"></div>
    <div class="my-20px flex justify-between items-center">
      <h1>评测结果</h1>
      <div class="flex items-center">
        <el-button
          @click="jumpTo('/task-detail')"
          type="primary"
          class="ml-10px"
          >查看明细</el-button
        >
        <el-button
          v-if="data.isAdmin && !isMoreModel"
          @click="jumpTo('/task-push')"
          type="primary"
          class="ml-10px"
          >推送问题库</el-button
        >
        <el-button @click="downloadDataApi" class="ml-10px">
          <el-icon><IEpDownload /></el-icon>
          下载
        </el-button>
      </div>
    </div>
    <el-table :data="data.tableData" style="width: 100%">
      <template v-if="isMoreModel">
        <!--        模型答案评测类型-->
        <el-table-column prop="evaluationName" label="评测模型" />
        <!--        <el-table-column prop="evaluationType" label="模型版本" />-->
        <el-table-column prop="standardAnswersNum" label="标准答案量" />
        <el-table-column prop="compareAnswersNum" label="对比答案量" />
        <el-table-column prop="consistentNum" label="优质答案量" />
        <el-table-column prop="precisionRate" label="精准率" />
        <el-table-column prop="recallRate" label="召回率" />
        <el-table-column prop="f1" label="F1" />
      </template>
      <template v-else>
        <!--        意图、实体-->
        <el-table-column
          prop="evaluationName"
          :label="isYiTu ? '意图' : '实体'"
          width="120"
        />
        <el-table-column prop="standardAnswersNum" label="标准答案量" />
        <el-table-column prop="compareAnswersNum" label="对比答案量" />
        <el-table-column prop="consistentNum" label="一致量" />
        <el-table-column prop="containNum" label="包含量" v-if="!isYiTu" />
        <el-table-column prop="inconsistentNum" label="不一致量" />
        <el-table-column prop="accuracyRate" label="正确率" />
        <el-table-column prop="errorRate" label="错误率" />
        <el-table-column prop="precisionRate" label="精准率" />
        <el-table-column prop="recallRate" label="召回率" />
        <el-table-column prop="f1" label="F1" />
      </template>
    </el-table>
    <div class="flex flex-end mt-20px">
      <el-pagination
        background
        v-model:current-page="pag.page"
        v-model:page-size="pag.size"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, prev, pager, next, jumper, sizes"
        :total="pag.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, toRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import MarkNav from '@/views/mark/components/mark-nav.vue'
import Status from '@/views/model/components/status.vue'

import {
  downloadData,
  getTaskDetail,
  getTaskResList,
  getUserRole,
} from '@/api/session'

import { downloadFile } from '@/common/helper'
import { TaskCreateReqType, TaskListResType } from '@/common/interface'
import { EvaluationTypeEnum, SampleSourceEnum } from './config/consts'

const Route = useRoute()
const Router = useRouter()

const data = reactive({
  isAdmin: false,
  id: '',
  tableData: [] as TaskListResType[],
  detail: {
    id: '',
    taskName: '',
    sampleSource: '',
    evaluationSample: '',
    sampleData: '',
    sampleDataName: '',
    application: '',
    appId: '',
    evaluationTypeId: '',
    comparisonType: 1,
    taskDescription: '',
    username: '',
  } as TaskCreateReqType,
})

const detail = toRef(data, 'detail')

let pag = reactive({
  page: 1,
  size: 10,
  total: 0,
})

const navList = computed(() => {
  return [
    {
      text: '任务列表',
      path: '/session-evaluation',
    },
    {
      text: '执行详情',
      path: '',
    },
  ]
})

const isYiTu = computed(() => {
  return [1, 4].includes(+data.detail.evaluationTypeId)
})

const isMoreModel = computed(() => {
  return data.detail.evaluationTypeId === 3
})

const getSampleDataName = computed(() => {
  let ids = detail.value.sampleData.split('|')
  let names = detail.value?.sampleDataName?.split(',') || []

  let str = ids.reduce((prev, cur, index) => {
    return prev + `${cur}(${names[index] || 'null'}),`
  }, '')

  return str.slice(0, -1)
})

function getEevaluationData({ evaluationName }, i) {
  return evaluationName.split('-')[i]
}

async function getTaskDetailApi() {
  try {
    let res = await getTaskDetail({
      id: data.id,
    })
    console.log(res)
    data.detail = res || {}
  } catch (e) {
    console.log(e)
  }
}

async function downloadDataApi() {
  try {
    let res = await downloadData({
      taskId: data.id,
    })
    // console.log(res)
    downloadFile(res.data, '评测结果数据.xlsx')
  } catch (e) {
    console.log(e)
  }
}

async function getPageList() {
  try {
    let { total, records } = await getTaskResList({
      current: pag.page,
      size: pag.size,
      taskId: data.id,
    })
    console.log({ total, records })
    pag.total = total
    data.tableData = records || []
  } catch (e) {
    console.log(e)
  }
}

function handleSizeChange(size) {
  console.log(size)
  pag.size = size
  getPageList()
}

function handleCurrentChange(page) {
  console.log(page)
  pag.page = page
  getPageList()
}

function jumpTo(path) {
  console.log(path)
  Router.push({
    path,
    query: {
      id: data.id,
      evaluationType: detail.value.evaluationTypeId,
    },
  })
}

async function getUserRoleApi() {
  try {
    let res = await getUserRole()
    console.log(res)
    data.isAdmin = res === 1
  } catch (e) {
    console.log(e)
  }
}

onMounted(() => {
  console.log('onMounted')
  let { id } = Route.query
  data.id = (id || '') as string
  getTaskDetailApi()
  getPageList()
  getUserRoleApi()

  window.addEventListener('storage', (data) => {
    console.log(data)
  })

  // 发送消息加入以下代码
  const channel = new BroadcastChannel('test')

  // 接收消息加入以下代码
  channel.addEventListener('message', (event) => {
    console.log('来活儿了:', event.data)
  })

  window.addEventListener('message', (e) => {
    if (e.data.type === 'ready') {
      console.log(e.data.value)
    }
  })
})
</script>
