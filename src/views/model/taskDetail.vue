<template>
  <div class="bg-white p-20px main-h">
    <MarkNav :nav-list="navList" />

    <!--    <h1 class="mt-20px">明细数据</h1>-->

    <el-form :inline="true" :model="form" class="mt-30px" label-width="90">
      <template v-if="data.evaluationType !== '3'">
        <el-form-item label="对比类型:">
          <el-select
            class="w-210px"
            v-model="form.comparisonType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in ComparisonTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="对比结果:">
          <el-select
            class="w-300px"
            v-model="form.consistencyList"
            multiple
            clearable
            placeholder="全部"
            @change="onChange('consistencyList')"
          >
            <el-option
              v-for="item in ConsistencyListOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item label="对比类型:">
          <el-select
            class="w-210px"
            v-model="form.comparisonType"
            placeholder="请选择"
          >
            <el-option label="多模型对比" :value="3" />
          </el-select>
        </el-form-item>
      </template>
    </el-form>
    <!--    <div class="b-solid b-t-1px b-t-#dcdfe6 mb-20px"></div>-->

    <div v-if="isMoreModel" class="mb-10px">
      <!--      设置表头：-->
      <el-popover placement="right-start" width="300" trigger="click">
        <template #reference>
          <el-button circle>
            <el-icon size="16"><IEpSetting /></el-icon>
          </el-button>
        </template>
        <div class="w-100px">
          <el-checkbox
            v-model="data.checkAll"
            :indeterminate="data.isIndeterminate"
            @change="handleCheckAllChange"
            label="all"
          >
            全选
          </el-checkbox>
          <el-checkbox-group
            v-model="data.checkList"
            @change="handleCheckChange"
          >
            <el-checkbox
              :label="item.value"
              v-for="item in data.thList"
              :key="item.value"
            >
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-popover>
    </div>
    <el-table
      class="task-detail-table"
      :data="data.tableData"
      style="width: 100%"
    >
      <template v-if="isMoreModel">
        <!--        模型答案评测类型-->
        <!--        <el-table-column label="评测样本">-->
        <template v-for="item in baseColumns">
          <el-table-column
            v-if="showTh(item.value)"
            :key="item.value"
            :prop="item.value"
            :label="item.label"
            :width="item.width"
          />
        </template>
        <!--        </el-table-column>-->
        <!--        <el-table-column label="评测模型答案">-->
        <template v-for="item in data.modelAnswerTh">
          <el-table-column
            v-if="showTh(item.label)"
            :key="item.label"
            :prop="item.label"
            :label="item.label"
            width="200"
          />
        </template>
        <!--        </el-table-column>-->
        <!--        <el-table-column label="评测结果">-->
        <template v-for="item in resultColumns">
          <el-table-column
            v-if="showTh(item.value)"
            :key="item.value"
            :prop="item.value"
            :label="item.label"
            :width="item.width"
          />
        </template>
        <!--        </el-table-column>-->
      </template>
      <template v-else>
        <!--        意图、实体-->
        <el-table-column
          prop="evaluationTaskId"
          label="评测任务ID"
          width="100"
        />
        <el-table-column prop="modelTaskId" label="模型任务ID" width="100" />
        <el-table-column prop="contentId" label="问题ID" width="80" />
        <el-table-column prop="content" label="问题内容" />
        <el-table-column prop="standardBatchId" label="样本批次版本ID" />

        <template v-if="isYiTu">
          <el-table-column
            prop="standardIntentionId"
            label="问题意图ID"
            width="100"
          />
          <el-table-column prop="standardIntentionName" label="问题意图名称" />
          <el-table-column
            prop="compareBatchId"
            label="模型任务批次版本ID"
            width="100"
          />
          <el-table-column
            prop="compareIntentionId"
            label="模型意图ID"
            width="100"
          />
          <el-table-column
            prop="compareIntentionName"
            label="模型意图名称"
            width="120"
          />
          <el-table-column
            prop="intentConsistency"
            label="意图一致性"
            width="100"
          >
            <template #default="scope">
              <div>{{ ConsistencyListEnum[scope.row.intentConsistency] }}</div>
            </template>
          </el-table-column>
        </template>

        <template v-else>
          <el-table-column prop="standardEntityName" label="问题实体名称" />
          <el-table-column prop="compareBatchId" label="模型任务批次版本ID" />
          <el-table-column prop="compareEntityName" label="模型实体名称" />
          <el-table-column
            prop="entityConsistency"
            label="实体一致性"
            width="100"
          >
            <template #default="scope">
              <div>{{ ConsistencyListEnum[scope.row.entityConsistency] }}</div>
            </template>
          </el-table-column>
        </template>
      </template>
    </el-table>
    <div class="flex flex-end mt-20px">
      <el-pagination
        background
        v-model:current-page="pag.page"
        v-model:page-size="pag.size"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, prev, pager, next, jumper, sizes"
        :total="pag.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, toRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import MarkNav from '@/views/mark/components/mark-nav.vue'

import { getModelDetailList, getTaskDetailList } from '@/api/session'

import {
  DetailColumnsType,
  NumberOrString,
  TaskDetailPagListResType,
} from '@/common/interface'
import {
  baseColumns,
  ComparisonTypeOptions,
  ConsistencyListEnum,
  ConsistencyListOptions,
  resultColumns,
} from '@/views/model/config/consts'

const Route = useRoute()
const Router = useRouter()

const data = reactive({
  id: '',
  evaluationType: '',
  tableData: [] as TaskDetailPagListResType[],
  form: {
    consistencyList: [0, 2] as NumberOrString[],
    comparisonType: 1,
  },
  checkList: [] as string[],
  checkAll: false,
  isIndeterminate: true,
  thList: [] as DetailColumnsType[],
  modelAnswerTh: [] as DetailColumnsType[],
})

const form = toRef(data, 'form')

let pag = reactive({
  page: 1,
  size: 10,
  total: 0,
})

const navList = computed(() => {
  return [
    {
      text: '任务列表',
      path: '/session-evaluation',
    },
    {
      text: '执行详情',
      path: `/session-task?id=${data.id}`,
    },
    {
      text: '明细数据',
      path: '',
    },
  ]
})

const isYiTu = computed(() => {
  return ['1', '4'].includes(data.evaluationType)
})

const isMoreModel = computed(() => {
  return data.evaluationType === '3'
})

function showTh(key) {
  return data.checkList.includes(key)
}

function handleCheckAllChange(val) {
  console.log(val)
  data.checkAll = val
  data.isIndeterminate = false
  if (val) {
    data.checkList = data.thList.map((item) => item.value)
  } else {
    data.checkList = []
  }
}

function handleCheckChange(val) {
  console.log(val)
  if (data.thList.length === val.length) {
    data.checkAll = true
    data.isIndeterminate = false
  } else if (val.length > 0) {
    // 半选
    data.checkAll = false
    data.isIndeterminate = true
  } else {
    // 没选
    data.checkAll = false
    data.isIndeterminate = false
  }
}

function onChange(type) {
  console.log(type)
  pag.page = 1
  getPageList()
}

async function getPageList() {
  try {
    let api = isMoreModel.value ? getModelDetailList : getTaskDetailList
    let { total, records } = await api({
      current: pag.page,
      size: pag.size,
      evaluationTaskId: data.id,
      ...data.form,
      consistencyList: form.value.consistencyList + '',
    })
    console.log({ total, records })
    pag.total = total
    records = records || []
    if (isMoreModel.value) {
      data.tableData = records.map((item, index) => {
        let { modelAnswerList } = item
        let columns = {}
        let modelTh = modelAnswerList.map((item) => {
          let { modelName, modelAnswer } = item
          columns[modelName] = modelAnswer
          return {
            value: modelName,
            label: modelName,
            selected: true,
            type: '2',
          }
        })
        data.modelAnswerTh = modelTh as DetailColumnsType[]

        return {
          ...item,
          ...columns,
        }
      })
      console.log(data.modelAnswerTh)
      let allList = [
        ...baseColumns,
        ...data.modelAnswerTh,
        ...resultColumns,
      ] as DetailColumnsType[]
      data.checkList = allList.reduce(
        (prev: string[], item: DetailColumnsType) => {
          if (item.selected) {
            prev.push(item.value)
          }
          return prev
        },
        []
      )
      data.thList = allList
      console.log(data.checkList)
    } else {
      data.tableData = records
    }
    console.log(data.tableData)
  } catch (e) {
    console.log(e)
  }
}

function handleSizeChange(size: number) {
  console.log(size)
  pag.size = size
  getPageList()
}

function handleCurrentChange(page: number) {
  console.log(page)
  pag.page = page
  getPageList()
}

function init() {
  if (isMoreModel.value) {
    data.form.comparisonType = 3
    data.form.consistencyList = []
  } else {
    data.form.comparisonType = 1
    data.form.consistencyList = [0, 2]
  }
}

onMounted(() => {
  console.log('onMounted')
  let { id, evaluationType } = Route.query
  data.id = (id || '') as string
  data.evaluationType = (evaluationType || '') as string
  init()
  getPageList()
})
</script>
