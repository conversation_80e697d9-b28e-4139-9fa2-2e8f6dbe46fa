<template>
  <div class="bg-white p-20px main-h">
    <MarkNav :nav-list="navList" />

    <!--    <h1 class="mt-20px">推送问题库</h1>-->

    <div class="w-400px mx-auto py-20px">
      <el-steps :active="data.step" align-center finish-status="finish">
        <el-step title="结果修正" :status="data.step === 2 ? 'success' : ''" />
        <el-step title="推送配置" />
      </el-steps>
    </div>

    <div v-if="data.step === 1">
      <div class="mb-10px">评测结果不一致条数：{{ pag.total }} 条</div>
      <el-table :data="data.tableData" style="width: 100%">
        <el-table-column prop="contentId" label="问题ID" width="80" />
        <el-table-column prop="content" label="问题内容" />

        <template v-if="isYiTu">
          <el-table-column prop="standardIntentionId" label="问题意图ID" />
          <el-table-column prop="standardIntentionName" label="问题意图名称" />
          <el-table-column
            prop="compareIntentionId"
            label="模型意图ID"
            width="100"
          />
          <el-table-column
            prop="compareIntentionName"
            label="模型意图名称"
            width="120"
          />
        </template>

        <template v-else>
          <el-table-column prop="standardEntityName" label="问题实体名称" />
          <el-table-column prop="compareEntityName" label="模型实体名称" />
          <el-table-column
            prop="entityConsistency"
            label="实体一致性"
            width="100"
          >
            <template #default="scope">
              <div>{{ ConsistencyListEnum[scope.row.entityConsistency] }}</div>
            </template>
          </el-table-column>
        </template>
        <el-table-column
          prop="evaluationType"
          :label="isYiTu ? '意图修正' : '实体修正'"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            <el-select
              v-if="isYiTu"
              class="w-100%"
              v-model="scope.row.intentCorrection"
              clearable
              filterable
              placeholder="请选择"
              @change="onChange(scope.row)"
            >
              <el-option-group
                v-for="group in data.intentionIdList"
                :key="group.label"
                :label="group.label"
              >
                <el-option
                  v-for="item in group.options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-option-group>
              <!--              <el-option-->
              <!--                v-for="value in data.intentionIdList"-->
              <!--                :key="value"-->
              <!--                :label="value"-->
              <!--                :value="value"-->
              <!--              />-->
            </el-select>
            <el-input
              v-else
              clearable
              class="w-100%"
              v-model="scope.row.entityCorrection"
              placeholder="请输入"
              @change="onChange(scope.row)"
            />
          </template>
        </el-table-column>
      </el-table>
      <div class="flex flex-end mt-20px">
        <el-pagination
          background
          v-model:current-page="pag.page"
          v-model:page-size="pag.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, jumper, sizes"
          :total="pag.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <el-form
      v-if="data.step === 2"
      ref="ruleFormRef"
      :rules="rules"
      :model="form"
      class="mt-20px"
      label-width="150"
    >
      <el-form-item label="推送问题集:" prop="pushType">
        <el-radio-group v-model="form.pushType">
          <el-radio
            v-for="{ value, label } in PushTypeOptions"
            :key="value"
            :label="value"
          >
            {{ label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="+form.pushType === 2"
        label="选择问题集:"
        prop="newCollId"
      >
        <el-cascader
          v-model="form.newCollId"
          :options="data.queList"
          :props="{ emitPath: false }"
          class="w-500px"
        />
      </el-form-item>
      <el-form-item label="推送范围:" prop="pushRange">
        <el-radio-group v-model="form.pushRange">
          <el-radio
            v-for="{ value, label } in PushRangeOptions"
            :key="value"
            :label="value"
          >
            {{ label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <div class="flex justify-center items-center pt-50px">
      <el-button class="mt-20px" @click="Router.go(-1)">取消</el-button>
      <el-button v-if="data.step === 1" class="mt-20px" @click="stepTo(2)"
        >下一步</el-button
      >
      <template v-else>
        <el-button class="mt-20px" @click="stepTo(1)">上一步</el-button>
        <el-button type="primary" class="mt-20px" @click="submit"
          >确定</el-button
        >
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, toRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  CascaderOption,
  ElMessage,
  FormInstance,
  FormRules,
} from 'element-plus'

import MarkNav from '@/views/mark/components/mark-nav.vue'

import {
  getIntentionIdList,
  getQueList,
  getTaskDetailList,
  pushQuestionManage,
  setCorrection,
} from '@/api/session'

import { TaskDetailPagListResType } from '@/common/interface'
import {
  ConsistencyListEnum,
  PushRangeOptions,
  PushTypeOptions,
  QuestionOptions,
} from '@/views/model/config/consts'

const Route = useRoute()
const Router = useRouter()

const data = reactive({
  step: 1,
  id: '',
  evaluationType: '',
  intentionIdList: [] as any[],
  queList: [] as CascaderOption[],
  tableData: [] as TaskDetailPagListResType[],
  form: {
    pushRange: '',
    pushType: '',
    newCollId: '',
  },
})

const form = toRef(data, 'form')
const ruleFormRef = ref<FormInstance>()

const rules = reactive<FormRules>({
  pushRange: [{ required: true, message: '必填' }],
  pushType: [{ required: true, message: '必填' }],
  newCollId: [{ required: true, message: '必填' }],
})

let pag = reactive({
  page: 1,
  size: 10,
  total: 0,
})

const navList = computed(() => {
  return [
    {
      text: '任务列表',
      path: '/session-evaluation',
    },
    {
      text: '执行详情',
      path: `/session-task?id=${data.id}`,
    },
    {
      text: '推送问题库',
      path: '',
    },
  ]
})

const isYiTu = computed(() => {
  return ['1', '4'].includes(data.evaluationType)
})

async function onChange({ id, intentCorrection, entityCorrection }) {
  // if (isYiTu.value && intentCorrection?.trim() === '') {
  //   // ElMessage({
  //   //   message: '意图修正必填',
  //   //   type: 'warning',
  //   // })
  //   return
  // }
  // if (data.evaluationType === '2' && entityCorrection?.trim() === '') {
  //   return
  // }

  try {
    let res = await setCorrection({
      detailId: id,
      evaluationTypeId: data.evaluationType,
      correction: isYiTu.value ? intentCorrection : entityCorrection,
    })
    console.log(res)
  } catch (e) {
    console.log(e)
  }
}

async function getIntentionIdListApi() {
  try {
    let res = await getIntentionIdList()
    data.intentionIdList =
      Object.entries(res).map(([key, value]) => {
        return {
          label: key,
          options: value.map((item) => ({
            value: item,
            label: item,
          })),
        }
      }) || []
  } catch (e) {
    console.log(e)
  }
}

async function getQueListApi() {
  try {
    let res = await getQueList()
    console.log(res)
    data.queList = QuestionOptions.map(({ label, value }) => ({
      value: value,
      label: label,
      children: res[value].map(({ id, collName }) => ({
        value: id,
        label: collName,
      })),
    }))
  } catch (e) {
    console.log(e)
  }
}

async function getPageList() {
  try {
    let { total, records } = await getTaskDetailList({
      current: pag.page,
      size: pag.size,
      evaluationTaskId: data.id,
      consistencyList: '0,2',
    })
    console.log({ total, records })
    pag.total = total
    data.tableData = records || []
  } catch (e) {
    console.log(e)
  }
}

function handleSizeChange(size) {
  console.log(size)
  pag.size = size
  getPageList()
}

function handleCurrentChange(page) {
  console.log(page)
  pag.page = page
  getPageList()
}

function stepTo(step) {
  console.log(step)
  if (step === 2) {
    getQueListApi()
  }
  data.step = step
}

async function submit() {
  try {
    await ruleFormRef!.value!.validate()
    let res = await pushQuestionManage({
      ...data.form,
      evaluationTaskId: data.id,
    })
    console.log(res)
    if (res.code === 0) {
      ElMessage({
        message: '操作成功',
        type: 'success',
      })
      Router.go(-1)
    } else {
      ElMessage({
        message: res.msg || '服务器内部错误',
        type: 'warning',
      })
    }
  } catch (e) {
    console.log(e)
  }
}

onMounted(() => {
  console.log('onMounted')
  let { id, evaluationType } = Route.query
  data.id = (id || '') as string
  data.evaluationType = (evaluationType || '') as string
  getPageList()
  getIntentionIdListApi()
})
</script>
