<template>
  <div class="create-prompt-page">
    <el-breadcrumb style="margin: 10px 0 20px" :separator-icon="ArrowRight">
      <el-breadcrumb-item :to="{ path: '/prompt-evaluation' }">
        Prompt评测列表
      </el-breadcrumb-item>
      <el-breadcrumb-item>{{ numberValidateForm.flowName }}</el-breadcrumb-item>
    </el-breadcrumb>
    <main class="page-content">
      <div class="mb-[10px] text-[16px]">基础信息</div>
      <el-form
        ref="formRef"
        label-suffix="："
        :model="numberValidateForm"
        class="demo-ruleForm"
      >
        <div class="flex">
          <el-form-item
            label="任务名称"
            prop="flowName"
            :rules="[{ required: true, message: '请输入任务名称' }]"
          >
            <el-input
              class="w-[300px]"
              v-model="numberValidateForm.flowName"
              type="text"
              placeholder="请输入任务名称"
              autocomplete="off"
            />
          </el-form-item>

          <el-button
            :disabled="!numberValidateForm.flowName"
            class="bg-[#4455ff] ml-[20px]"
            type="primary"
            @click="submitForm(formRef)"
          >
            确 认
          </el-button>
        </div>
      </el-form>
      <div v-if="flowId">
        <div
          class="mt-[30px] mb-[10px] flex items-center justify-between mt-25px"
        >
          <div class="text-[16px]">任务视图</div>
          <div class="flex items-center">
            <el-button type="text" @click="handleDownload">
              <el-icon size="18px"><Download /></el-icon> 下载数据
            </el-button>
          </div>
        </div>
        <div
          v-if="!flowId || !tableData.length"
          class="flex w-[100%] justify-center flex-col items-center border-dashed border-[#d4d4d8] border-[1px] rounded-[8px] h-[200px] cursor-pointer"
        >
          <div class="color-[#666666] text-[14px]">添加节点</div>
          <el-button
            class="w-[100px] min-w-[60px] h-[60px] text-[30px]"
            type="success"
            circle
            icon="Plus"
            @click="configNode"
          ></el-button>
        </div>

        <div v-else-if="tableData.length">
          <el-table :data="tableData">
            <el-table-column
              type="index"
              label="序号"
              v-if="cloneColumnGroupList"
              v-for="item in [...new Set(Object.values(cloneColumnGroupList))]"
              :key="item"
            >
              <template #header>
                <!-- {{item}} -->
                <div
                  :class="`flex justify-center bg-[${lightColors[item]}] p-[14px]`"
                >
                  <el-tooltip
                    v-if="
                      findAction(item) === 'READY' ||
                      findAction(item) === 'FAILED'
                    "
                    effect="dark"
                    :content="item === 1 ? '启动全部跑批' : `启动${item}跑批`"
                  >
                    <el-button
                      @click="startFlow(item)"
                      type="primary"
                      circle
                      icon="VideoPlay"
                      class="text-[30px]"
                    >
                    </el-button>
                  </el-tooltip>

                  <el-tooltip
                    v-if="findAction(item) === 'RUNNING'"
                    effect="dark"
                    content="停止跑批"
                  >
                    <el-button
                      @click="stopFlow(item)"
                      type="primary"
                      circle
                      icon="VideoPause"
                      class="text-[30px]"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip effect="dark" content="重新上传第一列">
                    <el-button
                      v-if="item === 1"
                      @click="replaceColumn(item)"
                      type="primary"
                      circle
                      icon="More"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip effect="dark" :content="`编辑第${item}列`">
                    <el-button
                      v-if="item !== 1"
                      @click="detail(item)"
                      type="primary"
                      circle
                      icon="EditPen"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip
                    effect="dark"
                    :content="item === 1 ? '删除全部列' : `删除第${item}列`"
                  >
                    <el-button
                      @click="delColumn(item)"
                      type="warning"
                      circle
                      icon="CloseBold"
                    >
                    </el-button>
                  </el-tooltip>
                </div>
              </template>
              <el-table-column
                v-for="column in columnGroupList.filter(
                  (e) => e.value === item
                )"
                :key="column.key"
                :prop="column.key"
                :label="column.value"
                align="center"
                width="135"
              >
                <template #header>
                  <div
                    :class="`bg-[${lightColors[item]}] px-[14px] py-[6px] h-[50px]`"
                  >
                    {{ column.key }}
                  </div>
                </template>
                <template #default="scope">
                  <el-tooltip placement="bottom" effect="light">
                    <template #content>
                      <div
                        class="max-w-[500px] overflow-auto whitespace-pre-wrap max-h-[300px] break-all"
                      >
                        {{ scope.row[column.key] }}
                      </div>
                    </template>

                    <div class="ellipsis">{{ scope.row[column.key] }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column prop="add" fixed="right" width="140">
              <template #header>
                <div
                  class="flex justify-center items-center h-[108px] bg-[#E8F5E9] p-[14px]"
                >
                  <el-button
                    @click="configNode"
                    type="success"
                    round
                    icon="CirclePlus"
                  >
                    添加节点
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
              :current-page="pagination.page"
              v-model:page-size="pagination.size"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, prev, pager, next, jumper,sizes"
              :total="pagination.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </main>
    <AddPrompt
      :flowId="flowId"
      :stepOrder="stepOrder"
      :columnNumber="columnNumber"
      @fetchList="fetchEmptyList"
      @actionList="getActionList"
      ref="addPromptRef"
    />
  </div>
</template>

<script lang="ts" setup>
import { defineExpose, onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'

import AddPrompt from './add-prompt.vue'

import {
  createFlow,
  deleteAction,
  downLoadDetailsByFlowId,
  getActionListByFlowId,
  getColumnGroupMap,
  getFlowActionDetailByFlowId,
  getFlowDetailList,
  startFlowAction,
  stopFlowAction,
} from '@/api/prompt'

import { ArrowRight } from '@element-plus/icons-vue'
const lightColors = [
  '#E8F5E9',
  '#C8E6C9',
  '#A5D6A7',
  '#81C784',
  '#66BB6A',
  '#4CAF50',
  '#43A047',
  '#388E3C',
  '#2E7D32',
  '#1B5E20',
]
const formRef = ref<FormInstance>()
const flowId = ref(null)
const stepOrder = ref(undefined)
const actionList = ref([])
const timer = ref(null)
let addPromptRef = ref(null)
const Route = useRoute()
const tableData = ref([])
const columnGroupList = ref([])
const cloneColumnGroupList = ref(null)
const columnNumber = ref(1)
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
})

const numberValidateForm = reactive({
  flowName: null,
})
function handleSizeChange(size) {
  fetchList()
}
const handleCurrentChange = (val: number) => {
  pagination.value.page = val
  fetchList()
}

const startFlow = (item) => {
  ElMessageBox.confirm(
    item === 1 ? '确认启动全部跑批？' : `确认启动第${item}列？`,
    '',
    {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }
  )
    .then(async () => {
      const res = await startFlowAction({
        flowId: flowId.value,
        stepOrder: item,
      })
      if (res.code === -1) {
        return ElMessage.error(res.msg)
      } else {
        getActionList()
        // ElMessage({ type: 'success', message: '启动成功' });
      }
    })
    .catch(() => {})
}

const findAction = (item) => {
  if (!actionList.value) {
    return ''
  }
  const action = actionList.value.filter((e) => e.stepOrder === item)
  if (!action) {
    return ''
  }
  return action[0]?.stepStatus
}

const stopFlow = (item) => {
  ElMessageBox.confirm(
    item === 1 ? '确认停止全部跑批？' : `确认暂停第${item}列？`,
    '',
    {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }
  )
    .then(async () => {
      const res = await stopFlowAction({
        flowId: flowId.value,
        stepOrder: item,
      })
      if (res.code === -1) {
        return ElMessage.error(res.msg)
      } else {
        getActionList()
        // ElMessage({ type: 'success', message: '启动成功' });
      }
    })
    .catch(() => {})
}

const handleDownload = async () => {
  const res = await downLoadDetailsByFlowId(flowId.value)

  if (res) {
    const blob = new Blob([res.data], {
      type: 'application/vnd.ms-excel;charset=utf-8',
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('id', 'downloadLink')
    link.setAttribute(
      'download',
      `${numberValidateForm.flowName}_${flowId.value}.xlsx`
    )
    document.body.appendChild(link)
    link.click()
    const objLink = document.getElementById('downloadLink')
    objLink && document.body.removeChild(objLink)
    ElMessage({
      type: 'success',
      message: '导出成功',
    })
  }
}

async function delColumn(id) {
  try {
    await ElMessageBox.confirm(
      id === 1 ? '确认删除全部列？' : `确认删除第${id}列？`,
      '',
      {
        type: 'warning',
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }
    )

    const res = await deleteAction({
      flowId: flowId.value,
      stepOrder: id,
    })
    if (res.code === -1) {
      return ElMessage.error(res.msg || '列删除失败')
    } else {
      ElMessage.success(`第${id}列删除成功！`)
      fetchEmptyList()
    }
  } catch (e) {}
}
async function detail(item) {
  try {
    let res = await getFlowActionDetailByFlowId(flowId.value, item)
    if (res.code === -1) {
      return ElMessage.error(res.msg)
    } else {
      console.log(item)
      stepOrder.value = item.stepOrder
      addPromptRef.value.dialogData.title = `编辑第${item}列节点`
      addPromptRef.value.initData(res)
    }
  } catch (error) {}
}
function fetchEmptyList() {
  pagination.value.page = 1
  fetchList()
}

async function fetchList() {
  try {
    await fetchColumnGroupList()
    const res = await getFlowDetailList({
      page: pagination.value.page,
      size: pagination.value.size,
      flowId: flowId.value,
    })
    if (res.code === -1) {
      return ElMessage.error(res.msg)
    } else {
      if (cloneColumnGroupList.value?.length) {
        pagination.value.total = res.total
        tableData.value = res.list
      } else {
        pagination.value.total = 0
        tableData.value = []
      }
    }
  } catch (e) {}
}
async function fetchColumnGroupList() {
  try {
    const res = await getColumnGroupMap({
      flowId: flowId.value,
    })
    if (res.code === -1) {
      return ElMessage.error(res.msg)
    } else {
      let arr = []
      columnGroupList.value = res
      for (let i in columnGroupList.value) {
        let value = columnGroupList.value[i].value
        if (!arr.includes(value)) {
          arr.push(value)
        }
      }
      cloneColumnGroupList.value = arr
    }
  } catch (e) {}
}

async function getActionList() {
  const res = await getActionListByFlowId({ flowId: flowId.value })
  if (res.code === -1) {
    return ElMessage.error(res.msg)
  } else {
    actionList.value = res
  }
}
async function configNode() {
  let num = 1
  if (tableData.value.length > 0) {
    const list = cloneColumnGroupList.value
      ? [...new Set(Object.values(cloneColumnGroupList.value))]
      : []
    num = list.length ? Math.max(...list) + 1 : 1
  }
  if (!flowId.value) {
    ElMessage.warning('请先输入任务名称')
    await formRef.value.validate()
    return
  }
  columnNumber.value = num
  addPromptRef.value.dialogData.title = '添加节点'
  addPromptRef.value.initData()
}

const replaceColumn = (item) => {
  columnNumber.value = 1
  addPromptRef.value.dialogData.title = '替换第一节点'
  addPromptRef.value.initData()
}

const submitForm = async (formEl: any | undefined) => {
  try {
    if (!formEl) return
    await formEl.validate()
    const res = await createFlow(numberValidateForm.flowName, flowId.value)
    if (res.code === -1) {
      return ElMessage.error(res.msg || '任务名称失败')
    } else {
      ElMessage.success(
        flowId.value ? '修改成功！' : '任务创建成功，请添加节点！'
      )
      flowId.value = res
    }
  } catch (e) {}
}

const initData = () => {
  if (!Route.query || !Route.query.id || !Route.query.id) {
    return
  }
  flowId.value = Route.query.id
  numberValidateForm.flowName = Route.query.flowName
  fetchList()
}

onMounted(async () => {
  await initData()
  await getActionList()
  timer.value = setInterval(() => {
    if (flowId.value && tableData.value.length) {
      fetchList()
      getActionList()
    }
  }, 5000)
})

onUnmounted(() => {
  clearInterval(timer.value)
  timer.value = null
})
defineExpose({
  configNode,
})
</script>

<style lang="less">
.create-prompt-page {
  .page-header {
    background: #4455ff;
    color: white;
    padding: 20px;
    margin-bottom: 20px;
  }

  .page-content {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 130px);

    .el-table__header {
      padding: 0px !important;
      .el-table__cell {
        padding: 0px !important;

        .cell {
          padding: 0px !important;
        }
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: end;
    margin-top: 40px;
  }
}
</style>
