<template>
  <div class="flex items-top mt-[20px]">
    <div class="ml-[30px] mr-[20px]">文件类型:</div>
    <el-upload
      v-if="dialogShow"
      ref="uploadRef"
      action=""
      :auto-upload="false"
      accept="xls,xlsx,csv"
      :limit="1"
      :file-list="filesList"
      :on-exceed="handleExceed"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
    >
      <template #trigger>
        <el-button type="primary">上传文件</el-button>
      </template>
      <template #tip>
        <div class="flex flex-col">
          <span>单次允许上传一个文件，支持扩展名: .xls .xlsx</span>
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, onBeforeUnmount, ref } from 'vue'
import { defineProps } from 'vue'

import type { UploadUserFile } from 'element-plus'

const emit = defineEmits(['fileChange'])
const filesList = ref<UploadUserFile[]>([])

const props = defineProps({
  dialogShow: {
    type: Boolean,
    default: false,
  },
})

const handleExceed = (files: File[], uploadFiles: UploadUserFile[]) => {
  if (files.length) {
    filesList.value = files
    emit('fileChange', { file: files[0] })
  }
}

const handleFileChange = (
  uploadFile: UploadUserFile,
  uploadFiles: UploadUserFile[]
) => {
  filesList.value = [uploadFile]
  emit('fileChange', { file: uploadFile })
}

function handleFileRemove() {
  filesList.value = []
  emit('fileChange', { file: null })
}

onBeforeUnmount(() => {
  filesList.value = []
})
</script>
