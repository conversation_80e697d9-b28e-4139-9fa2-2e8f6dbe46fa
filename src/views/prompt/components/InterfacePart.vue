<template>
  <div class="mt-[30px]">
    <el-form-item label="接口路径:">
      <el-input @change="interfaceDataChange" class="w-[400px]" v-model="interfaceData.url" placeholder="输入接口URL" />
    </el-form-item>

    <el-form-item label="method:">
      <el-radio-group @change="interfaceDataChange" v-model="interfaceData.method">
        <el-radio label="GET" value="GET" />
        <el-radio label="POST" value="POST" />
      </el-radio-group>
    </el-form-item>

    <el-form-item class="mt-[15px]" label="请求头:">
      <el-input @change="interfaceDataChange" :autosize="{ minRows: 1, maxRows: 3 }" type="textarea" class="w-[400px]"
        v-model="interfaceData.headers" placeholder="输入请求头" />
      <el-popover trigger="hover" width="170">
        <template #default>
          <pre>
请输入JSON格式数据，
例如:
{
  "h1": "value1",
  "h2": "value2"
}</pre>
        </template>
        <template #reference>
          <el-icon size="18px" class="ml-10px">
            <QuestionFilled />
          </el-icon>
        </template>
      </el-popover>
    </el-form-item>

    <el-form-item v-if="interfaceData.method === 'GET'" class="mt-[15px]" label="请求参数:">
      <el-input @change="interfaceDataChange" class="w-[400px]" v-model="interfaceData.params" placeholder="输入请求参数" />
      <el-popover trigger="hover" width="250">
        <template #default>
          <pre>
请输入JSON格式数据，
例如:
{
  "appId": "1f105268", 
  "textOne": "${用户问题}",
  "textTwo": "${答案}"
}</pre>
        </template>
        <template #reference>
          <el-icon size="18px" class="ml-10px">
            <QuestionFilled />
          </el-icon>
        </template>
      </el-popover>
    </el-form-item>

    <el-form-item v-if="interfaceData.method === 'POST'" class="mt-[15px]" label="请求体:">
      <el-input @change="interfaceDataChange" :autosize="{ minRows: 1, maxRows: 3 }" type="textarea" class="w-[400px]"
        v-model="interfaceData.body" placeholder="输入请求体" />
      <el-popover trigger="hover" width="250">
        <template #default>
          <pre>
请输入JSON格式数据，
例如:
{
  "appId": "1f105268", 
  "textOne": "${用户问题}",
  "textTwo": "${答案}"
}</pre>
        </template>
        <template #reference>
          <el-icon size="18px" class="ml-10px">
            <QuestionFilled />
          </el-icon>
        </template>
      </el-popover>
    </el-form-item>

    <el-form-item class="mt-[15px]" label="响应抽取:">
      <el-input @change="interfaceDataChange" :autosize="{ minRows: 1, maxRows: 3 }" type="textarea" class="w-[400px]"
        v-model="interfaceData.resultDesc" placeholder="输入响应抽取" />
      <el-popover trigger="hover" width="250">
        <template #default>
          <pre>
请输入JSON格式数据，
例如:
{
  "apiOK": {"code": 0},
  "errorMsgKey": "msg", 
  "mainResultKey": 
  "records.similarities"
}</pre>
        </template>
        <template #reference>
          <el-icon size="18px" class="ml-10px">
            <QuestionFilled />
          </el-icon>
        </template>
      </el-popover>
    </el-form-item>

    <el-form-item class="mt-[15px]" label="输入绑定:" style="width: 500px">
      <el-table :data="tableData">
        <el-table-column prop="key" label="占位符列表" width="180" />
        <el-table-column prop="value" label="选择列" width="230">
          <template #default="scope">
            <el-select clearable @change="(e) => columnGroupChange(e, scope.$index)"
              v-model="form.placeHolderMap[scope.row.key]" placeholder="请选择" class="w-[200px]">
              <el-option v-for="item in columnGroupList" :key="item.key" :label="item.key" :value="item.key" />
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>

    <el-form-item class="mt-[15px]" label="输出列名:" prop="bindOutputParam">
      <el-input class="w-[400px]" v-model="form.bindOutputParam" placeholder="绑定到输出的列名"
        @input="form.bindOutputParam = form.bindOutputParam.replace(/,/g, '')" />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import {
  defineEmits,
  defineExpose,
  defineProps,
  onBeforeUnmount,
  onMounted,
  ref,
} from "vue";

import { getJsonPlaceHolderList } from "@/api/prompt";
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  columnGroupList: {
    type: Object,
    required: true,
  },
});
const tableData = ref([]);
const interfaceData = ref({
  url: props.form.url ? props.form.url : "",
  method: props.form.method ? props.form.method : "POST",
  headers: props.form.headers ? props.form.headers : "",
  body: props.form.body ? props.form.body : "",
  params: props.form.params ? props.form.params : null,
  resultDesc: props.form.resultDesc ? props.form.resultDesc : "",
});
const emit = defineEmits(["promptIdChange", "promptChange"]);
const getPromptPlaceHolderListFn = async () => {
  const res = await getJsonPlaceHolderList({ ...interfaceData.value });
  if (res) {
    res.forEach((item) => {
      tableData.value.push({ key: item, value: null });
    });
  }
};

const interfaceDataChange = () => {
  // placeHolderMap.value = {};
  props.form.placeHolderMap = {};
  tableData.value = [];
  getPromptPlaceHolderListFn();
  emit("promptChange", { placeHolderMap: props.form.placeHolderMap });
  // emit('promptChange', { placeHolderMap: placeHolderMap.value });
};

const columnGroupChange = (e, index) => {
  tableData.value[index].value = e;
  emit("promptChange", { placeHolderMap: props.form.placeHolderMap });
  // emit('promptChange', { placeHolderMap: placeHolderMap.value });
};

const promptIdChange = (e) => {
  // placeHolderMap.value = {};
  props.form.placeHolderMap = {};
  tableData.value = [];
  getPromptPlaceHolderListFn();
};

onBeforeUnmount(() => {
  // placeHolderMap.value = {};
  props.form.placeHolderMap = {};
});

onMounted(async () => {
  if (interfaceData.value.url) {
    await getPromptPlaceHolderListFn();
  }
  tableData.value.forEach((e, index) => {
    if (props.form.placeHolderMap[e.key]) {
      tableData.value[index] = {
        ...e,
        value: props.form.placeHolderMap[e.key],
      };
    }
  });
});

defineExpose({ interfaceData, tableData });
</script>
