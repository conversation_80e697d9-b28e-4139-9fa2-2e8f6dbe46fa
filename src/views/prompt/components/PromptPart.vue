<template>
  <div class="mt-[30px]">
    <el-form-item label="PromptID:">
      <el-select
        @change="instructIdChange"
        clearable
        filterable
        v-model="form.instructId"
        placeholder="请输入PromptID"
        class="w-[400px]"
      >
        <el-option v-for="item in data.promptList" :key="item.key" :label="item.value" :value="item.key" />
      </el-select>
    </el-form-item>
    <el-form-item class="mt-[15px]" label="Prompt版本:">
      <el-select
        @change="promptIdChange"
        clearable
        v-model="form.promptId"
        placeholder="请选择Prompt版本"
        class="w-[400px]"
      >
        <el-option v-for="item in data.promptVersionList" :key="item.key" :label="item.value" :value="item.key" />
      </el-select>
    </el-form-item>

    <el-form-item class="mt-[15px]" label="输入绑定:" style="width: 500px">
      <el-table :data="tableData">
        <el-table-column prop="key" label="占位符列表" width="180" />
        <el-table-column prop="value" label="选择列" width="230">
          <template #default="scope">
            <el-select
              clearable
              @change="(e) => columnGroupChange(e, scope.$index)"
              v-model="form.placeHolderMap[scope.row.key]"
              placeholder="请选择"
              class="w-[200px]"
            >
              <el-option v-for="item in columnGroupList" :key="item.key" :label="item.key" :value="item.key" />
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>

    <el-form-item class="mt-[15px]" label="输出列名:" prop="bindOutputParam">
      <el-input 
        class="w-[400px]" 
        v-model="form.bindOutputParam" 
        placeholder="请输入" 
        @input="form.bindOutputParam = form.bindOutputParam.replace(/,/g, '')" 
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, watch, ref, onMounted, onBeforeUnmount } from 'vue';
import { getPromptPlaceHolderList } from '@/api/prompt';
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  columnGroupList: {
    type: Object,
    required: true,
  },
});
const tableData = ref([]);
const placeHolderMap = ref({});
const emit = defineEmits(['promptIdChange', 'promptChange']);
const getPromptPlaceHolderListFn = async () => {
  if (props.form.promptId || props.form.instructId) {
    const res = await getPromptPlaceHolderList(props.form.instructId, props.form.promptId);
    if (res) {
      res.forEach((item) => {
        tableData.value.push({ key: item, value: null });
      });
    }
  }
};

watch(()=> props.form.clonePlaceHolderMap, (val)=> {
  tableData.value.length = 0
  // placeHolderMap.value = val;
  if (val && Object.keys(val)){
    for(let key in val){
      tableData.value.push({
        key,
        value: val[key]
      })
    }
    
  }
}, {immediate: true, deep: true})

const instructIdChange = () => {
  // placeHolderMap.value = {};
  props.form.placeHolderMap = {};
  tableData.value = [];
  emit('instructIdChange');
  // emit('promptChange', { placeHolderMap: placeHolderMap.value });
  emit('promptChange', { placeHolderMap: props.form.placeHolderMap });
  getPromptPlaceHolderListFn();
};

const columnGroupChange = (e, index) => {
  tableData.value[index].value = e;
  // emit('promptChange', { placeHolderMap: placeHolderMap.value });
  emit('promptChange', { placeHolderMap: props.form.placeHolderMap });
  
};

const promptIdChange = (e) => {
  // placeHolderMap.value = {};
  props.form.placeHolderMap = {};
  tableData.value = [];
  getPromptPlaceHolderListFn();
};

onBeforeUnmount(() => {
  // placeHolderMap.value = {};
  props.form.placeHolderMap = {};
  tableData.value = [];
});
</script>
