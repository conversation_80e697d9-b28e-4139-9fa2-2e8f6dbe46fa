<template>
  <div>
    <el-form-item class="mt-[30px]" label="问题集ID:">
      <el-tree-select
        v-model="treeValue"
        :data="data.commonDropDownList"
        multiple
        :render-after-expand="false"
        show-checkbox
        class="w-[400px]"
        node-key="id"
        :props="defaultProps"
        ref="tree"
      />

      <!-- <el-input class="w-[400px]" v-model="form.tagId" placeholder="请输入问题集ID" /> -->
    </el-form-item>
    <el-form-item class="mt-[15px]" label="输出绑定:">
      <el-checkbox-group v-model="checkList">
        <el-checkbox
          v-for="item in data.questionHubList"
          :label="item.key"
          :key="item.key"
        >
          <div class="check-content">
            {{ item.value }}
            <el-input 
              v-if="checkList.includes(item.key)" 
              class="check-input" 
              v-model="item.input"
              placeholder="绑定输出名称" 
            />
          </div>
        </el-checkbox>
      </el-checkbox-group>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from 'vue';
import { ElMessage } from 'element-plus';
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  defaultProps: {
    type: Object,
    default: () => {
      return {
        children: 'children',
        label: 'name',
        value: 'id',
      }
    },
    
  },
});
const checkList = ref([])
const tree = ref(null);
const treeValue = ref()
// const handleCheckedChange = (item) => {
//   // if(!checkList.value.includes(item.key)) {
//   //   item.input = ''
//   // }else {
    
//   // }
// }
const getValue = () => {
  let questionObj = tree.value?.getCheckedNodes()
  let contentJson = {}
  let collidNameMap = {}
  let userBindOutMap = {}
  // if(questionObj && questionObj.length < 1) {
  //   return ElMessage.error('请选择问题集ID')
  // }
  questionObj.forEach(item => {
    let id = item.id
    collidNameMap[id] = item.name
  });
  for(let i in props.data.questionHubList) {
    let key = props.data.questionHubList[i].key
    let value = props.data.questionHubList[i].input
    if(checkList.value.includes(props.data.questionHubList[i].key)) {
      if(!value) {
        ElMessage.error('请输入绑定输出名称')
        break
      }
      userBindOutMap[key] = value
    }
  }
  return contentJson = {
    collidNameMap,
    userBindOutMap
  }
  
}
defineExpose({getValue})
</script>

<style scoped>
.el-checkbox {
  margin-bottom: 10px;
}
.check-content {
  display: flex;
  align-items: center;
}
.check-input {
  margin-left: 20px;
}
.el-checkbox-group {
  display: grid;
}
</style>