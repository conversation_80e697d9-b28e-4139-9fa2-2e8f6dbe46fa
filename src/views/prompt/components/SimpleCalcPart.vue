<template>
  <div>
    <el-form-item prop="countType" label="计算类型:" class="mt-[30px]">
      <el-select class="w-[400px]" v-model="form.countType" placeholder="请选择">
        <el-option 
          v-for="item in data.countTypeList" 
          :key="item.key" 
          :label="item.value" 
          :value="item.key"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="类型:" class="mt-[30px]" v-if="form.countType">
      <el-radio-group v-model="data.jsonType">
        <el-radio :label="'字符串'" size="large">字符串</el-radio>
        <el-radio :label="'整数'" size="large">整数</el-radio>
        <el-radio :label="'小数'" size="large">小数</el-radio>
        <el-radio :label="'日期'" size="large">日期</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="计算结果:" class="mt-[30px]" v-if="form.countType">
      <div>
        <el-select 
          class="w-[130px] mr-[4px]" 
          v-model="form.inputParamsLeft" 
          placeholder="请选择"
          v-if="data.importType === 1"
        >
          <el-option 
            v-for="item in data.columnGroupMapList" 
            :key="item.key" 
            :label="item.key" 
            :value="item.value"
          />
        </el-select>
        <el-input v-else v-model="form.inputParamsLeft" class="w-[130px] mr-[4px]" placeholder="输入具体值" />
        <span> {{ objType[form.countType] }} </span>
        <el-select 
          v-if="data.importTypeRight === 1" 
          class="w-[130px] ml-[4px]" 
          v-model="form.inputParamsRight" 
          placeholder="请选择"
        >
          <el-option 
            v-for="item in data.columnGroupMapList" 
            :key="item.key" 
            :label="item.key" 
            :value="item.value"
          />
        </el-select>
        <el-input v-else v-model="form.inputParamsRight" class="w-[130px] ml-[4px]" placeholder="输入具体值" />
        <span> = </span>
        <el-input 
          v-model="form.bindOutputParam" 
          class="w-[130px]" 
          placeholder="输出列名" 
          @input="form.bindOutputParam = form.bindOutputParam.replace(/,/g, '')"
        />
      </div>
      <div>
        <el-radio-group v-model="data.importType" class="w-[140px]">
          <el-radio :label="1" size="small">下拉选方式</el-radio>
          <el-radio :label="2" size="small">输入框方式</el-radio>
        </el-radio-group>
        <el-radio-group v-model="data.importTypeRight" class="w-[140px] ml-[12px]">
          <el-radio :label="1" size="small">下拉选方式</el-radio>
          <el-radio :label="2" size="small">输入框方式</el-radio>
        </el-radio-group>
      </div>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { ElMessage } from 'element-plus';
const objType = {
  1: "+",
  2: "-",
  3: "=",
  4: ">",
  5: "相似",
  6: "包含",
}
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
});
const getValue = () => {
  let arr = []
  let type= []
  let simpleCalcJson = {}
  // if(!props.form.countType) {
  //   return ElMessage.error('请选择计算类型')
  // }
  // if(!props.form.jsonType) {
  //   return ElMessage.error('请选择JSON类型')
  // }
  // if(!props.form.inputParamsLeft || !props.form.inputParamsRight) {
  //   return ElMessage.error('请输入计算结果')
  // }
  // if(!props.form.bindOutputParam) {
  //   return ElMessage.error('请输入字段名')
  // }
  if (props.form.inputParamsLeft && props.form.inputParamsRight) {
    arr.push(props.form.inputParamsLeft)
    arr.push(props.form.inputParamsRight)
  }
  if (props.form.countType && props.data.jsonType) {
    type.push(props.form.countType)
    type.push(props.data.jsonType)
  }
  return simpleCalcJson = {
    inputParams: arr.join(','),
    actionContent: type.join(','),
    bindOutputParam: props.form.bindOutputParam,
  }
  
}
defineExpose({getValue})
</script>
