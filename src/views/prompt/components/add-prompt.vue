<template>
  <el-dialog v-model="dialogData.show" :title="dialogData.title" width="700" align-center destroy-on-close
    :before-close="handleClose">
    <div>
      <el-form ref="ruleFormRef" :model="form" label-position="right" label-width="100" :rules="rules"
        class="demo-ruleForm">
        <el-form-item label="节点类型:" prop="actionType">
          <el-select clearable class="w-[400px]" v-model="form.actionType" @change="actionTypeChange" placeholder="请选择">
            <el-option v-for="item in data &&
              appTypeList(form.id ? form.stepOrder : columnNumber)" :key="item.key" :value="item.value"
              :label="item.label"></el-option>
          </el-select>
        </el-form-item>

        <QuestionHubForm v-if="dialogData.show && form.actionType === 'QUESTION_HUB'" :form="form" :data="data"
          ref="questionHub" />

        <InterfacePart v-if="dialogData.show && form.actionType === 'INTERFACE'" :columnGroupList="columnGroupList"
          :form="form" :data="data" ref="interfacePartRef" @promptChange="handleChange"
          @instructIdChange="instructIdChange" />

        <PromptForm v-if="dialogData.show && form.actionType === 'PROMPT'" :columnGroupList="columnGroupList"
          :form="form" :data="data" @promptChange="handleChange" @instructIdChange="instructIdChange" />
        <SimpleCalcForm v-if="dialogData.show && form.actionType === 'SIMPLE_CALC'" :form="form" :data="data"
          ref="simpleCalc" />
      </el-form>
    </div>

    <div v-if="form.actionType === 'EXCEL_IMPORT'">
      <ExcelImportForm :dialogShow="dialogData.show" @fileChange="handleChange" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submit"> 提交 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { defineExpose, defineProps, reactive, ref } from 'vue'
import { ElMessage, UploadRawFile } from 'element-plus'

import ExcelImportForm from './ExcelImportPart.vue' // 引入 ExcelImportForm 组件
import InterfacePart from './InterfacePart.vue'
import PromptForm from './PromptPart.vue'
import QuestionHubForm from './QuestionPart.vue'
import SimpleCalcForm from './SimpleCalcPart.vue'

import {
  getColumnGroupMap,
  getCommonDropDownList,
  getQuestionSetList,
} from '@/api/prompt'

import { appTypeList, checkOptions } from '@/common/prompt.ts'

import type { FormInstance } from 'element-plus'
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http
const ruleFormRef = ref<FormInstance>()
const questionHub = ref(null)
const simpleCalc = ref(null)
const interfacePartRef = ref(null)
const columnGroupList = ref(null)
const title = ref('')
let dialogData = reactive({
  show: false,
  title: '添加节点',
})
const rules = {
  actionType: [{ required: true, message: '请选择节点类型' }],
  countType: [{ required: true, message: '请选择计算类型', trigger: 'blur' }],
  bindOutputParam: [{ required: true, message: '请输入输出列名' }],
}

const props = defineProps({
  flowId: {
    type: String,
    default: null,
  },
  stepOrder: {
    type: Number,
    default: undefined,
  },

  columnNumber: {
    type: Number,
    default: 1,
  },
})
let data = reactive({
  checkOptions,
  countTypeList: [],
  promptList: [],
  promptVersionList: [],
  questionHubList: [],
  commonDropDownList: [],
  columnGroupMapList: [],
  importType: 1,
  importTypeRight: 1,
  jsonType: '字符串',
})

let form = reactive({
  id: null,
  file: null,
  contentJson: null,
  actionContent: null,
  bindOutputParam: null,
  inputParams: null,
  inputParamsLeft: null,
  inputParamsRight: null,
  actionType: null,
  promptId: null,
  instructId: null,
  placeHolderMap: null,
  clonePlaceHolderMap: {},
  countType: null, // 计算类型
  stepOrder: null,
  body: '',
  headers: '',
  resultDesc: '',
})

defineExpose({ dialogData, initData })

const handleChange = (data) => {
  if (data.placeHolderMap) {
    form.placeHolderMap = data.placeHolderMap
  } else {
    form.file = data.file
  }
  console.log('form.file', form.file)
}

const actionTypeChange = (e) => {
  form.actionType = e
  switch (e) {
    case 'QUESTION_HUB':
      fetchCommonDropDownList(7)
      fetchQuestionSetList()
      break
    case 'SIMPLE_CALC':
      fetchCommonDropDownList(6)
      fetchColumnGroupMapByFlowId(props.flowId)
      break
    case 'PROMPT':
      fetchCommonDropDownList(4)
      fetchColumnGroupList()
      break
    case 'INTERFACE':
      fetchColumnGroupList()
      break
    case 'PROMPT':
      fetchCommonDropDownList(4)
      break

    case 'EXCEL_IMPORT':
      break
  }
}

const instructIdChange = (id) => {
  form.instructId && fetchCommonDropDownList(5, form.instructId)
  form.promptId = id ? id : null
}
async function fetchQuestionSetList() {
  try {
    const res = await getQuestionSetList()
    data.commonDropDownList = res
  } catch (error) {
    throw new Error('error')
  }
}
async function fetchColumnGroupMapByFlowId(flowId) {
  try {
    const res = await getColumnGroupMap({ flowId, stepOrder: form.stepOrder })
    data.columnGroupMapList.length = 0
    for (let i in res) {
      let key = res[i].key
      data.columnGroupMapList.push({
        key: key,
        value: key,
      })
    }
  } catch (error) {
    console.log(error)
  }
}
async function fetchCommonDropDownList(type, param1 = null) {
  try {
    const listEnum = {
      6: 'countTypeList',
      4: 'promptList',
      5: 'promptVersionList',
      7: 'questionHubList',
    }
    const res = await getCommonDropDownList(type, param1)
    data[listEnum[type]] = res
    if (form.actionType === 'QUESTION_HUB') {
      data.questionHubList.forEach((item) => {
        item.input = item.value
      })
    }

    return res
  } catch (e) {
    throw new Error('error')
  }
}

const emit = defineEmits(['fetchList', 'actionList'])

function initData(data, item) {
  dialogData.show = true
  initFormData()
  // if(item) {
  //   title.value = item === 1 ? '替换第一节点' : `编辑第${item}列节点`
  // }
  if (data) {
    switch (data.actionType) {
      case 'PROMPT':
        for (let i in data.promptInfo) {
          form[i] = data.promptInfo[i]
        }
        instructIdChange(form.promptId)
        form.clonePlaceHolderMap = data.promptInfo.placeHolderMap
        break
      case 'INTERFACE':
        for (let i in data.interfaceInfo) {
          form[i] = data.interfaceInfo[i]
        }
        form.body =
          data.interfaceInfo.body && JSON.stringify(data.interfaceInfo.body)
        form.params =
          data.interfaceInfo.params && JSON.stringify(data.interfaceInfo.params)
        form.headers =
          data.interfaceInfo.headers &&
          JSON.stringify(data.interfaceInfo.headers)
        form.resultDesc =
          data.interfaceInfo.resultDesc &&
          JSON.stringify(data.interfaceInfo.resultDesc)
        form.clonePlaceHolderMap = data.interfaceInfo.placeHolderMap
        break
      case 'SIMPLE_CALC':
        for (let i in data) {
          form[i] = data[i]
        }
        let content = data.actionContent.split(',')
        let inputParams = data.inputParams.split(',')
        form.countType = Number(content[0])
        data.jsonType = content[1]
        form.inputParamsLeft = inputParams[0]
        form.inputParamsRight = inputParams[1]
        break
    }
    form.id = data.id
    form.actionType = data.actionType
    form.stepOrder = data.stepOrder
    form.bindOutputParam = data.bindOutputParam
    console.log(form, data)

    actionTypeChange(form.actionType)
  }
}

function handleClose() {
  initFormData()
  dialogData.show = false
}

const initFormData = () => {
  for (const key in form) {
    form[key] = null
  }
}

async function submit() {
  let contentJson = null
  let simpleCalcJson = null
  let interfacePartData = null
  try {
    if (!ruleFormRef.value) return
    await ruleFormRef.value.validate()
    const formData = new FormData()
    if (form.id) {
      formData.append('id', form.id)
    }
    formData.append('flowId', props.flowId)
    formData.append('actionType', form.actionType)
    formData.append(
      'stepOrder',
      form.stepOrder ? form.stepOrder : props.columnNumber
    )
    if (form.actionType === 'EXCEL_IMPORT' && form.file) {
      const temp = form.file
      const data = temp.raw ? temp.raw : temp
      formData.append('file', new Blob([data as UploadRawFile]))
    }
    if (form.actionType === 'PROMPT') {
      form.contentJson = JSON.stringify({
        placeHolderMap: form.placeHolderMap,
        instructId: form.instructId,
        promptId: form.promptId,
      })
      formData.append('bindOutputParam', form.bindOutputParam)
      formData.append('contentJson', form.contentJson)
    }

    if (form.actionType === 'INTERFACE') {
      let tableData = interfacePartRef.value?.tableData
      //如果选择列中有null提醒用户选择
      let status = tableData.some((e) => !e.value)
      if (status) {
        ElMessage.error('有选择列为空，请完善后提交!')
        return
      }
      interfacePartData = interfacePartRef.value?.interfaceData
      const jsonName = ['headers', 'body', 'params', 'resultDesc']

      const newObj = {}
      for (const key in interfacePartData) {
        const e = interfacePartData[key]
        console.log(e, key, interfacePartData, 111)
        if (jsonName.includes(key) && e) {
          if (!isValidJSON(e)) {
            console.log(e, key, 222)
            ElMessage.error(
              '请求头，请求体，请求参数，响应抽取必须是合法的JSON格式'
            )
            return
          }
          newObj[key] = JSON.parse(e)
        } else {
          newObj[key] = e
        }
      }
      form.contentJson = JSON.stringify({
        placeHolderMap: form.placeHolderMap,
        ...newObj,
      })
      formData.append('bindOutputParam', form.bindOutputParam)
      formData.append('contentJson', form.contentJson)
    }

    if (form.actionType === 'SIMPLE_CALC') {
      simpleCalcJson = simpleCalc.value?.getValue()
      if (simpleCalcJson) {
        if (!simpleCalcJson.bindOutputParam) {
          return ElMessage.error('请输入输出的列名')
        }
        formData.append('inputParams', simpleCalcJson.inputParams)
        formData.append('bindOutputParam', simpleCalcJson.bindOutputParam)
        formData.append('actionContent', simpleCalcJson.actionContent)
      }
    }

    if (form.actionType === 'QUESTION_HUB') {
      contentJson = questionHub.value?.getValue()
      formData.append('contentJson', JSON.stringify(contentJson))
    }
    const res = await $http.ajax({
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      url: '/nbi/evaluate/flowEval/flow/createAction',
      method: 'POST',
      data: formData,
    })
    if (res.code === -1) {
      ElMessage({
        message: res.msg || '网络异常，请重试~',
        type: 'warning',
      })
    } else {
      handleClose()
      emit('fetchList')
      emit('actionList')
    }
  } catch (e) {
    console.log(e)
  }
}

function isValidJSON(str) {
  try {
    JSON.parse(str)
    return true
  } catch (e) {
    return false
  }
}

async function fetchColumnGroupList() {
  try {
    const res = await getColumnGroupMap({
      flowId: props.flowId,
      stepOrder: form.stepOrder,
    })
    if (!res) return
    columnGroupList.value = res
    console.log(columnGroupList.value)
  } catch (e) {
    console.log(e)
  }
}
</script>

<style lang="less" scoped>
.el-checkbox {
  margin-bottom: 10px;
}

.check-content {
  display: flex;
  align-items: center;
}

.check-input {
  margin-left: 20px;
}
</style>
