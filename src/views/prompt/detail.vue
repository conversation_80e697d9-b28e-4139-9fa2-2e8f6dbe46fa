<template>
  <div class="page-content search-content label-content">
    <NotAuth v-if="useAuth()" />
    <div v-else class="pb-20px">
      <div>
        <el-button type="primary" @click="toAddQuality(null)">开始</el-button>
      </div>
      <el-table :data="data.tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="100" />
        <el-table-column prop="tagName" label="任务名称" />
        <el-table-column prop="tagNameEn" label="任务链" />
        <el-table-column prop="tagBusiId" label="任务状态" />
        <el-table-column prop="creatorName" label="创建人" />
        <el-table-column prop="updateTime" label="创建时间" width="180">
          <template #default="scope">
            {{ dayjs(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间" width="180">
          <template #default="scope">
            {{ dayjs(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
      </el-table>
      <!-- <div class="flex flex-end mt-20px">
        <el-pagination
          background
          v-model:current-page="pag.page"
          v-model:page-size="pag.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, jumper, sizes"
          :total="pag.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div> -->
    </div>
  </div>
  <AddPrompt @change="onQualityChange" ref="addPromptRef" />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import dayjs from 'dayjs'

import NotAuth from '@/views/401.vue'
import AddPrompt from '@/views/prompt/components/add-prompt.vue'

import { getDropDownList } from '@/api/common'
import { getItemsDropDownForCreate, getTagsPage } from '@/api/tag'

import {
  SelectListItemType,
  TagsListResType,
  TagsPageListQueryType,
} from '@/common/interface'

import { useAuth } from '@/hooks/useAuth'

import { useEnumsStore } from '@/store/useEnumsStore'

const enumsStore = useEnumsStore()
let addPromptRef = ref()

let data = reactive({
  appTypeList: [] as SelectListItemType[],
  serviceList: [] as SelectListItemType[],
  qualityList: [] as SelectListItemType[],
  tableData: [] as TagsListResType[],
})

let form = reactive({
  appId: '',
  serviceId: '',
  itemId: [],
  keyword: '',
  username: '',
})

let pag = reactive({
  page: 1,
  size: 10,
  total: 0,
})

function toAddQuality(row) {
  // console.log(taskAllocationRef.value)
  addPromptRef.value.initData(row)
}

function onQualityChange(status) {
  console.log(status)
  getTagsPageApi()
}

function query() {
  pag.page = 1
  getTagsPageApi()
}

function handleSizeChange(size) {
  console.log(size)
  query()
}

function handleCurrentChange(page) {
  console.log(page)
  getTagsPageApi()
}

function reset() {
  pag.page = 1
  form.appId = ''
  form.serviceId = ''
  form.itemId = ''
  form.keyword = ''
  getTagsPageApi()
}

async function getTagsPageApi() {
  try {
    localStorage.setItem('searchQualityData', JSON.stringify(form))
    let { pageSize, currentPage, total, elements } = await getTagsPage({
      page: pag.page,
      size: pag.size,
      ...form,
    } as TagsPageListQueryType)
    console.log({ pageSize, currentPage, total, elements })
    pag.total = total
    data.tableData = elements
  } catch (e) {
    console.log(e)
  }
}

async function getDropDownListApi() {
  try {
    let res = await getDropDownList(1)
    console.log(res)
    data.appTypeList = res
  } catch (e) {
    console.log(e)
  }
}

async function getItemsDropDownForCreateApi() {
  try {
    let res = await getItemsDropDownForCreate()
    console.log(res)
    data.qualityList = res
  } catch (e) {
    console.log(e)
  }
}

onMounted(async () => {
  if (localStorage.getItem('searchQualityData')) {
    const search = JSON.parse(
      localStorage.getItem('searchQualityData') as string
    )
    if (typeof search === 'object') {
      form.appId = search.appId
      form.serviceId = search.serviceId
      form.itemId = search.itemId
      form.keyword = search.keyword
    }
  }
  getTagsPageApi()
  getDropDownListApi()
  getItemsDropDownForCreateApi()
  data.serviceList = await enumsStore.fetchDropDownList(4)
})
</script>
