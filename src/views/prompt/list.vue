<template>
  <div class="page-content search-content label-content">
    <NotAuth v-if="useAuth()" />
    <div v-else class="pb-20px">
      <el-form :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="任务名称:">
          <el-select
            v-model="form.flowId"
            placeholder="请选择"
            filterable
            clearable
            class="w-[275px]"
            @change="changeSelect"
          >
            <el-option v-for="item in data.taskNameList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务状态:">
          <el-select
            v-model="flowTaskStatus"
            placeholder="请选择"
            filterable
            clearable
            multiple
            class="w-[275px]"
            @change="changeSelect"
          >
            <el-option v-for="item in data.statusList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>

        <el-form-item label="创建人:">
          <el-select
            v-model="form.creatorInVague"
            filterable
            clearable
            placeholder="请输入"
            class="w-[275px]"
            @change="changeSelect"
          >
            <el-option v-for="item in data.userNameList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="mb-10px search-btns">
        <el-button type="primary" @click="query">查询</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button type="success" @click="toDetail">新建评测</el-button>
      </div>
      <el-table :data="data.tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="flowName" label="任务名称">
          <template #default="scope">
            <span style="color: #409eff; cursor: pointer" @click="toDetail(scope.row)">{{ scope.row.flowName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="flowChain" label="任务链" />
        <el-table-column prop="flowTaskStatusName" label="任务状态" width="80" />
        <el-table-column prop="createName" label="创建人" width="90" />
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="scope">
            {{ dayjs(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间" width="180">
          <template #default="scope">
            {{ dayjs(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" prop="id" label="操作" width="200">
          <template #default="scope">
            <el-button link type="primary" @click="toDetail(scope.row)">详情</el-button>
            <el-button link type="primary" @click="startTaskApi(scope.row.id)">执行</el-button>
            <el-button link type="primary" @click="del(scope.row.id)">删除</el-button>
            <el-button link type="primary" @click="handleDownload(scope.row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex flex-end mt-20px">
        <el-pagination
          background
          v-model:current-page="pag.page"
          v-model:page-size="pag.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, jumper, sizes"
          :total="pag.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import NotAuth from '@/views/401.vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getTagsPage, getCommonDropDownList, delTask, startTask, downLoadDetailsByFlowId } from '@/api/prompt';

import { SelectListItemType, TagsListResType, PromptListItemType } from '@/common/interface';

import { useAuth } from '@/hooks/useAuth';
import { useEnumsStore } from '@/store/useEnumsStore';
const Router = useRouter();
const enumsStore = useEnumsStore();
let data = reactive({
  taskNameList: [] as SelectListItemType[],
  statusList: [] as SelectListItemType[],
  serviceList: [] as SelectListItemType[],
  tableData: [] as TagsListResType[],
  userNameList: [] as SelectListItemType[],
});
const flowTaskStatus = ref<number[]>([]);
let form = reactive({
  flowId: '',
  flowNameInVague: '',
  creatorInVague: '',
  flowTaskStatuses: '',
});

let pag = reactive({
  page: 1,
  size: 10,
  total: 0,
});
function toDetail(row) {
  Router.push({
    path: '/prompt/create',
    query: {
      id: row.id,
      flowName: row.flowName,
    },
  });
}
const changeSelect = (item) => {
  getTagsPageApi();
};
function del(row) {
  ElMessageBox.confirm('确认删除任务？', '', {
    type: 'warning',
    distinguishCancelAndClose: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(() => {
      delTaskApi(row);
    })
    .catch((action) => {
      console.log(action);
    });
}

const handleDownload = async (data) => {
  const res = await downLoadDetailsByFlowId(data.id);

  if (res) {
    const blob = new Blob([res.data], {
      type: 'application/vnd.ms-excel;charset=utf-8',
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('id', 'downloadLink');
    link.setAttribute('download', `${data.flowName}_${data.id}.xlsx`);
    document.body.appendChild(link);
    link.click();
    const objLink = document.getElementById('downloadLink');
    objLink && document.body.removeChild(objLink);
    ElMessage({
      type: 'success',
      message: '导出成功',
    });
  }
};
async function delTaskApi(flowId) {
  try {
    await delTask(flowId);
    getTagsPageApi();
  } catch (e) {
    console.log(e);
  }
}
async function startTaskApi(flowId) {
  try {
    const res = await startTask(flowId);
    if (res.code === -1) {
      return ElMessage.error(res.msg);
    }
  } catch (e) {
    console.log(e);
  }
}
function query() {
  pag.page = 1;
  getTagsPageApi();
}

function handleSizeChange(size) {
  query();
}

function handleCurrentChange(page) {
  getTagsPageApi();
}

function reset() {
  pag.page = 1;
  flowTaskStatus.value = [];
  form.creatorInVague = '';
  form.flowId = '';
  getTagsPageApi();
}

async function getTagsPageApi() {
  try {
    localStorage.setItem('searchQualityData', JSON.stringify(form));
    form.flowTaskStatuses = flowTaskStatus.value.join(',');
    let { total, list } = await getTagsPage({
      page: pag.page,
      size: pag.size,
      ...form,
    } as PromptListItemType);
    pag.total = total;
    data.tableData = list;
  } catch (e) {
    console.log(e);
  }
}

async function getDropDownListApi() {
  try {
    let taskName = await getCommonDropDownList(1);
    let status = await getCommonDropDownList(2);
    let userName = await getCommonDropDownList(3);
    data.taskNameList = taskName;
    data.statusList = status;
    data.userNameList = userName;
  } catch (e) {
    console.log(e);
  }
}

onMounted(async () => {
  getTagsPageApi();
  getDropDownListApi();
  data.serviceList = await enumsStore.fetchDropDownList(4);
});
</script>
<style lang="less" scoped>
.search-btns {
  display: flex;
  justify-content: right;
}
</style>
