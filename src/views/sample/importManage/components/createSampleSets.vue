<template>
  <!-- Form -->
  <el-button type="success" @click="dialogFormVisible = true">
    创建样本集
  </el-button>

  <div class="sampleDialog">
    <el-dialog
      :append-to-body="true"
      v-model="dialogFormVisible"
      :destroy-on-close="true"
      title="创建样本集"
      width="880"
    >
      <el-form
        :model="form"
        ref="ruleFormRef"
        label-suffix="："
        :rules="rules"
        label-width="130"
      >
        <el-form-item label="所属应用" prop="appId">
          <el-select
            :style="{ width: '100%' }"
            v-model="form.appId"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in applyList as Array<{value: string, key: string}>"
              :key="index"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="样本分类" prop="sampleType">
          <el-radio-group v-model="form.sampleType" @change="sampleTypeChange">
            <el-radio
              v-for="item in SampleTypeOptions"
              :label="item.value"
              :key="item.value"
            >
              {{ item.label }}({{ item.desc }})
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="导入方式" prop="importType">
          <el-radio-group v-model="form.importType" @change="handleImportType">
            <el-radio :label="0" :disabled="['1001'].includes(form.sampleType)"
              >SQL导入</el-radio
            >
            <el-radio :label="1" :disabled="form.sampleType === '1002'"
              >文件上传</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <template v-if="form.importType === 0">
          <div class="flex">
            <el-form-item
              label="数据提取SQL"
              prop="importSql"
              class="flex-grow"
            >
              <el-input
                type="textarea"
                :rows="5"
                placeholder="请输入"
                v-model="form.importSql"
              />
            </el-form-item>
            <el-popover
              placement="top"
              width="400"
              v-if="form.sampleType === '1003'"
            >
              <template #reference>
                <span class="ml-2">SQL示例</span>
              </template>
              <template #default>
                <pre class="overflow-auto h-200px">
                  {{ sql }}
                </pre>
              </template>
            </el-popover>
          </div>
        </template>
        <template v-else-if="form.importType === 1">
          <el-form-item label="样本上传" prop="file">
            <el-upload
              ref="uploadRef"
              action=""
              :auto-upload="false"
              accept="xls,xlsx,csv"
              :limit="1"
              v-model:file-list="file"
              :on-exceed="handleExceed"
              :on-change="handleFileChange"
              :on-remove="handleFileChange"
            >
              <template #trigger>
                <el-button type="primary">上传文件</el-button>
              </template>
              <template #tip>
                <div class="flex flex-col">
                  <span>单次允许上传一个文件，支持扩展名: .xls .xlsx</span>
                  <div
                    class="flex items-center cursor-pointer text-#409eff"
                    @click="downloadTemplate"
                  >
                    下载模板
                    <el-icon class="ml-2"><IEpDownload /></el-icon>
                  </div>
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </template>

        <el-form-item label="导入描述" prop="importDesc">
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入"
            v-model="form.importDesc"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitForm(ruleFormRef)">
            提 交
          </el-button>
          <el-button @click="resetForm(ruleFormRef)">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, nextTick, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'

import { SampleTypeOptions } from '@/common/constants'

import type {
  FormInstance,
  FormRules,
  UploadInstance,
  UploadRawFile,
  UploadUserFile,
} from 'element-plus'

const $http = getCurrentInstance()?.appContext.config.globalProperties.$http
const dialogFormVisible = ref(false)

const uploadRef = ref<UploadInstance>()
const file = ref<UploadUserFile[]>([])

const sql = `
填写示例：
select
-- 平台使用字段，必须有
    session_id     as \`_#_分组_#_\` -- 必须 平台分组字段 会话唯一ID（ 会话ID、通话ID、sessionid）
     , create_time as \`_#_排序_#_\` -- 必填 平台用于句子排序字段，字段名order_id为默认名不可修改,字段取可用于排序的字段名。如果不需要排序，可以在自己的SQL中定义一个空值字段
-- 平台标注时要展示的字段，as后为页面展示列名
     , user_id     as \`用户ID\`     -- 用户ID
     , session_id  as \`会话\`       -- 会话
     , answer      as \`模型回答\`-- 回复内容
from (
         -- 你的SQL 需要在这里把需要处理的字段都处理完成
         select trace_id    -- 请求ID
              , user_id     -- 用户ID
              , session_id  -- 会话ID
              , answer
              , create_time -- 行插入时间
         from yc_ods.ods_sharing_orca_ai_hub_chat_history_day_a -- 小鹏客服生产环境对话记录表
         where dt = '2024-06-18'
           and user_id not in ('100', '200')) tt
limit 10
`
const ruleFormRef = ref<FormInstance>()
const form = reactive({
  appId: null,
  importSql: null,
  importDesc: null,
  importType: 0,
  sampleType: '0',
})
const rules = reactive<FormRules>({
  appId: [{ required: true, message: '请选择所属应用' }],
  importSql: [
    {
      required: true,
      message: '请输入数据提取SQL',
    },
  ],
  importType: [
    {
      required: true,
      message: '请选择导入方式',
    },
  ],
  importDesc: [
    {
      required: true,
      message: '请输入导入描述',
    },
  ],
  file: [
    {
      validator: (rule, value, callback) => {
        if (file.value.length === 0) {
          callback(new Error('请上传文件'))
        } else {
          callback()
        }
      },
    },
  ],
})

defineProps({
  applyList: {
    type: Array,
  },
})
const emit = defineEmits(['fetchList'])

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  await formEl.validate()

  try {
    await formEl.validate()
    const formData = new FormData()
    if (form) {
      formData.append('appId', form.appId || '')
      formData.append('importDesc', form.importDesc || '')
      formData.append('sampleType', form.sampleType || '')
      if (form.importType === 0) {
        formData.append('importSql', form.importSql || '')
        formData.append('importType', '0')
      } else {
        formData.append('importType', '1')
        if (file.value.length > 0) {
          const temp = file.value[0]
          const data = temp.raw ? temp.raw : temp
          formData.append('file', new Blob([data as UploadRawFile]))
        }
      }
    }
    const res = await $http.ajax({
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      url: '/nbi/anno/importManage/newTask',
      method: 'POST',
      data: formData,
    })
    if (res.code === -1) {
      ElMessage({
        message: res.msg || '网络异常，请重试~',
        type: 'warning',
      })
    } else {
      resetForm(formEl)
      emit('fetchList')
      dialogFormVisible.value = false
    }
  } catch (error) {
    console.log(error)
  }
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  dialogFormVisible.value = false
  file.value = []
}

const downloadTemplate = async () => {
  const res = await $http.ajax({
    url: `/nbi/anno/importManage/templateDownload?sampleType=${form.sampleType}`,
    method: 'GET',
    responseType: 'blob',
  })

  if (res) {
    const blob = new Blob([res.data], {
      type: 'application/vnd.ms-excel;charset=utf-8',
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('id', 'downloadLink')
    link.setAttribute('download', `标注平台样本上传样例.xlsx`)
    document.body.appendChild(link)
    link.click()
    const objLink = document.getElementById('downloadLink')
    objLink && document.body.removeChild(objLink)
    ElMessage({
      type: 'success',
      message: '导出成功',
    })
  }
}

const handleExceed = (files: File[], uploadFiles: UploadUserFile[]) => {
  file.value = files
}
const handleFileChange = (uploadFile) => {
  nextTick(() => {
    if (ruleFormRef.value) {
      ruleFormRef.value?.validateField('file')
    }
  })
}
const handleImportType = () => {
  file.value = []
}

function sampleTypeChange(value) {
  if (value === '1002') {
    form.importType = 0
  }
  if (value === '1001') {
    form.importType = 1
  }
}
</script>
<style lang="less" scoped>
.sampleDialog {
  .el-button--text {
    margin-right: 15px;
  }

  .el-select {
    width: 300px;
  }

  .el-input {
    width: 300px;
  }

  .dialog-footer button:first-child {
    margin-right: 10px;
  }
}
</style>
