<template>
  <!-- Form -->
  <el-button type="success" @click="dialogFormVisible = true">
    + 样本导入
  </el-button>

  <div class="sampleDialog">
    <el-dialog
      :append-to-body="true"
      v-model="dialogFormVisible"
      title="样本导入"
    >
      <el-form
        :model="form"
        ref="ruleFormRef"
        label-suffix="："
        :rules="rules"
        label-width="130"
      >
        <el-form-item label="所属应用" prop="applicationId">
          <el-select
            :style="{ width: '100%' }"
            v-model="form.applicationId"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in applyList"
              :key="index"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据提取SQL" prop="importSql">
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入"
            v-model="form.importSql"
          />
        </el-form-item>

        <el-form-item label="导入描述" prop="importDesc">
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入"
            v-model="form.importDesc"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitForm(ruleFormRef)">
            提 交
          </el-button>
          <el-button @click="resetForm(ruleFormRef)">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'

import type { FormInstance, FormRules } from 'element-plus'
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http
const dialogFormVisible = ref(false)

const ruleFormRef = ref<FormInstance>()
const form = reactive({
  applicationId: null,
  importSql: null,
  importDesc: null,
})
const rules = reactive<FormRules>({
  applicationId: [{ required: true, message: '请选择所属应用' }],
  importSql: [
    {
      required: true,
      message: '请输入数据提取SQL',
    },
  ],
  importDesc: [
    {
      required: true,
      message: '请输入导入描述',
    },
  ],
})

defineProps({
  applyList: {
    type: Object,
  },
})
const emit = defineEmits(['fetchList'])

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  await formEl.validate()

  try {
    await formEl.validate()
    const res = await $http.ajax({
      // headers: {
      //   'Content-Type': 'multipart/form-data',
      // },
      url: '/nbi/anno/importManage/newTask',
      method: 'POST',
      data: form,
    })
    if (res.code === -1) {
      ElMessage({
        message: res.msg || '网络异常，请重试~',
        type: 'warning',
      })
    } else {
      // ElMessage({
      //   message: '操作成功~',
      //   type: 'success',
      // })
      emit('fetchList')
      dialogFormVisible.value = false
    }
  } catch (error) {
    console.log(error)
  }
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  dialogFormVisible.value = false
}
</script>
<style lang="less" scoped>
.sampleDialog {
  .el-button--text {
    margin-right: 15px;
  }

  .el-select {
    width: 300px;
  }

  .el-input {
    width: 300px;
  }

  .dialog-footer button:first-child {
    margin-right: 10px;
  }
}
</style>
