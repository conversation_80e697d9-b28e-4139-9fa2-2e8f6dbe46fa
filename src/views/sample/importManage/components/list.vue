<template>
  <div class="flex-start">
    <div class="title">数据导入记录</div>
    <div class="total">
      累计导入
      <span class="count id-text">{{ importTotal }}</span>
      条
    </div>
  </div>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column label="记录ID">
      <template #default="scope">
        <div>
          <span class="id-text" @click="handleCheck(scope.$index, scope.row)">{{
            scope.row.taskIdDisplay
          }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="执行状态">
      <template #default="scope">
        <div>
          <span :class="[colorObj[scope.row.taskStatus]]">
            {{ scope.row.taskStatusSTr }}</span
          >
        </div>
      </template>
    </el-table-column>
    <el-table-column label="所属应用" width="120">
      <template #default="scope">
        {{
          (applyList || []).find((e) => e.key === scope.row.appId)?.value || ''
        }}
      </template>
    </el-table-column>
    <el-table-column label="导入批次">
      <template #default="scope">
        {{ scope.row.importBatch }}
      </template>
    </el-table-column>
    <el-table-column label="导入条数">
      <template #default="scope">
        {{ scope.row.importCount }}
      </template>
    </el-table-column>
    <el-table-column label="样本类型">
      <template #default="scope">
        {{ scope.row.sampleTypeName }}
      </template>
    </el-table-column>
    <el-table-column label="提交时间">
      <template #default="scope">
        {{ scope.row.submitTime.split(' ')[0] }}
      </template>
    </el-table-column>
    <el-table-column label="创建人">
      <template #default="scope">
        {{ scope.row.creatorName }}
      </template>
    </el-table-column>
    <el-table-column label="导入描述" width="200">
      <template #default="scope">
        <el-popover effect="light" trigger="hover" placement="top" width="auto">
          <template #default>
            <div>{{ scope.row.importDesc }}</div>
          </template>
          <template #reference>
            <div class="overflow-ellipsis">
              {{ scope.row.importDesc }}
            </div>
          </template>
        </el-popover>
      </template>
    </el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <el-button
          v-if="store.permissionCode.includes('23.80.433.434.436')"
          type="text"
          @click="handleCheck(scope.$index, scope.row)"
          >查 看</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <div class="pagination">
    <el-pagination
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, prev, pager, next, jumper, sizes"
      :total="total"
      background="#4455FF"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>

  <ViewDrawer
    :dialogVisible="viewDialogVisible"
    :viewData="viewData"
    :applyList="applyList"
    @closeView="(_) => (viewDialogVisible = false)"
  />
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

import ViewDrawer from './viewDrawer.vue'

import { useUserStore } from '@/store/useUserStore'
const colorObj = {
  2: 'green',
  0: 'red',
  1: 'yellow',
  3: 'red',
}
const store = useUserStore()
//defineProps
const props = defineProps({
  listData: {
    type: Object,
  },

  applyList: {
    type: Array,
    default: () => {
      return []
    },
  },
})

watch(
  () => props.listData,
  () => {
    if (props.listData?.elements) {
      tableData.value = props.listData.elements
    }

    currentPage.value = props.listData?.currentPage
    total.value = props.listData?.total
    importTotal.value = props.listData?.value1
    pageSize.value = props.listData?.pageSize
  }
)

const emit = defineEmits(['getPage'])
const currentPage = ref(1)
const total = ref(0)
const importTotal = ref(0)
const pageSize = ref(10)
const viewDialogVisible = ref(false)
const tableData = ref([])
const viewData = ref()
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  emit('getPage', { page: currentPage.value, pageSize: pageSize.value })
}

function handleSizeChange(size) {
  console.log(pageSize.value, size, 333)
  currentPage.value = 1
  pageSize.value = size
  emit('getPage', { page: currentPage.value, pageSize: pageSize.value })
}

const handleCheck = (index: number, row: object) => {
  if (!store.permissionCode.includes('23.80.433.434.436')) {
    return
  }
  viewData.value = row
  viewDialogVisible.value = true
}
</script>

<style lang="less" scoped>
.id-text {
  color: #4455ff;
  cursor: pointer;
}

.pagination {
  display: flex;
  justify-content: end;
  margin-top: 40px;
}

.total {
  margin-left: 30px;
  font-size: 12px;
  font-weight: 500;
  color: #686464;
  margin-bottom: 10px;
}
</style>
