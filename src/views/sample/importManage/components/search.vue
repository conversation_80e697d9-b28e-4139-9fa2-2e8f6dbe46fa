<template>
  <el-form
    class="sampleSearch"
    label-suffix="："
    ref="ruleFormRef"
    :model="form"
  >
    <el-row :gutter="30">
      <el-col :span="8">
        <el-form-item label="所属应用">
          <el-select v-model="form.appId" placeholder="请选择">
            <el-option label="全部" :value="null" />
            <el-option
              v-for="(item, index) in applyList"
              :key="index"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="执行状态">
          <el-select v-model="form.taskStatus" placeholder="请选择">
            <el-option label="全部" :value="null" />
            <el-option label="未执行" value="0" />
            <el-option label="执行中" value="1" />
            <el-option label="成功" value="2" />
            <el-option label="失败" value="3" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="导入时间">
          <el-date-picker
            v-model="datePicker"
            @change="dateChange"
            type="daterange"
            value-format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="起始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="30">
      <el-col :span="8">
        <el-form-item label="导入批次">
          <el-select
            class="importBatches"
            v-model="form.importBatches"
            placeholder="请选择"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            :max-collapse-tags="3"
          >
            <!-- <el-option label="全部" :value="null" /> -->
            <el-option
              v-for="(item, index) in importList"
              :key="index"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="样本类型">
          <el-select
            class="importBatches"
            v-model="form.sampleType"
            placeholder="全部"
          >
            <el-option label="全部" :value="null" />
            <el-option
              v-for="item in SampleTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="search-btns">
      <el-form-item>
        <el-button @click="reset">重 置</el-button>
        <el-button type="primary" @click="onSubmit">搜 索</el-button>
        <!--        <ImportDialog :applyList="applyList" @fetchList="fetchList" />-->
        <CreateSampleSets :apply-list="applyList" @fetchList="fetchList" />
      </el-form-item>
    </div>
  </el-form>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'

import CreateSampleSets from '@/views/sample/importManage/components/createSampleSets.vue'

import { SampleTypeOptions } from '@/common/constants'

import type { FormInstance } from 'element-plus'

import { useEnumsStore } from '@/store/useEnumsStore'

const ruleFormRef = ref<FormInstance>()
const applyList = ref<{ value: string; key: string }[]>([])
const importList = ref<{ value: string; key: string }[]>([])
const datePicker = ref()
const enumsStore = useEnumsStore()
const form = ref({
  appId: null,
  taskStatus: null,
  taskDateTimeBegin: null,
  taskDateTimeEnd: null,
  importBatches: [],
  sampleType: null,
})

//defineEmits
const emit = defineEmits(['onSubmit', 'getEnums'])

onMounted(() => {
  Promise.all([
    enumsStore.fetchDropDownList(1),
    enumsStore.fetchDropDownList(7),
  ])
    .then((values) => {
      applyList.value = values[0] as []
      importList.value = values[1] as []
      emit('getEnums', {
        applyList: applyList.value,
        importList: importList.value,
      })

      if (localStorage.getItem('searchSampleData')) {
        const search = JSON.parse(
          localStorage.getItem('searchSampleData') as string
        )
        if (typeof search === 'object') {
          datePicker.value = [search.taskDateTimeBegin, search.taskDateTimeEnd]
          form.value = search
        }
      }
      emit('onSubmit', {
        ...form.value,
        importBatches: form.value.importBatches.join(','),
      })
    })
    .catch((err) => {
      console.log(err)
    })
})

function fetchList() {
  emit('onSubmit', {
    ...form.value,
    importBatches: form.value.importBatches.join(','),
  })
}

//dateChange
const dateChange = (val) => {
  if (val && val.length > 0) {
    form.value.taskDateTimeBegin = val[0]
    form.value.taskDateTimeEnd = val[1]
  } else {
    form.value.taskDateTimeBegin = null
    form.value.taskDateTimeEnd = null
  }
}

const onSubmit = () => {
  localStorage.setItem('searchSampleData', JSON.stringify(form.value))
  emit('onSubmit', {
    ...form.value,
    importBatches: form.value.importBatches.join(','),
  })
}
const reset = () => {
  datePicker.value = []
  form.value = {
    appId: null,
    taskStatus: null,
    taskDateTimeBegin: null,
    taskDateTimeEnd: null,
    importBatches: [],
    sampleType: null,
  }
  localStorage.setItem('searchSampleData', JSON.stringify(form.value))
  emit('onSubmit', form.value)
}
</script>
<style lang="less">
.sampleSearch {
  .el-select {
    width: 100%;
  }

  // .el-select__tags {
  //   height: 30px;
  //   overflow-x: hidden;
  //   overflow-y: scroll;
  // }

  .search-btns {
    display: flex;
    justify-content: right;

    .el-button {
      min-width: 85px;
    }
  }
}
</style>
