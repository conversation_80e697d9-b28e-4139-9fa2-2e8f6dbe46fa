<template>
  <el-drawer
    ref="drawerRef"
    :model-value="dialogVisible"
    title="导入批次明细"
    :before-close="handleClose"
    direction="rtl"
    class="drawers"
  >
    <el-descriptions
      :style="{ marginBottom: '120px' }"
      :column="2"
      title="导入信息"
    >
      <el-descriptions-item label="记录ID">{{
        form.taskIdDisplay
      }}</el-descriptions-item>
      <el-descriptions-item label="创建人">{{
        form.creatorName
      }}</el-descriptions-item>
      <el-descriptions-item label="所属应用">
        {{ applyList?.find((e) => e.key === form.appId)?.value || '' }}
      </el-descriptions-item>
      <el-descriptions-item label="导入批次">
        {{ form.importBatch }}
      </el-descriptions-item>
      <el-descriptions-item label="提交时间">
        {{ (form.submitTime || '').split(' ')[0] }}
      </el-descriptions-item>
      <el-descriptions-item label="导入方式">
        {{ form.importType === 1 ? '文件上传' : 'sql' }}
      </el-descriptions-item>
      <el-descriptions-item label="样本分类">
        {{ form.sampleTypeName }}
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions :column="2" title="执行结果">
      <el-descriptions-item label="执行状态">
        {{ form.taskStatusSTr }}
        <el-button
          type="primary"
          v-if="form.taskStatus === 3"
          class="btn"
          id="btn"
          @click="copyToClipboard"
          data-clipboard-action="copy"
          :data-clipboard-text="form.statusReason"
        >
          复制失败信息
        </el-button>
        <!-- <el-tag v-if="form.taskStatus === 3" class="mx-1" size="small">{{
          form.statusReason
        }}</el-tag> -->
      </el-descriptions-item>
      <el-descriptions-item label="导入条数">
        {{ form.importCount }}
      </el-descriptions-item>
      <el-descriptions-item label="整体耗时">
        {{ form.timeCost || 0 }}秒
      </el-descriptions-item>
      <!-- <el-descriptions-item label="导入描述">
        <el-input
          v-model="form.importDesc"
          :rows="5"
          disabled
          type="textarea"
        />
      </el-descriptions-item>
      <el-descriptions-item label="执行SQL">
        <el-input v-model="form.importSql" :rows="5" disabled type="textarea" />
      </el-descriptions-item> -->
    </el-descriptions>

    <el-descriptions class="descriptions-bottom" :column="1" title="">
      <el-descriptions-item label="导入描述">
        <el-input
          v-model="form.importDesc"
          :rows="8"
          disabled
          type="textarea"
        />
      </el-descriptions-item>
      <el-descriptions-item label="执行SQL">
        <el-input
          v-model="form.userImportSql"
          :rows="8"
          disabled
          type="textarea"
        />
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="样本明细示例">
      <el-descriptions-item>
        <div class="max-w-200 overflow-y-scroll">
          <el-table :data="tableData" border size="small" :max-height="300">
            <el-table-column
              v-for="(item, index) in columns"
              :key="index"
              :prop="item.prop"
              :label="item.label"
            >
              <template #default="{ row }">
                <div class="max-h-100px overflow-y-auto">
                  {{ row[item.prop] }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </el-drawer>
</template>

<script lang="ts" setup>
import { getCurrentInstance, nextTick, ref, watch } from 'vue'
import { ElDrawer, ElMessage } from 'element-plus'

import Clipboard from 'clipboard'

const $http = getCurrentInstance()?.appContext.config.globalProperties.$http
//defineProps  参数类型
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  viewData: {
    type: Object,
  },
  applyList: {
    type: Array,
    default: () => {
      return []
    },
  },
})

const columns = ref<{ prop: string; label: string }[]>([])
const tableData = ref([])

watch(
  () => props.dialogVisible,
  () => {
    if (props.dialogVisible) {
      columns.value = []
      getApplyById()
      getSampleExample()
    }
  }
)

async function getApplyById() {
  const res = await $http.ajax({
    url: '/nbi/anno/importManage/getOneTask',
    method: 'GET',
    params: { id: props.viewData?.id },
  })
  form.value = { ...form.value, ...res }
  nextTick(() => {
    if (document.getElementById('btn')) {
      clipboard.value = new Clipboard(document.getElementById('btn') as any)
    }
  })
  return res
}

const getSampleExample = async () => {
  const res = await $http.ajax({
    url: '/nbi/anno/importManage/getSomeSampleData',
    method: 'GET',
    params: {
      id: props.viewData?.id,
    },
  })

  if (res.length) {
    const keys = Object.keys(res[0])
    for (const key of keys) {
      columns.value.push({
        prop: key,
        label: key,
      })
    }

    tableData.value = res
  }
}

const copyToClipboard = () => {
  clipboard.value.on('success', (e) => {
    clipboard.value.destroy()
    ElMessage.success('复制成功')
    clipboard.value = new Clipboard(document.getElementById('btn') as any)
    e.clearSelection()
  })
}
//defineEmits 参数类型
const emit = defineEmits(['closeView'])

const loading = ref(false)
const clipboard = ref()
const form = ref({
  appId: null,
  applicationName: null,
  createTime: null,
  creatorDomain: null,
  creatorName: null,
  id: null,
  importBatch: null,
  importCount: null,
  importDesc: null,
  userImportSql: null,
  submitTime: null,
  taskIdDisplay: null,
  taskStatus: null,
  taskStatusSTr: null,
  timeCost: null,
  updateTime: null,
  statusReason: null,
  updatorDomain: null,
  importType: null,
  sampleType: null,
  sampleTypeName: null,
})

const drawerRef = ref<InstanceType<typeof ElDrawer>>()

const handleClose = (done) => {
  emit('closeView')
}
</script>

<style lang="less">
.drawers {
  width: 50% !important;
}

.descriptions-bottom {
  .el-descriptions__cell {
    display: flex;
  }

  .el-descriptions__content {
    flex-grow: 1;

    .el-textarea {
      width: 100%;
    }
  }
}
</style>
