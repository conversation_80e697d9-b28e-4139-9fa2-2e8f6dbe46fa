<template>
  <NotAuth v-if="useAuth()" />

  <template v-else>
    <div class="page-content search-content">
      <Search @onSubmit="onSubmit" @getEnums="getEnums" />
    </div>
    <div class="page-content">
      <SampleList
        ref="importListRef"
        @getPage="getPage"
        :applyList="applyList"
        :listData="listData"
      />
    </div>
  </template>
</template>
<script lang="ts" setup>
import { getCurrentInstance, onMounted, onUnmounted, ref } from 'vue'
import { ElMessage } from 'element-plus'

import SampleList from './components/list.vue'
import Search from './components/search.vue'
import NotAuth from '@/views/401.vue'

import { useAuth } from '@/hooks/useAuth'
const $http = getCurrentInstance()?.appContext.config.globalProperties.$http
const listData = ref<any>()
let importListRef = ref()
let timer = ref()

const searchData = ref({
  applicationId: null,
  taskStatus: null,
  taskDateTimeBegin: null,
  taskDateTimeEnd: null,
  importBatch: null,
  page: 1,
  size: 10,
})

const applyList = ref<{ value: string; key: string }[]>([])
const importList = ref<{ value: string; key: string }[]>([])
function getPage(pageData) {
  const { page, pageSize } = pageData
  searchData.value.page = page
  searchData.value.size = pageSize
  fetchData()
}

function getEnums(pageData) {
  applyList.value = pageData.applyList
  importList.value = pageData.importList
}
function onSubmit(data) {
  searchData.value = { ...searchData.value, ...data, page: 1, size: 10 }
  importListRef.value.pageSize = 10
  importListRef.value.currentPage = 1
  fetchData()
}

async function fetchData() {
  const res = await $http.ajax({
    url: '/nbi/anno/importManage/getTaskPage',
    method: 'GET',
    params: searchData.value,
  })
  if (!res.code) {
    // ElMessage({
    //   message: '操作成功~',
    //   type: 'success',
    // })
  } else {
    ElMessage({
      message: '网络异常，请重试~',
      type: 'warning',
    })
  }
  listData.value = res
  return res
}

onMounted(() => {
  console.log('mounted 1111111111111111')
  window.$SDK_ALL.sendPage({
    event_id: 'lantu_view',
    data: {
      page: 'importManage',
    },
  })

  window.$SDK_ALL.sendPageEvent({
    event_id: 'lantu_click', // 业务字段
    data: {
      button: 'importManage',
    },
  })
  timer.value = setInterval(() => {
    fetchData()
  }, 5000)
})
onUnmounted(() => {
  clearInterval(timer.value)
})
</script>
