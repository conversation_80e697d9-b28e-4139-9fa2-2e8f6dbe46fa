<template>
  <el-tabs
    v-model="that.activeName"
    :before-leave="beforeLeave"
    @tab-click="tabChange"
  >
    <el-tab-pane
      v-for="item in LabelTabs"
      :key="item.name"
      :label="item.label"
      :name="item.name"
      :disabled="item.disabled"
      ><br
    /></el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
import { reactive } from 'vue'
import { useRouter } from 'vue-router'

import { LabelTabs } from '@/common/constants'

const Router = useRouter()

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  value: {
    type: String,
    default: '',
  },
})

const that = reactive({
  activeName: props.value,
})

function tabChange(key) {
  window.$SDK_ALL.sendPageEvent({
    event_id: 'lantu_click',
    data: {
      button: 'annotation',
      card: key.props.name === '31' ? 'quality-list' : 'enum-list',
    },
  })
  window.$SDK_ALL.sendPage({
    event_id: 'lantu_view',
    data: {
      page: 'annotation',
      card: key.props.name === '31' ? 'quality-list' : 'enum-list',
    },
  })
}

function beforeLeave(paneName) {
  console.log(paneName)
  const { path } = LabelTabs.find((item) => item.name === paneName) || {}
  if (!path) return false
  Router.replace({
    path,
  })
  return false
}
</script>
