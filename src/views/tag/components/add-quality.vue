<template>
  <el-dialog
    v-model="dialogData.show"
    :title="dialogData.title"
    width="700"
    destroy-on-close
    :before-close="handleClose"
  >
    <div class="pl-20% pr-20%">
      <el-form ref="formRef" :model="form" label-width="100">
        <el-form-item label="所属应用:">
          <el-select v-model="form.appId" placeholder="全部状态" class="w-100%">
            <el-option
              v-for="item in data.appTypeList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属服务:">
          <el-select
            v-model="form.serviceId"
            placeholder="全部状态"
            class="w-100%"
          >
            <el-option
              v-for="item in data.serviceList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标注项:">
          <el-input v-model="form.tagName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="英文名:">
          <el-input v-model="form.tagNameEn" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="外部业务ID:">
          <el-input v-model="form.tagBusiId" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="对应值:">
          <el-select v-model="form.itemId" placeholder="请选择" class="w-100%">
            <el-option
              v-for="item in data.configTypeList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            >
              <el-tooltip
                class="box-item"
                effect="dark"
                :raw-content="true"
                :content="getEnumStr(item)"
                placement="top"
              >
                <div>{{ item.value }}</div>
              </el-tooltip>
              <!--              <div>{{ item.value }}</div>-->
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前状态:">
          <el-select
            v-model="form.status"
            placeholder="全部状态"
            class="w-100%"
          >
            <el-option
              v-for="item in TagStatus"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="规则描述:">
          <el-input
            v-model="form.tagDesc"
            :autosize="{ minRows: 3, maxRows: 5 }"
            type="textarea"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="典型样例:">
          <el-input
            v-model="form.typicalSample"
            :autosize="{ minRows: 3, maxRows: 5 }"
            type="textarea"
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submit"> 提交 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'

import { getDropDownList } from '@/api/common'
import { getItemsDropDownForCreate, newTag } from '@/api/tag'

import { TagStatus } from '@/common/constants'
import { getEnumStr } from '@/common/helper'
import {
  AxiosErrorResType,
  newTagReqType,
  SelectListItemType,
} from '@/common/interface'

import type { FormInstance } from 'element-plus'

import { useEnumsStore } from '@/store/useEnumsStore'

let formRef = ref<FormInstance>()

const enumsStore = useEnumsStore()
let dialogData = reactive({
  show: false,
  title: '新增标注项',
})

let data = reactive({
  configTypeList: [] as SelectListItemType[],
  appTypeList: [] as SelectListItemType[],
  serviceList: [] as SelectListItemType[],
})

let form = reactive({
  id: '',
  appId: '',
  serviceId: '',
  tagName: '',
  tagDesc: '',
  typicalSample: '',
  itemId: '',
  status: '',
  tagNameEn: '',
  tagBusiId: '',
})

defineExpose({ dialogData, initData })

const emit = defineEmits<{
  (e: 'change', status: boolean): void
  // (e: 'update', value: string): void
}>()

function initData(data) {
  if (data) {
    let {
      id,
      appId,
      serviceId,
      tagName,
      tagDesc,
      typicalSample,
      itemId,
      status,
      tagNameEn,
      tagBusiId,
    } = data
    // console.log(data)
    form.id = id
    form.appId = appId
    form.serviceId = serviceId === -1 ? '' : serviceId
    form.tagName = tagName
    form.tagDesc = tagDesc
    form.typicalSample = typicalSample
    form.itemId = itemId
    form.status = status
    form.tagNameEn = tagNameEn
    form.tagBusiId = tagBusiId
    dialogData.title = '编辑标注项'
  } else {
    dialogData.title = '新增标注项'
  }
  dialogData.show = true
  getItemsDropDownForCreateApi()
}

function handleClose() {
  dialogData.show = false
  form.id = ''
  form.appId = ''
  form.serviceId = ''
  form.tagName = ''
  form.tagDesc = ''
  form.typicalSample = ''
  form.itemId = ''
  form.status = ''
  form.tagNameEn = ''
  form.tagBusiId = ''
  formRef!.value!.resetFields()
}

async function submit() {
  await newTagApi()
}

async function newTagApi() {
  try {
    let res = await newTag({
      ...form,
    } as newTagReqType)
    if (res === 'OK') {
      emit('change', true)
      handleClose()
    } else {
      ElMessage.warning((res as AxiosErrorResType).msg)
    }
    console.log(res)
  } catch (e) {
    console.log(e)
  }
}

async function getItemsDropDownForCreateApi() {
  try {
    let res = await getItemsDropDownForCreate()
    console.log(res)
    data.configTypeList = res || []
  } catch (e) {
    console.log(e)
  }
}

async function getDropDownListApi() {
  try {
    let res = await getDropDownList(1)
    console.log(res)
    data.appTypeList = res || []
  } catch (e) {
    console.log(e)
  }
}

onMounted(async () => {
  getDropDownListApi()

  const res = await enumsStore.fetchDropDownList(4)
  data.serviceList = res
})
</script>
