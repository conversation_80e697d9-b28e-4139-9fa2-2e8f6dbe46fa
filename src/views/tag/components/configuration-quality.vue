<template>
  <el-dialog
    v-model="dialogData.show"
    title="新增枚举值"
    width="600"
    :before-close="handleClose"
  >
    <div class="pl-5% pr-5%">
      <div class="flex items-center mb-10px">
        <div class="w-100px text-right pr-10px">配置名称:</div>
        <el-input
          class="w-330px"
          v-model="form.itemName"
          placeholder="请输入"
        />
      </div>
      <div class="flex items-center mb-10px">
        <div class="w-100px text-right pr-10px">配置类型:</div>
        <el-select v-model="form.itemType" placeholder="请选择" class="w-330px">
          <el-option
            v-for="item in data.configTypeList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </div>
      <div
        v-if="![0, 1, 2, 9, ''].includes(form.itemType)"
        class="flex items-center mb-10px"
      >
        <div class="w-100px text-right pr-10px">关联接口:</div>
        <el-input
          class="w-330px"
          v-model="form.itemContent"
          placeholder="请输入"
        />
      </div>
      <template v-if="store.LIMITED_DROPDOWN.includes(form.itemType as number)">
        <div class="flex items-center border-b-1px border-#eee border-solid">
          <div class="w-100px mb-10px text-right pr-10px">配置项:</div>
        </div>
        <div
          v-for="(item, index) in form.config"
          :key="index"
          class="flex items-center mb-5px p-10px pl-50px"
        >
          <div class="m-5px color-#999">Name:</div>
          <el-input class="w-139px" v-model="item.value" placeholder="请输入" />
          <div class="color-#999 m-5px">Code:</div>
          <el-input class="w-139px" v-model="item.key" placeholder="请输入" />
          <el-button
            @click="delConfig({ ...item, index })"
            link
            class="ml-10px"
            :disabled="form.config.length <= 1"
          >
            <el-icon size="16" color="#409eff"><IEpDelete /></el-icon>
          </el-button>
          <el-button
            link
            class="ml-10px"
            v-if="form.config.length - 1 === index"
            @click="addConfig"
          >
            <el-icon size="16" color="#409eff"><IEpPlus /></el-icon>
          </el-button>
        </div>
      </template>
      <div class="flex mb-10px items-start">
        <div class="w-100px text-right pr-10px pt-5px">描述:</div>
        <el-input
          :rows="2"
          type="textarea"
          class="w-330px"
          v-model="form.itemContentAbbr"
          placeholder="请输入"
        />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submit"> 提交 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'

import { getDropDownList } from '@/api/common'
import { newItem } from '@/api/tag'

import { AxiosErrorResType, SelectListItemType } from '@/common/interface'

import { useCommonStore } from '@/store/useCommonStore'

const store = useCommonStore()
console.log(store.INTERFACE)

let dialogData = reactive({
  show: false,
})

let data = reactive({
  configTypeList: [] as SelectListItemType[],
})

let form = reactive({
  itemName: '',
  itemType: '' as number | '',
  itemContent: '',
  itemContentAbbr: '',
  config: [
    {
      key: '',
      value: '',
    },
  ],
})

defineExpose({ dialogData })

const emit = defineEmits<{
  (e: 'change', status: boolean): void
}>()

function addConfig() {
  form.config.push({
    key: '',
    value: '',
  })
}

function delConfig({ index }) {
  console.log(index)
  form.config.splice(index, 1)
}

function handleClose() {
  dialogData.show = false
  form.itemName = ''
  form.itemType = ''
  form.itemContent = ''
  form.config = [
    {
      key: '',
      value: '',
    },
  ]
}

async function submit() {
  await newItemApi()
}

async function newItemApi() {
  let { itemType, itemContent, itemName, itemContentAbbr, config } = form
  try {
    let res = await newItem({
      itemType: itemType,
      itemName,
      itemContentAbbr,
      itemContent: store.LIMITED_DROPDOWN.includes(itemType as number)
        ? JSON.stringify(config)
        : itemContent,
    })
    if (res === 'OK') {
      handleClose()
      emit('change', true)
    } else {
      ElMessage.warning((res as AxiosErrorResType).msg)
    }
    console.log(res)
  } catch (e) {
    console.log(e)
  }
}

async function getDropDownListApi() {
  try {
    let res = await getDropDownList(8)
    console.log(res)
    data.configTypeList = res
  } catch (e) {
    console.log(e)
  }
}

onMounted(async () => {
  getDropDownListApi()
})
</script>
