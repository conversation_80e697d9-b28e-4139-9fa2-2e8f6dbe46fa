<template>
  <div class="page-content search-content label-content">
    <!-- <PageTabs value="32" /> -->

    <NotAuth v-if="useAuth()" />
    <div v-else class="pb-20px">
      <el-form
        @submit.prevent
        :inline="true"
        :model="form"
        class="demo-form-inline"
      >
        <el-form-item label="质检项:">
          <el-input
            v-model="form.keyword"
            clearable
            @keyup.enter="query"
            placeholder="枚举值名称模糊搜索"
          />
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" @click="query">查询</el-button>
          <el-button @click="reset">重置</el-button>
          <el-button type="success" @click="toConQuality">新增枚举值</el-button>
        </el-form-item>
      </el-form>
      <!--      <div class="flex items-center mb-10px">-->
      <!--        <el-button type="success" @click="toConQuality">新增枚举值</el-button>-->
      <!--      </div>-->
      <el-table :data="data.tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="100" />
        <el-table-column prop="itemName" label="枚举值" />
        <el-table-column prop="itemContent" label="枚举值内容" width="160" />
        <el-table-column prop="itemContentAbbr" label="描述" />
        <el-table-column prop="creatorName" label="创建人" />
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="scope">
            {{ dayjs(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" prop="id" label="操作" width="100">
          <template #default="scope">
            <el-popconfirm
              width="260"
              confirm-button-text="确认"
              confirm-button-type="danger"
              title="操作后无法恢复，是否确认删除？"
              @confirm="confirmEvent(scope.row)"
            >
              <template #reference>
                <el-button link type="danger" @click="del(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-popconfirm>
            <!--            <el-button link type="primary" @click="toConQuality(scope.row)"-->
            <!--              >对应值配置</el-button-->
            <!--            >-->
          </template>
        </el-table-column>
      </el-table>
      <div class="flex flex-end mt-20px">
        <el-pagination
          background
          v-model:current-page="pag.page"
          v-model:page-size="pag.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, jumper, sizes"
          :total="pag.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
  <ConfigurationQuality @change="onQualityChange" ref="conQualityRef" />
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

import NotAuth from '@/views/401.vue'
import PageTabs from '@/views/tag/components/PageTabs.vue'
import ConfigurationQuality from '@/views/tag/components/configuration-quality.vue'

import { deleteItem, getItemsPage } from '@/api/tag'

import type {
  SelectListItemType,
  TagsListResType,
  TagsPageListQueryType,
} from '@/common/interface'

import { useAuth } from '@/hooks/useAuth'

const conQualityRef = ref()

const data = reactive({
  sampleBusiId: '',
  annoStatusName: '',
  appTypeList: [] as SelectListItemType[],
  // serviceList: [] as SelectListItemType[],
  qualityList: [] as SelectListItemType[],
  tableData: [] as TagsListResType[],
})

const form = reactive({
  applicationId: '',
  serviceTypeId: '',
  itemId: '',
  keyword: '',
})

const pag = reactive({
  page: 1,
  size: 10,
  total: 0,
})

function toConQuality() {
  // console.log(taskAllocationRef.value)
  conQualityRef.value.dialogData.show = true
}

function onQualityChange(status) {
  console.log(status)
  query()
}

function query() {
  pag.page = 1
  getTagsPageApi()
}

function handleSizeChange(size) {
  console.log(size)
  query()
}

function handleCurrentChange(page) {
  console.log(page)
  getTagsPageApi()
}

function reset() {
  pag.page = 1
  form.keyword = ''
  getTagsPageApi()
}

async function getTagsPageApi() {
  try {
    localStorage.setItem('searchEnumData', JSON.stringify(form))
    const { pageSize, currentPage, total, elements } = await getItemsPage({
      ...pag,
      ...form,
    } as TagsPageListQueryType)
    console.log({ pageSize, currentPage, total, elements })
    pag.total = total
    data.tableData = elements
  } catch (e) {
    console.log(e)
  }
}

function del({ id }) {
  console.log(id)
}

async function confirmEvent({ id }) {
  console.log('confirmEvent', id)
  try {
    const res = await deleteItem({ itemId: id })
    console.log(res)
    if (res?.code) {
      ElMessage({
        message: '已绑定标注项，不可删除',
        type: 'warning',
      })
    } else {
      ElMessage({
        message: '删除成功~',
        type: 'success',
      })
      pag.page = 1
      getTagsPageApi()
    }
  } catch (e) {
    console.log(e)
  }
}

onMounted(async () => {
  if (localStorage.getItem('searchEnumData')) {
    const search = JSON.parse(localStorage.getItem('searchEnumData') as string)
    if (typeof search === 'object') {
      form.keyword = search.keyword
    }
  }
  getTagsPageApi()
})
</script>
