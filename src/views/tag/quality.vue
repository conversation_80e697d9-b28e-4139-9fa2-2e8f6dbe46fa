<template>
  <div class="page-content search-content label-content">
    <!-- <PageTabs value="31" /> -->

    <NotAuth v-if="useAuth()" />
    <div v-else class="pb-20px">
      <el-form :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="标注项:">
          <el-input
            v-model="form.keyword"
            clearable
            @keyup.enter="query"
            placeholder="标注项名称模糊搜索"
          />
        </el-form-item>
        <el-form-item label="对应值:">
          <el-select
            v-model="form.itemId"
            class="120px"
            style="width: 190px"
            clearable
            placeholder="全部"
          >
            <el-option
              v-for="item in data.qualityList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="所属应用:">
          <el-select
            v-model="form.appId"
            style="width: 190px"
            clearable
            placeholder="全部"
          >
            <el-option
              v-for="item in data.appTypeList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属服务:">
          <el-select
            v-model="form.serviceId"
            style="width: 190px"
            clearable
            placeholder="全部"
          >
            <el-option
              v-for="item in data.serviceList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" @click="query">查询</el-button>
          <el-button @click="reset">重置</el-button>
          <el-button type="success" @click="toAddQuality(null)"
            >新增标注项</el-button
          >
        </el-form-item>
      </el-form>
      <el-table :data="data.tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="100" />
        <el-table-column prop="tagName" label="标注项" />
        <el-table-column prop="tagNameEn" label="英文名" />
        <el-table-column prop="tagBusiId" label="标注项ID" width="100" />
        <el-table-column prop="itemNameStr" label="对应值">
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :raw-content="true"
              :content="getEnumStr(scope.row)"
              placement="top"
            >
              {{ scope.row.itemNameStr }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="applicationName" label="所属应用" />
        <el-table-column prop="serviceName" label="所属服务" />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            {{ TagStatusEnum[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column prop="creatorName" label="创建人" />
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="scope">
            {{ dayjs(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" prop="id" label="操作" width="100">
          <template #default="scope">
            <el-button link type="primary" @click="toAddQuality(scope.row)"
              >编辑</el-button
            >
            <!--            <el-button link type="primary" @click="toConQuality(scope.row)"-->
            <!--              >对应值配置</el-button-->
            <!--            >-->
          </template>
        </el-table-column>
      </el-table>
      <div class="flex flex-end mt-20px">
        <el-pagination
          background
          v-model:current-page="pag.page"
          v-model:page-size="pag.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, jumper, sizes"
          :total="pag.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
  <AddQuality @change="onQualityChange" ref="addQualityRef" />
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { onMounted, reactive, ref } from 'vue'

import NotAuth from '@/views/401.vue'
import PageTabs from '@/views/tag/components/PageTabs.vue'
import AddQuality from '@/views/tag/components/add-quality.vue'

import { getDropDownList } from '@/api/common'
import { getItemsDropDownForCreate, getTagsPage } from '@/api/tag'

import { TagStatusEnum } from '@/common/constants'
import { getEnumStr } from '@/common/helper'
import type {
  SelectListItemType,
  TagsListResType,
  TagsPageListQueryType,
} from '@/common/interface'

import { useAuth } from '@/hooks/useAuth'

import { useEnumsStore } from '@/store/useEnumsStore'

const enumsStore = useEnumsStore()
const addQualityRef = ref()

const data = reactive({
  sampleBusiId: '',
  annoStatusName: '',
  appTypeList: [] as SelectListItemType[],
  serviceList: [] as SelectListItemType[],
  qualityList: [] as SelectListItemType[],
  tableData: [] as TagsListResType[],
})

const form = reactive({
  appId: '',
  serviceId: '',
  itemId: '',
  keyword: '',
})

const pag = reactive({
  page: 1,
  size: 10,
  total: 0,
})

function toAddQuality(row) {
  console.log(row)
  // console.log(taskAllocationRef.value)
  addQualityRef.value.initData(row)
}

function onQualityChange(status) {
  console.log(status)
  getTagsPageApi()
}

function query() {
  pag.page = 1
  getTagsPageApi()
}

function handleSizeChange(size) {
  console.log(size)
  query()
}

function handleCurrentChange(page) {
  console.log(page)
  getTagsPageApi()
}

function reset() {
  pag.page = 1
  form.appId = ''
  form.serviceId = ''
  form.itemId = ''
  form.keyword = ''
  getTagsPageApi()
}

async function getTagsPageApi() {
  try {
    localStorage.setItem('searchQualityData', JSON.stringify(form))
    const { pageSize, currentPage, total, elements } = await getTagsPage({
      page: pag.page,
      size: pag.size,
      ...form,
    } as TagsPageListQueryType)
    console.log({ pageSize, currentPage, total, elements })
    pag.total = total
    data.tableData = elements
  } catch (e) {
    console.log(e)
  }
}

async function getDropDownListApi() {
  try {
    const res = await getDropDownList(1)
    console.log(res)
    data.appTypeList = res
  } catch (e) {
    console.log(e)
  }
}

async function getItemsDropDownForCreateApi() {
  try {
    const res = await getItemsDropDownForCreate()
    console.log(res)
    data.qualityList = res
  } catch (e) {
    console.log(e)
  }
}

onMounted(async () => {
  if (localStorage.getItem('searchQualityData')) {
    const search = JSON.parse(
      localStorage.getItem('searchQualityData') as string
    )
    if (typeof search === 'object') {
      form.appId = search.appId
      form.serviceId = search.serviceId
      form.itemId = search.itemId
      form.keyword = search.keyword
    }
  }
  getTagsPageApi()
  getDropDownListApi()
  getItemsDropDownForCreateApi()
  data.serviceList = await enumsStore.fetchDropDownList(4)
})
</script>
