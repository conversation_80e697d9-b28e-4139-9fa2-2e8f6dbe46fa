const { argv } = process
const desc = (argv.length > 4 && argv.slice(-1)[0]) || `feat：crm 审批`

module.exports = {
  shell: [
    'git status',
    'git add .',
    `git commit -m "${desc}"`,
    'git pull origin master',
    'git push origin master',
    // 'git pull origin test',
    // 'git push origin test',
    // 'git merge --no-ff -m "feat/question-v2.0 merge into test" feat/question-v2.0',
    // 'git pull origin feat/crm-v2.0',
    // 'git push origin test',
    // 'git checkout feat/question-v2.0',
    // 'npm run push',
  ],
}
