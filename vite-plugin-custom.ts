import { writeFileSync } from 'node:fs'
import { join } from 'node:path'
import colors from 'picocolors'
import type { PluginOption, ViteDevServer } from 'vite'
interface ChunkType {
  code: any
  fileName: string
}

export const ViteMicroApp = (): PluginOption => {
  let basePath = ''
  return {
    name: 'vite:micro-app',
    apply: 'build',
    configResolved({ base, build }) {
      basePath = `${base}${build.assetsDir}/`
    },
    writeBundle(options, bundle) {
      for (const chunkName in bundle) {
        if (Object.prototype.hasOwnProperty.call(bundle, chunkName)) {
          const chunk = bundle[chunkName] as ChunkType
          if (chunk.fileName && chunk.fileName.endsWith('.js')) {
            chunk.code = chunk.code.replace(
              /(from|import\()(\s*['"])(\.\.?\/)/g,
              (all, $1, $2, $3) => {
                return all.replace($3, new URL($3, basePath))
              }
            )
            const fullPath = join(options.dir, chunk.fileName)
            writeFileSync(fullPath, chunk.code)
          }
        }
      }
    },
  }
}

export const vitePluginVueMonitor = (): PluginOption => {
  return {
    name: 'vite-plugin-vue-monitor',
    apply: 'serve',
    enforce: 'pre',
    configureServer(server: ViteDevServer) {
      const print = server.printUrls
      server.printUrls = () => {
        const network = server.resolvedUrls?.network[0]
        const local = server.resolvedUrls?.local[0]
        if (!network && !local) {
          console.log(
            colors.red(
              '获取IP地址失败,请检查vite.config.ts文件中server.host配置是否正确!\n'
            )
          )
        } else {
          console.log(
            // colors.green(
            //   '                          _ooOoo_                               \n' +
            //     '                         o8888888o                              \n' +
            //     '                         88" . "88                              \n' +
            //     '                         (| ^_^ |)                              \n' +
            //     '                         O\\  =  /O                              \n' +
            //     "                      ____/`---'\\____                           \n" +
            //     "                    .'  \\\\|     |  `.                         \n" +
            //     '                   /  \\\\|||  :  |||//  \\                        \n' +
            //     '                  /  _||||| -:- |||||-  \\                       \n' +
            //     '                  |   | \\\\\\  -  / |   |                       \n' +
            //     "                  | \\_|  ''\\---/''  |   |                       \n" +
            //     '                  \\  .-\\__  `-`  ___/-. /                       \n' +
            //     "                ___`. .'  /--.--\\  `. . ___                     \n" +
            //     '              ."" \'<  `.___\\_<|>_/___.\'  >\'"".                  \n' +
            //     '            | | :  `- \\`.;`\\ _ /`;.`/ - ` : | |                 \n' +
            //     '            \\  \\ `-.   \\_ __\\ /__ _/   .-` /  /                 \n' +
            //     "      ========`-.____`-.___\\_____/___.-`____.-'========         \n" +
            //     "                           `=---='                              \n" +
            //     '      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^        \n' +
            //     '            佛祖保佑                 永无BUG                    \n' +
            //     '                                             \n'
            // )
            colors.green(`
 ___      ___ ___  _________  _______      
|\\  \\    /  /|\\  \\|\\___   ___\\\\  ___ \\     
\\ \\  \\  /  / | \\  \\|___ \\  \\_\\ \\   __/|    
 \\ \\  \\/  / / \\ \\  \\   \\ \\  \\ \\ \\  \\_|/__  
  \\ \\    / /   \\ \\  \\   \\ \\  \\ \\ \\  \\_|\\ \\ 
   \\ \\__/ /     \\ \\__\\   \\ \\__\\ \\ \\_______\\
    \\|__|/       \\|__|    \\|__|  \\|_______|
              `)
          )
          print()
        }
      }
    },
  }
}
