import { proxy } from './vite.proxy.config'
import vue from '@vitejs/plugin-vue'
import path from 'node:path'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import IconsResolver from 'unplugin-icons/resolver'
import Icons from 'unplugin-icons/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig } from 'vite'
import viteCompression from 'vite-plugin-compression'
import { h, insertHtml } from 'vite-plugin-insert-html'
import Inspect from 'vite-plugin-inspect'

const { npm_lifecycle_event, NODE_ENV } = process.env
const isProd = NODE_ENV === 'production'
function getBase() {
  let basePath = ''
  if (NODE_ENV === 'development') {
    basePath = ''
  } else {
    const prefix = npm_lifecycle_event === 'build:prod' ? 'sto' : 'pre'
    basePath = `https://${prefix}.bdp.yiche.com`
  }
  return basePath + '/annotation/'
}

export default defineConfig({
  base: getBase(),
  resolve: {
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    // 在导入模块时，如果模块路径以 / 开头，则会尝试在下面这些目录中查找该模块
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@img': path.resolve(__dirname, './src/assets/img'),
    },
  },
  // assetsInclude: ['./src/hg/web-highlighter.min.js'],
  css: {
    preprocessorOptions: {
      scss: {
        // additionalData: `@use "@/styles/element/index.scss" as *;`,
      },
    },
  },
  // optimizeDeps: {
  //   include: ['./src/hg/web-highlighter.min.js'],
  // },
  plugins: [
    insertHtml({
      head: [h('script', { src: './web-highlighter.min.js' })],
    }),
    vue(),
    AutoImport({
      // Auto import functions from Vue, e.g. ref, reactive, toRef...
      // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
      imports: ['vue'],
      // Auto import functions from Element Plus, e.g. ElMessage, ElMessageBox... (with style)
      // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox... (带样式)
      resolvers: [
        ElementPlusResolver(),
        // Auto import icon components
        // 自动导入图标组件
        IconsResolver({
          prefix: 'Icon',
        }),
      ],
      // dts: path.resolve(pathSrc, 'auto-imports.d.ts'),
    }),
    Components({
      resolvers: [
        // Auto register icon components
        // 自动注册图标组件
        IconsResolver({
          enabledCollections: ['ep'],
        }),
        // Auto register Element Plus components
        // 自动导入 Element Plus 组件
        ElementPlusResolver(),
      ],
    }),
    Icons({
      autoInstall: true,
    }),
    Inspect(),
    UnoCSS(),
    // eslintPlugin({
    //   include: [
    //     // 'src/**/*.js',
    //     'src/**/*.vue',
    //     // 'src/*.js',
    //     'src/**/*.ts',
    //     'src/*.ts',
    //     'src/*.vue',
    //   ],
    // }),
    viteCompression({
      threshold: 10240, // 对大于 1mb 的文件进行压缩
    }),
    // vitePluginVueMonitor(),
  ],
  esbuild: {
    // drop: isProd ? ['console', 'debugger'] : [],
  },
  build: {
    assetsDir: 'assets/js',
    // commonjsOptions: {
    //   include: 'src/hg/web-highlighter.min.js',
    //   transformMixedEsModules: true,
    // },
    sourcemap: false,
    // minify: 'terser',
    assetsInlineLimit: 4096,
    chunkSizeWarningLimit: 300,
    reportCompressedSize: false,
    rollupOptions: {
      external: ['APlayer'],
      output: {
        // 最小化拆分包
        manualChunks(id) {
          if (id.includes('node_modules')) {
            return id
              .toString()
              .split('node_modules/')[1]
              .split('/')[0]
              .toString()
          }
        },
        chunkFileNames: 'assets/js/[name].[hash].js', // 用于命名代码拆分时创建的共享块的输出命名，[name]表示文件名,[hash]表示该文件内容hash值
      },
    },
    // terserOptions: {
    //   compress: {
    //     drop_console: true,
    //     drop_debugger: true,
    //   },
    // },
  },
  server: {
    hmr: {
      overlay: false,
    },
    // open: true,
    port: 1024,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    proxy,
  },
})
