export const proxy = {
  '/Employee': {
    target: 'http://api.hr.oa.bitauto.com/Employee/',
    changeOrigin: true,
    ws: true,
    rewrite: (path) => path.replace(/^\/Employee/, ''),
  },
  '/plus': {
    target: 'http://yiplus.yiche.com',
    changeOrigin: true,
    ws: true,
    rewrite: (path) => path.replace(/^\/plus/, 'nbi'),
  },
  '/nbi': {
    target: 'http://tnorbi.yiche.com/nbi/',
    changeOrigin: true,
    ws: true,
    rewrite: (path) => path.replace(/^\/nbi/, ''),
  },
  '/indicatorsLib': {
    target: 'http://pre.bdp.yiche.com/',
    changeOrigin: true,
    ws: true,
  },
  '/ston': {
    target: 'http://pre.bdp.yiche.com/',
    changeOrigin: true,
    ws: true,
  },
  '/jobcenter': {
    target: 'http://pre.bdp.yiche.com/',
    changeOrigin: true,
    ws: true,
  },
  '/allemployeeapi': {
    target: 'http://sysapp.bitauto.com/',
    changeOrigin: true,
    ws: true,
  },
  '/restapi': {
    target: 'http://restapi.amap.com/',
    changeOrigin: true,
    ws: true,
  },
  '/robot-api': {
    target: 'http://tnorbi.yiche.com/', // 东文开发环境
    changeOrigin: true,
    ws: true,
  },
  '/stonCommunity': {
    target: 'http://pre.bdp.yiche.com',
    changeOrigin: true,
    ws: true,
  },
  '/v3/weather/weatherInfo': {
    target: 'http://pre.bdp.yiche.com/',
    changeOrigin: true,
    ws: true,
  },
}
